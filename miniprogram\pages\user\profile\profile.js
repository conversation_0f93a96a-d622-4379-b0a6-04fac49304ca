const app = getApp();
const { user, auth } = require('../../../utils/api');
const util = require('../../../utils/util');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 表单数据
    formData: {
      nickname: '',
      gender: 'secret',
      age: '',
      occupation: '',
      region: '',
      avatar: ''
    },
    // 错误信息
    errors: {},
    // 提交状态
    isSubmitting: false,
    // 原始用户信息（用于对比是否有修改）
    originalUserInfo: null
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.initializeUserData();
  },

  /**
   * 初始化用户数据
   */
  initializeUserData: function() {
    const userInfo = app.globalData.userInfo;
    
    if (userInfo) {
      const formData = {
        nickname: userInfo.nickname || '',
        gender: userInfo.gender || 'secret',
        age: userInfo.age ? userInfo.age.toString() : '',
        occupation: userInfo.occupation || '',
        region: userInfo.region || '',
        avatar: userInfo.avatar || ''
      };

      this.setData({
        formData: formData,
        originalUserInfo: { ...userInfo }
      });
    } else {
      // 如果没有用户信息，重新获取
      this.getCurrentUserInfo();
    }
  },

  /**
   * 获取当前用户信息
   */
  getCurrentUserInfo: function() {
    util.showLoading('加载中...');
    
    auth.getCurrentUser()
      .then(res => {
        util.hideLoading();
        
        const userInfo = res.data;
        const formData = {
          nickname: userInfo.nickname || '',
          gender: userInfo.gender || 'secret',
          age: userInfo.age ? userInfo.age.toString() : '',
          occupation: userInfo.occupation || '',
          region: userInfo.region || '',
          avatar: userInfo.avatar || ''
        };

        this.setData({
          formData: formData,
          originalUserInfo: { ...userInfo }
        });

        // 更新全局用户信息
        app.globalData.userInfo = userInfo;
        wx.setStorageSync('userInfo', userInfo);
      })
      .catch(err => {
        util.hideLoading();
        console.error('获取用户信息失败:', err);
        util.showToast('获取用户信息失败');
        
        // 返回上一页
        wx.navigateBack();
      });
  },

  /**
   * 输入框内容变化处理
   */
  onInputChange: function(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    
    this.setData({
      [`formData.${field}`]: value,
      [`errors.${field}`]: '' // 清除该字段的错误信息
    });
  },

  /**
   * 性别文字选择处理
   */
  onGenderSelect: function(e) {
    const { gender } = e.currentTarget.dataset;
    
    this.setData({
      'formData.gender': gender
    });
  },

  /**
   * 表单验证
   */
  validateForm: function() {
    const { formData } = this.data;
    const errors = {};

    // 验证昵称
    if (!formData.nickname.trim()) {
      errors.nickname = '昵称不能为空';
    } else if (formData.nickname.trim().length < 2) {
      errors.nickname = '昵称至少2个字符';
    } else if (formData.nickname.trim().length > 20) {
      errors.nickname = '昵称不能超过20个字符';
    }

    // 验证年龄
    if (formData.age) {
      const age = Number(formData.age);
      if (isNaN(age) || age < 0 || age > 120) {
        errors.age = '年龄应在0-120之间';
      }
    }

    // 验证职业
    if (formData.occupation && formData.occupation.length > 50) {
      errors.occupation = '职业描述不能超过50个字符';
    }

    // 验证地区
    if (formData.region && formData.region.length > 50) {
      errors.region = '地区信息不能超过50个字符';
    }

    this.setData({ errors });
    return Object.keys(errors).length === 0;
  },

  /**
   * 检查数据是否有修改
   */
  hasDataChanged: function() {
    const { formData, originalUserInfo } = this.data;
    
    if (!originalUserInfo) return true;

    // 对比各个字段
    const changes = {
      nickname: formData.nickname.trim() !== (originalUserInfo.nickname || ''),
      gender: formData.gender !== (originalUserInfo.gender || 'secret'),
      age: formData.age !== (originalUserInfo.age ? originalUserInfo.age.toString() : ''),
      occupation: formData.occupation.trim() !== (originalUserInfo.occupation || ''),
      region: formData.region.trim() !== (originalUserInfo.region || '')
    };

    return Object.values(changes).some(changed => changed);
  },

  /**
   * 构建更新数据
   */
  buildUpdateData: function() {
    const { formData } = this.data;
    const updateData = {};

    // 昵称是必填项
    updateData.nickname = formData.nickname.trim();
    
    // 其他字段
    updateData.gender = formData.gender;
    
    if (formData.age) {
      updateData.age = Number(formData.age);
    }
    
    if (formData.occupation.trim()) {
      updateData.occupation = formData.occupation.trim();
    }
    
    if (formData.region.trim()) {
      updateData.region = formData.region.trim();
    }

    return updateData;
  },

  /**
   * 表单提交处理
   */
  onSubmit: function(e) {
    // 防止重复提交
    if (this.data.isSubmitting) {
      return;
    }

    // 表单验证
    if (!this.validateForm()) {
      util.showToast('请检查输入内容');
      return;
    }

    // 检查是否有修改
    if (!this.hasDataChanged()) {
      util.showToast('没有修改任何内容');
      return;
    }

    // 开始提交
    this.setData({ isSubmitting: true });
    util.showLoading('保存中...');

    const updateData = this.buildUpdateData();
    console.log('更新用户资料数据:', updateData);

    user.updateProfile(updateData)
      .then(res => {
        util.hideLoading();
        this.setData({ isSubmitting: false });
        
        util.showToast('保存成功', 'success');
        
        // 更新全局用户信息
        const updatedUserInfo = { ...this.data.originalUserInfo, ...updateData };
        app.globalData.userInfo = updatedUserInfo;
        wx.setStorageSync('userInfo', updatedUserInfo);
        
        // 延迟返回上一页，让用户看到成功提示
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      })
      .catch(err => {
        util.hideLoading();
        this.setData({ isSubmitting: false });
        
        console.error('更新用户资料失败:', err);
        util.showToast(err.message || '保存失败，请稍后再试');
      });
  }
}); 