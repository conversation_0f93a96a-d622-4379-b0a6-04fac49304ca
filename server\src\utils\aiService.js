const axios = require('axios');
const HUGGINGFACE_API_KEY = process.env.HUGGINGFACE_API_KEY;
const AZURE_OPENAI_API_KEY = process.env.AZURE_OPENAI_API_KEY;
const AZURE_OPENAI_ENDPOINT = process.env.AZURE_OPENAI_ENDPOINT;
const AZURE_OPENAI_DEPLOYMENT = process.env.AZURE_OPENAI_DEPLOYMENT;
const AZURE_OPENAI_API_VERSION = process.env.AZURE_OPENAI_API_VERSION || '2024-02-15-preview';

// DeepSeek配置
const DEEPSEEK_API_KEY = process.env.DEEPSEEK_API_KEY;
const DEEPSEEK_API_BASE = process.env.DEEPSEEK_API_BASE || 'https://api.deepseek.com';
const DEEPSEEK_MODEL = process.env.DEEPSEEK_MODEL || 'deepseek-chat';

// 默认的AI配置
const DEFAULT_AI_CONFIG = {
  temperature: 0.7,
  maxTokens: 1500,
  timeout: 120000
};

/**
 * 调用HuggingFace API生成文本
 * @param {Object} input 输入数据
 * @param {String} model 模型名称
 * @returns {Promise<String>} 生成的文本
 */
const generateTextWithHuggingFace = async (input, model = 'gpt2') => {
  try {
    const API_URL = `https://api-inference.huggingface.co/models/${model}`;
    
    const response = await axios({
      url: API_URL,
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${HUGGINGFACE_API_KEY}`,
        'Content-Type': 'application/json',
      },
      data: JSON.stringify(input),
      timeout: 60000, // 60秒超时
    });

    if (response.status !== 200) {
      throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
    }

    return response.data[0].generated_text;
  } catch (error) {
    console.error('HuggingFace API调用失败:', error);
    throw new Error('AI总结生成失败，请稍后重试');
  }
};

/**
 * 调用Azure OpenAI API生成文本
 * @param {String} userPrompt 用户输入提示
 * @param {String} systemPrompt 系统提示（可选）
 * @param {Object} config 配置选项（可选）
 * @returns {Promise<String>} 生成的文本
 */
const generateTextWithAzureOpenAI = async (userPrompt, systemPrompt = null, config = {}) => {
  try {
    // 合并配置
    const aiConfig = { ...DEFAULT_AI_CONFIG, ...config };
    
    // 确保endpoint没有结尾的斜杠
    const baseEndpoint = AZURE_OPENAI_ENDPOINT.endsWith('/') 
      ? AZURE_OPENAI_ENDPOINT.slice(0, -1) 
      : AZURE_OPENAI_ENDPOINT;
    
    // 使用成功的URL格式
    const url = `${baseEndpoint}/openai/deployments/${AZURE_OPENAI_DEPLOYMENT}/chat/completions?api-version=${AZURE_OPENAI_API_VERSION}`;
    
    console.log(`调用Azure OpenAI API: ${url}`);
    
    // 构建消息数组
    const messages = [];
    if (systemPrompt) {
      messages.push({ role: "system", content: systemPrompt });
    }
    messages.push({ role: "user", content: userPrompt });
    
    const response = await axios({
      url: url,
      method: 'POST',
      headers: {
        'api-key': AZURE_OPENAI_API_KEY,
        'Content-Type': 'application/json',
      },
      data: {
        model: AZURE_OPENAI_DEPLOYMENT,
        messages: messages,
        temperature: aiConfig.temperature,
        max_tokens: aiConfig.maxTokens
      },
      timeout: aiConfig.timeout,
    });
    
    if (response.status === 200) {
      console.log(`成功生成Azure AI总结`);
      return response.data.choices[0].message.content;
    } else {
      throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
    }
  } catch (error) {
    console.error('Azure OpenAI API调用失败:', error);
    let errorMessage = 'Azure AI总结生成失败，请稍后重试';
    
    if (error.response) {
      errorMessage += `(状态码: ${error.response.status}, 错误信息: ${JSON.stringify(error.response.data)})`;
    } else if (error.request) {
      errorMessage += `(请求错误: ${error.message})`;
    } else {
      errorMessage += `(错误: ${error.message})`;
    }
    
    throw new Error(errorMessage);
  }
};

/**
 * 调用DeepSeek API生成文本
 * @param {String} userPrompt 用户输入提示
 * @param {String} systemPrompt 系统提示（可选）
 * @param {Object} config 配置选项（可选）
 * @returns {Promise<String>} 生成的文本
 */
const generateTextWithDeepSeek = async (userPrompt, systemPrompt = null, config = {}) => {
  try {
    // 合并配置
    const aiConfig = { ...DEFAULT_AI_CONFIG, ...config };
    
    // 确保endpoint没有结尾的斜杠
    const baseEndpoint = DEEPSEEK_API_BASE.endsWith('/') 
      ? DEEPSEEK_API_BASE.slice(0, -1) 
      : DEEPSEEK_API_BASE;
    
    const url = `${baseEndpoint}/v1/chat/completions`;
    
    console.log(`调用DeepSeek API: ${url}`);
    
    // 构建消息数组
    const messages = [];
    if (systemPrompt) {
      messages.push({ role: "system", content: systemPrompt });
    }
    messages.push({ role: "user", content: userPrompt });
    
    const response = await axios({
      url: url,
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${DEEPSEEK_API_KEY}`,
        'Content-Type': 'application/json',
      },
      data: {
        model: DEEPSEEK_MODEL,
        messages: messages,
        temperature: aiConfig.temperature,
        max_tokens: aiConfig.maxTokens,
        stream: false
      },
      timeout: aiConfig.timeout,
    });
    
    if (response.status === 200) {
      console.log(`成功生成DeepSeek AI总结`);
      return response.data.choices[0].message.content;
    } else {
      throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
    }
  } catch (error) {
    console.error('DeepSeek API调用失败:', error);
    let errorMessage = 'DeepSeek AI总结生成失败，请稍后重试';
    
    if (error.response) {
      errorMessage += `(状态码: ${error.response.status}, 错误信息: ${JSON.stringify(error.response.data)})`;
    } else if (error.request) {
      errorMessage += `(请求错误: ${error.message})`;
    } else {
      errorMessage += `(错误: ${error.message})`;
    }
    
    throw new Error(errorMessage);
  }
};

/**
 * 通用AI文本生成函数
 * @param {String} provider AI提供商 ('azure', 'deepseek', 'huggingface')
 * @param {String} userPrompt 用户输入提示
 * @param {String} systemPrompt 系统提示（可选）
 * @param {Object} config 配置选项（可选）
 * @returns {Promise<String>} 生成的文本
 */
const generateText = async (provider, userPrompt, systemPrompt = null, config = {}) => {
  switch (provider) {
    case 'azure':
      return await generateTextWithAzureOpenAI(userPrompt, systemPrompt, config);
    case 'deepseek':
      return await generateTextWithDeepSeek(userPrompt, systemPrompt, config);
    case 'huggingface':
      // HuggingFace暂时保持原有接口
      return await generateTextWithHuggingFace({ inputs: userPrompt }, config.model);
    default:
      throw new Error(`不支持的AI提供商: ${provider}`);
  }
};

module.exports = {
  generateTextWithHuggingFace,
  generateTextWithAzureOpenAI,
  generateTextWithDeepSeek,
  generateText,
  DEFAULT_AI_CONFIG
}; 