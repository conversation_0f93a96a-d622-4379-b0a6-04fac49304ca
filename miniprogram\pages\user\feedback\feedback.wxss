/* pages/user/feedback/feedback.wxss */
.page {
  background-color: #f5f5f5;
  min-height: 100vh;
  padding-bottom: 200rpx;
}

/* ==================== 页面特有样式 Page Specific Styles ==================== */

/* 反馈类型选择器 */
.type-options {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.type-option {
  flex: 1;
  min-width: 140rpx;
  padding: 20rpx 24rpx;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  text-align: center;
  transition: all 0.3s ease;
  cursor: pointer;
}

.type-option.selected {
  border-color: #3B7ADB;
  background-color: #f0f6ff;
}

.option-text {
  font-size: 28rpx;
  color: #666;
}

.type-option.selected .option-text {
  color: #3B7ADB;
  font-weight: 600;
}

/* 内容输入区域特殊样式 */
.content-input {
  position: relative;
}

.char-count {
  position: absolute;
  bottom: 15rpx;
  right: 30rpx;
  font-size: 24rpx;
  color: #999;
}

.subtitle {
  font-size: 24rpx;
  color: #999;
  font-weight: normal;
  margin-left: 8rpx;
}

/* ==================== 图片上传组件 Image Upload Components ==================== */

.image-upload {
  margin-top: 20rpx;
}

.image-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.image-item {
  position: relative;
  width: 200rpx;
  height: 200rpx;
  border-radius: 12rpx;
  overflow: hidden;
  background-color: #f8f9fa;
  border: 1rpx solid #e9ecef;
}

.image-item image {
  width: 100%;
  height: 100%;
  background-color: #f8f9fa;
}

.delete-btn {
  position: absolute;
  top: -8rpx;
  right: -8rpx;
  width: 44rpx;
  height: 44rpx;
  background-color: #ff4757;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #fff;
  font-size: 28rpx;
  border: 4rpx solid #fff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.2);
  z-index: 10;
}

.add-image-btn {
  width: 200rpx;
  height: 200rpx;
  border: 2rpx dashed #ddd;
  border-radius: 12rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background-color: #fafafa;
}

.add-icon {
  font-size: 48rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.add-text {
  font-size: 24rpx;
  color: #999;
}

/* ==================== 底部操作区 Bottom Actions ==================== */

.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  padding: 30rpx;
  border-top: 2rpx solid #f0f0f0;
  box-shadow: 0 -4rpx 16rpx rgba(0, 0, 0, 0.05);
}

.action-tips {
  text-align: center;
}

/* 使用全局间距类的情况下，不需要额外的 margin-bottom 定义 */ 