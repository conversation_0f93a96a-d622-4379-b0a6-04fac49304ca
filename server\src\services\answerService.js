const Answer = require('../models/Answer');
const Question = require('../models/Question');
const mongoose = require('mongoose');
const notificationService = require('./notificationService');
const socketService = require('./socketService');
const { auditAnswerContent } = require('../utils/contentAuditUtils');

/**
 * 创建回答
 * @param {String} questionId 问题ID
 * @param {String} optionId 选项ID
 * @param {String} content 回答内容
 * @param {Boolean} isAnonymous 是否匿名
 * @param {String} userId 用户ID
 * @returns {Promise<Object>} 创建的回答
 */
const createAnswer = async (questionId, optionId, content, isAnonymous, userId) => {
  // 验证ObjectId有效性
  if (!mongoose.Types.ObjectId.isValid(questionId) || !mongoose.Types.ObjectId.isValid(optionId)) {
    throw new Error('无效的参数ID');
  }

  // 检查问题是否存在且未关闭
  const question = await Question.findById(questionId).populate('userId', 'nickname');
  if (!question) {
    throw new Error('问题不存在');
  }
  
  if (question.status === 'closed') {
    throw new Error('问题已关闭，无法回答');
  }

  // 验证选项是否属于该问题
  const option = question.options.find(opt => opt._id.toString() === optionId);
  if (!option) {
    throw new Error('选项不存在于该问题中');
  }

  // 检查问题是否需要回答理由
  if (question.requireReason && !content) {
    throw new Error('该问题需要提供回答理由');
  }

  // 内容审核 - 如果有回答理由，需要进行审核
  if (content && content.trim()) {
    try {
      const auditResult = auditAnswerContent(content);
      
      // 如果审核未通过，抛出错误
      if (!auditResult.passed) {
        const errorMessage = auditResult.violations.length > 0 
          ? auditResult.violations[0].message 
          : '内容审核未通过';
        
        throw new Error(`内容审核失败: ${errorMessage}`);
      }
    } catch (error) {
      console.error('回答内容审核过程中发生错误:', error);
      throw error;
    }
  }

  // 创建回答
  const answer = new Answer({
    questionId,
    optionId,
    userId,
    content: content || '',
    isAnonymous
  });

  await answer.save();

  // 处理问题投票通知（如果投票者不是问题作者）
  if (question.userId && question.userId._id.toString() !== userId.toString()) {
    try {
      // 获取用户信息
      const User = mongoose.model('User');
      const user = await User.findById(userId).select('nickname');
      
      // 构建通知内容
      let username = user ? user.nickname : "匿名用户";
      if (isAnonymous) {
        username = "匿名用户";
      }
      
      // 截取标题和选项内容，避免过长
      const shortTitle = question.title.length > 20 ? `${question.title.substring(0, 20)}...` : question.title;
      
      // 构建通知消息，包含回答理由（如果存在）
      let notificationMessage = `${username} 对您的问题"${shortTitle}"进行了投票（${option.content}）`;
      
      // 记录此通知是否包含理由
      let hasReason = false;
      
      // 如果有回答理由，添加到通知内容中
      if (content && content.trim()) {
        // 截取理由内容，避免过长
        const shortReason = content.length > 30 ? `${content.substring(0, 30)}...` : content;
        notificationMessage += `，理由："${shortReason}"`;
        hasReason = true;
      }
      
      // 创建通知，添加metadata字段包含回答ID和是否有理由的信息
      const notification = await notificationService.createNotification(
        question.userId._id,
        userId,
        'question_voted',
        notificationMessage,
        questionId,
        'question',
        {
          answerId: answer._id,
          hasReason: hasReason
        }
      );
      
      // 发送实时通知
      socketService.sendNotification(question.userId._id, notification);
    } catch (error) {
      console.error('发送问题投票通知失败:', error);
    }
  }

  return answer;
};

/**
 * 获取问题的回答列表
 * @param {String} questionId 问题ID
 * @param {Object} options 选项 (page, limit, sortBy, optionId)
 * @param {String} userId 当前用户ID
 * @returns {Promise<Object>} 回答列表和分页信息
 */
const getAnswers = async (questionId, options = {}, userId) => {
  // 验证ObjectId有效性
  if (!mongoose.Types.ObjectId.isValid(questionId)) {
    throw new Error('无效的问题ID');
  }

  const { 
    page = 1, 
    limit = 10, 
    sortBy = 'newest',
    optionId = null
  } = options;

  // 构建查询条件
  const query = { questionId };
  if (optionId && mongoose.Types.ObjectId.isValid(optionId)) {
    query.optionId = optionId;
  }

  // 设置排序
  let sort = {};
  switch (sortBy) {
    case 'newest':
      sort = { createdAt: -1 };
      break;
    case 'mostLiked':
      sort = { likes: -1, createdAt: -1 };
      break;
    default:
      sort = { createdAt: -1 };
  }

  // 获取总数
  const total = await Answer.countDocuments(query);

  // 获取回答列表
  const answers = await Answer.find(query)
    .sort(sort)
    .skip((page - 1) * limit)
    .limit(limit)
    .populate({
      path: 'userId',
      select: '_id nickname avatar',
      options: { lean: true }
    })
    .lean();

  // 获取问题信息，用于获取选项内容
  const question = await Question.findById(questionId).select('options').lean();
  
  if (!question) {
    throw new Error('问题不存在');
  }

  // 处理回答数据
  const formattedAnswers = answers.map(answer => {
    // 查找选项内容
    const option = question.options.find(opt => 
      opt._id.toString() === answer.optionId.toString()
    );
    
    // 检查当前用户是否点赞过该回答
    // 如果用户未登录（userId为null或undefined），则hasLiked为false
    const hasLiked = userId && answer.likedBy && answer.likedBy.some(id => 
      id.toString() === userId.toString()
    );

    return {
      id: answer._id,
      optionId: answer.optionId,
      optionContent: option ? option.content : '未知选项',
      content: answer.content,
      isAnonymous: answer.isAnonymous,
      likes: answer.likes,
      hasLiked: !!hasLiked, // 确保返回布尔值
      commentCount: answer.commentCount,
      createdAt: answer.createdAt,
      user: answer.isAnonymous ? 
        { id: null, nickname: '匿名用户', avatar: '/assets/images/default-avatar.png' } : 
        {
          id: answer.userId._id,
          nickname: answer.userId.nickname,
          avatar: answer.userId.avatar
        }
    };
  });

  // 计算总页数
  const pages = Math.ceil(total / limit);

  return {
    answers: formattedAnswers,
    pagination: {
      total,
      page,
      limit,
      pages
    }
  };
};

/**
 * 点赞/取消点赞回答
 * @param {String} answerId 回答ID
 * @param {String} action 操作类型 (like/unlike)
 * @param {String} userId 用户ID
 * @returns {Promise<Object>} 更新后的点赞信息
 */
const likeAnswer = async (answerId, action, userId) => {
  // 验证ObjectId有效性
  if (!mongoose.Types.ObjectId.isValid(answerId)) {
    throw new Error('无效的回答ID');
  }

  // 验证action参数
  if (!['like', 'unlike'].includes(action)) {
    throw new Error('无效的操作类型');
  }

  const answer = await Answer.findById(answerId).populate('userId', '_id');
  if (!answer) {
    throw new Error('回答不存在');
  }

  let updateOperation;
  let liked;

  if (action === 'like') {
    // 检查是否已经点赞
    const alreadyLiked = answer.likedBy.some(id => id.toString() === userId);
    if (alreadyLiked) {
      return { likes: answer.likes, liked: true };
    }

    // 添加点赞
    updateOperation = {
      $inc: { likes: 1 },
      $addToSet: { likedBy: userId }
    };
    liked = true;
    
    // 创建点赞通知（仅当点赞者不是回答作者时）
    if (answer.userId && answer.userId._id.toString() !== userId.toString()) {
      try {
        // 获取用户信息和问题信息
        const User = mongoose.model('User');
        const user = await User.findById(userId).select('nickname');
        const question = await Question.findById(answer.questionId).select('title');
        
        // 构建通知内容
        const username = user ? user.nickname : "用户";
        const shortTitle = question.title.length > 20 ? `${question.title.substring(0, 20)}...` : question.title;
        const shortContent = answer.content.length > 20 ? `${answer.content.substring(0, 20)}...` : answer.content;
        const notificationMessage = `${username} 赞了你的回答（问题：${shortTitle}）:"${shortContent}"`;
        
        // 创建通知
        const notification = await notificationService.createNotification(
          answer.userId._id,
          userId,
          'answer_liked',
          notificationMessage,
          answerId,
          'answer',
          {
            questionId: answer.questionId
          }
        );
        
        // 发送实时通知
        socketService.sendNotification(answer.userId._id, notification);
      } catch (error) {
        console.error('发送回答点赞通知失败:', error);
      }
    }
  } else {
    // 检查是否已经点赞
    const alreadyLiked = answer.likedBy.some(id => id.toString() === userId);
    if (!alreadyLiked) {
      return { likes: answer.likes, liked: false };
    }

    // 取消点赞
    updateOperation = {
      $inc: { likes: -1 },
      $pull: { likedBy: userId }
    };
    liked = false;
  }

  // 更新回答
  const updatedAnswer = await Answer.findByIdAndUpdate(
    answerId,
    updateOperation,
    { new: true }
  );

  return {
    likes: updatedAnswer.likes,
    liked
  };
};

module.exports = {
  createAnswer,
  getAnswers,
  likeAnswer
}; 