/* pages/user/about/about.wxss */

.page {
  min-height: 100vh;
  background-color: #f5f5f5;
  padding-bottom: 40rpx;
}

.container {
  padding: 40rpx 30rpx;
}

/* 通用section样式 */
.section {
  background-color: #fff;
  border-radius: 20rpx;
  padding: 40rpx 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

/* 应用信息样式 */
.app-info {
  text-align: center;
}

.app-logo {
  width: 120rpx;
  height: 120rpx;
  border-radius: 20rpx;
  overflow: hidden;
  background-color: #f8f8f8;
  display: flex;
  align-items: center;
  justify-content: center;
}

.app-logo image {
  width: 100%;
  height: 100%;
}

.app-name {
  font-size: 48rpx;
  font-weight: 700;
  color: #333;
  margin-bottom: 10rpx;
}

.app-version {
  font-size: 28rpx;
  color: #999;
  margin-bottom: 30rpx;
}

.app-description {
  font-size: 30rpx;
  color: #666;
  line-height: 1.6;
  text-align: left;
}

/* 功能特色样式 */
.feature-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.feature-item {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.feature-icon {
  width: 40rpx;
  height: 40rpx;
  background-color: #4CAF50;
  color: white;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24rpx;
  font-weight: bold;
  flex-shrink: 0;
}

.feature-text {
  font-size: 30rpx;
  color: #333;
  flex: 1;
}

/* 联系我们样式 */
.contact-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.contact-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background-color: #f8f9fa;
  border-radius: 16rpx;
  transition: all 0.3s ease;
}

.contact-item:active {
  background-color: #e9ecef;
  transform: scale(0.98);
}

.contact-label {
  font-size: 28rpx;
  color: #666;
  width: 120rpx;
  flex-shrink: 0;
}

.contact-value {
  font-size: 30rpx;
  color: #333;
  flex: 1;
}

.contact-action {
  font-size: 28rpx;
  color: #007AFF;
  font-weight: 500;
}

/* 团队信息样式 */
.team-info {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.team-item {
  display: flex;
  align-items: center;
}

.team-label {
  font-size: 28rpx;
  color: #666;
  width: 120rpx;
  flex-shrink: 0;
}

.team-value {
  font-size: 30rpx;
  color: #333;
  flex: 1;
}

/* 版权信息样式 */
.copyright {
  text-align: center;
  background-color: transparent;
  box-shadow: none;
  padding: 20rpx 30rpx;
}

.copyright-text {
  font-size: 24rpx;
  color: #999;
  line-height: 1.5;
  margin-bottom: 10rpx;
}

.copyright-text:last-child {
  margin-bottom: 0;
} 