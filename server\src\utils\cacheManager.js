const ProductComparisonCache = require('../models/ProductComparisonCache');

/**
 * 缓存管理工具类
 * 提供缓存清理、统计和监控功能
 */
class CacheManager {
  
  /**
   * 清理过期缓存
   * @param {Object} options 清理选项
   * @returns {Promise<Object>} 清理结果
   */
  static async cleanupExpiredCaches(options = {}) {
    const {
      maxAge = 7 * 24 * 60 * 60 * 1000, // 7天
      minHeatScore = 1,
      batchSize = 100
    } = options;

    try {
      const cutoffDate = new Date(Date.now() - maxAge);
      
      // 分批清理，避免大批量操作影响性能
      let totalDeleted = 0;
      let hasMore = true;

      console.log('🧹 开始清理过期缓存，条件：', {
        cutoffDate: cutoffDate.toISOString(),
        minHeatScore,
        batchSize
      });

      while (hasMore) {
        // 先查找需要删除的文档ID，限制数量
        const documentsToDelete = await ProductComparisonCache.find({
          $or: [
            { 'hotness.lastAccessTime': { $lt: cutoffDate } },
            { 'hotness.heatScore': { $lt: minHeatScore } },
            { 'cacheMetadata.status': 'expired' }
          ]
        })
        .select('_id')
        .limit(batchSize)
        .lean();

        if (documentsToDelete.length === 0) {
          hasMore = false;
          break;
        }

        // 提取文档ID数组
        const idsToDelete = documentsToDelete.map(doc => doc._id);

        // 执行删除操作
        const deleteResult = await ProductComparisonCache.deleteMany({
          _id: { $in: idsToDelete }
        });

        totalDeleted += deleteResult.deletedCount;
        hasMore = documentsToDelete.length === batchSize;

        console.log(`📋 本批次删除了 ${deleteResult.deletedCount} 个缓存，累计删除 ${totalDeleted} 个`);

        // 避免连续大量删除操作，添加短暂延迟
        if (hasMore) {
          await new Promise(resolve => setTimeout(resolve, 100));
        }
      }

      const message = `清理了 ${totalDeleted} 个过期缓存`;
      console.log(`✅ ${message}`);
      
      return {
        success: true,
        deletedCount: totalDeleted,
        message
      };

    } catch (error) {
      console.error('❌ 清理过期缓存失败:', error);
      return {
        success: false,
        error: error.message,
        deletedCount: 0
      };
    }
  }

  /**
   * 获取详细的缓存统计信息
   * @returns {Promise<Object>} 缓存统计
   */
  static async getDetailedCacheStats() {
    try {
      const [
        totalCaches,
        activeCaches,
        expiredCaches,
        topHotCaches,
        cachesByType,
        totalCacheSize,
        averageHeatScore
      ] = await Promise.all([
        ProductComparisonCache.countDocuments(),
        ProductComparisonCache.countDocuments({ 'cacheMetadata.status': 'active' }),
        ProductComparisonCache.countDocuments({ 'cacheMetadata.status': 'expired' }),
        this.getTopHotCaches(10),
        this.getCachesByProductType(),
        this.getTotalCacheSize(),
        this.getAverageHeatScore()
      ]);

      const hitRate = totalCaches > 0 ? (activeCaches / totalCaches * 100).toFixed(2) : 0;

      return {
        overview: {
          total: totalCaches,
          active: activeCaches,
          expired: expiredCaches,
          hitRate: `${hitRate}%`
        },
        performance: {
          totalSizeMB: (totalCacheSize / (1024 * 1024)).toFixed(2),
          averageHeatScore: averageHeatScore.toFixed(2),
          topHotCaches
        },
        distribution: {
          byProductType: cachesByType
        }
      };

    } catch (error) {
      console.error('获取缓存统计失败:', error);
      return null;
    }
  }

  /**
   * 获取热门缓存列表
   * @param {Number} limit 返回数量限制
   * @returns {Promise<Array>} 热门缓存列表
   */
  static async getTopHotCaches(limit = 10) {
    try {
      return await ProductComparisonCache.find({ 'cacheMetadata.status': 'active' })
        .sort({ 'hotness.heatScore': -1 })
        .limit(limit)
        .select('comparisonKey productInfo.productNames hotness cacheMetadata.cacheSize')
        .lean();
    } catch (error) {
      console.error('获取热门缓存失败:', error);
      return [];
    }
  }

  /**
   * 按产品类型统计缓存分布
   * @returns {Promise<Object>} 缓存类型分布
   */
  static async getCachesByProductType() {
    try {
      const result = await ProductComparisonCache.aggregate([
        { $match: { 'cacheMetadata.status': 'active' } },
        {
          $group: {
            _id: '$productInfo.productType',
            count: { $sum: 1 },
            totalSize: { $sum: '$cacheMetadata.cacheSize' },
            averageHeatScore: { $avg: '$hotness.heatScore' }
          }
        },
        { $sort: { count: -1 } }
      ]);

      const distribution = {};
      result.forEach(item => {
        distribution[item._id] = {
          count: item.count,
          totalSizeMB: (item.totalSize / (1024 * 1024)).toFixed(2),
          averageHeatScore: item.averageHeatScore.toFixed(2)
        };
      });

      return distribution;
    } catch (error) {
      console.error('获取缓存类型分布失败:', error);
      return {};
    }
  }

  /**
   * 计算总缓存大小
   * @returns {Promise<Number>} 总缓存大小（字节）
   */
  static async getTotalCacheSize() {
    try {
      const result = await ProductComparisonCache.aggregate([
        { $match: { 'cacheMetadata.status': 'active' } },
        {
          $group: {
            _id: null,
            totalSize: { $sum: '$cacheMetadata.cacheSize' }
          }
        }
      ]);

      return result.length > 0 ? result[0].totalSize : 0;
    } catch (error) {
      console.error('计算总缓存大小失败:', error);
      return 0;
    }
  }

  /**
   * 计算平均热度得分
   * @returns {Promise<Number>} 平均热度得分
   */
  static async getAverageHeatScore() {
    try {
      const result = await ProductComparisonCache.aggregate([
        { $match: { 'cacheMetadata.status': 'active' } },
        {
          $group: {
            _id: null,
            averageScore: { $avg: '$hotness.heatScore' }
          }
        }
      ]);

      return result.length > 0 ? result[0].averageScore : 0;
    } catch (error) {
      console.error('计算平均热度得分失败:', error);
      return 0;
    }
  }

  /**
   * 预热缓存（为热门产品组合预生成缓存）
   * @param {Array} popularComparisons 热门对比组合列表
   * @returns {Promise<Object>} 预热结果
   */
  static async warmupCache(popularComparisons = []) {
    if (!Array.isArray(popularComparisons) || popularComparisons.length === 0) {
      return {
        success: false,
        message: '没有提供预热的对比组合'
      };
    }

    let successCount = 0;
    let failCount = 0;
    const errors = [];

    for (const comparison of popularComparisons) {
      try {
        const { compareProductsByNames } = require('../services/productService');
        await compareProductsByNames(comparison.productNames);
        successCount++;
        console.log(`预热缓存成功: ${comparison.productNames.join(' vs ')}`);
      } catch (error) {
        failCount++;
        errors.push(`${comparison.productNames.join(' vs ')}: ${error.message}`);
        console.error(`预热缓存失败: ${comparison.productNames.join(' vs ')}`, error);
      }

      // 避免并发过高，添加延迟
      await new Promise(resolve => setTimeout(resolve, 500));
    }

    return {
      success: true,
      total: popularComparisons.length,
      successCount,
      failCount,
      errors
    };
  }

  /**
   * 强制刷新指定缓存
   * @param {String} comparisonKey 对比键
   * @returns {Promise<Object>} 刷新结果
   */
  static async refreshCache(comparisonKey) {
    try {
      const cache = await ProductComparisonCache.findOne({ comparisonKey });
      
      if (!cache) {
        return {
          success: false,
          message: '未找到指定的缓存'
        };
      }

      // 标记缓存为过期，强制重新生成
      cache.cacheMetadata.status = 'expired';
      await cache.save();

      // 重新生成缓存
      const { compareProductsByNames } = require('../services/productService');
      await compareProductsByNames(cache.productInfo.productNames);

      return {
        success: true,
        message: '缓存刷新成功'
      };

    } catch (error) {
      console.error('刷新缓存失败:', error);
      return {
        success: false,
        error: error.message
      };
    }
  }

  /**
   * 定时清理任务（建议在 cron job 中调用）
   * @returns {Promise<Object>} 清理结果
   */
  static async scheduledCleanup() {
    console.log('开始定时缓存清理...');
    
    const cleanupOptions = {
      maxAge: 7 * 24 * 60 * 60 * 1000, // 7天
      minHeatScore: 1, // 热度得分低于1的缓存
      batchSize: 50
    };

    const result = await this.cleanupExpiredCaches(cleanupOptions);
    
    // 获取清理后的统计信息
    const stats = await this.getDetailedCacheStats();
    
    console.log('定时缓存清理完成:', {
      deleted: result.deletedCount,
      remaining: stats?.overview?.active || 0
    });

    return {
      cleanup: result,
      stats
    };
  }
}

module.exports = CacheManager; 