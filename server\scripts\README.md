# PHONES_JS 数据批量导入工具

这套工具用于将 `data/PHONES_JS` 目录中的产品数据批量导入到 MongoDB 的 `NewProduct` 集合中。

## 文件说明

### 数据模型
- `src/models/NewProduct.js` - 新的产品数据模型，专门用于存储 JS 文件中的产品数据

### 脚本文件
- `scripts/importPhonesData.js` - 批量导入脚本
- `scripts/testImport.js` - 测试导入脚本（只导入少量数据）
- `scripts/queryNewProducts.js` - 查询和验证数据的脚本

## 快速开始

### 1. 测试导入
在正式导入所有数据之前，建议先运行测试脚本：

```bash
# 使用 npm 脚本
npm run phones:test

# 或直接运行
node scripts/testImport.js
```

### 2. 批量导入所有数据
```bash
# 使用 npm 脚本
npm run phones:import

# 或直接运行
node scripts/importPhonesData.js
```

### 3. 查询和验证数据
```bash
# 显示基本统计信息和示例产品
npm run phones:query

# 显示详细统计信息
npm run phones:query -- --stats

# 按品牌查询
npm run phones:query -- --brand 荣耀

# 搜索产品
npm run phones:query -- --search Magic

# 按价格范围查询
npm run phones:query -- --price 2000 5000

# 检查数据完整性
npm run phones:query -- --check

# 显示帮助
npm run phones:query -- --help
```

### 4. 清空数据
如果需要重新导入，可以先清空 NewProduct 集合：

```bash
# 使用 npm 脚本
npm run phones:clear

# 或直接运行
node scripts/importPhonesData.js --clear
```

## 数据结构

### NewProduct 模型特点
1. **灵活的规格存储** - 使用 `mongoose.Schema.Types.Mixed` 完全保留原始 JS 文件中的规格结构
2. **自动生成搜索关键词** - 从产品名称、品牌和规格中提取关键词
3. **智能搜索匹配字段** - 生成用于快速匹配的搜索字段
4. **文件路径追踪** - 记录数据来源文件路径
5. **品牌和系列分类** - 根据目录结构自动分类

### 主要字段
- `skuId` - 产品 SKU ID（唯一标识）
- `skuName` - 产品名称
- `productType` - 产品类型（phone、laptop、tablet 等）
- `brandName` - 品牌名称
- `configurations` - 产品配置（内存、存储、价格等）
- `commonSpecs` - 通用规格参数
- `conversionInfo` - 转换信息（记录原始文件信息）
- `sourceFilePath` - 源文件相对路径
- `brandFolder` - 品牌目录
- `seriesFolder` - 系列目录
- `searchKeywords` - 搜索关键词数组
- `searchMatch` - 智能搜索匹配字段

## 导入流程

1. **扫描文件** - 递归扫描 `data/PHONES_JS` 目录下的所有 `.js` 文件
2. **数据验证** - 检查文件格式和必需字段
3. **数据转换** - 将 JS 文件数据转换为 NewProduct 模型格式
4. **重复检查** - 根据 `skuId` 检查是否已存在，存在则更新，不存在则创建
5. **关键词生成** - 自动生成搜索关键词和匹配字段
6. **索引优化** - 自动创建优化的数据库索引

## 性能优化

### 数据库索引
自动创建以下索引以优化查询性能：
- 文本搜索索引（skuName、brandName、searchKeywords）
- 复合索引（productType + brandName + category）
- 价格范围索引
- 品牌和系列索引

### 批处理
- 支持大量文件的批量处理
- 错误容错机制，单个文件失败不影响整体导入
- 详细的导入统计和错误报告

## 环境变量

在 `.env` 文件中配置：
```
MONGODB_URI=mongodb://localhost:27017/xuanxuan_test
```

## 错误处理

### 常见问题
1. **文件格式错误** - 确保 JS 文件正确导出产品数据
2. **缺少必需字段** - 检查 skuId、skuName、productType、brandName 字段
3. **MongoDB 连接失败** - 检查数据库连接字符串和服务状态
4. **重复数据** - 系统会自动处理重复的 skuId，更新现有记录

### 日志输出
脚本会输出详细的执行日志：
- ✅ 成功操作
- ❌ 失败操作
- ⚠️ 警告信息
- 📊 统计信息

## 扩展性

### 支持新产品类型
1. 只需要在 JS 文件中设置正确的 `productType`
2. NewProduct 模型会自动适应不同的规格结构
3. 搜索和查询功能自动支持新产品类型

### 自定义处理逻辑
可以通过继承 `PhonesDataImporter` 类来自定义处理逻辑：

```javascript
class CustomImporter extends PhonesDataImporter {
  transformProductData(rawData, fileInfo) {
    // 自定义数据转换逻辑
    const transformed = super.transformProductData(rawData, fileInfo);
    // 添加自定义字段
    transformed.customField = 'custom value';
    return transformed;
  }
}
```

## 示例输出

### 导入统计
```
📊 导入统计信息
==================================================
📁 总文件数: 156
✅ 成功导入: 154
❌ 导入失败: 2
⏭️  跳过文件: 0
📈 成功率: 99%
```

### 数据统计
```
📊 NewProduct 数据统计

📱 总产品数: 154

📈 按品牌分布:
  荣耀: 21
  华为: 18
  小米: 15
  OPPO: 12
  VIVO: 11
  ...

📈 按产品类型分布:
  phone: 154

📈 按品牌目录分布:
  HONOR: 21
  HUAWEI: 18
  XIAOMI: 15
  OPPO: 12
  VIVO: 11
  ...
```
