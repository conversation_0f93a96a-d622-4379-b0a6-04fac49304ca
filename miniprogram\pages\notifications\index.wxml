<!-- 通知页面 -->
<view class="container">
  <!-- 未登录状态 -->
  <view class="not-logged-in" wx:if="{{!isLoggedIn}}">
    <view class="empty-icon">
      <image src="/assets/images/notification-empty.png" mode="aspectFit"></image>
    </view>
    <view class="empty-text">登录后查看您的通知</view>
    <button class="btn btn-primary login-btn" bindtap="goToLogin">去登录</button>
  </view>

  <!-- 已登录状态 -->
  <block wx:else>
    <!-- 页面标题和操作区域 -->
    <view class="header" wx:if="{{notifications.length > 0}}">
      <view class="title">通知列表</view>
      <view class="action" bindtap="markAllAsRead">全部标为已读</view>
    </view>

    <!-- 通知列表 -->
    <scroll-view 
      class="notification-list" 
      scroll-y 
      enable-flex 
      bindscrolltolower="loadMore" 
      wx:if="{{notifications.length > 0}}"
    >
      <view 
        class="notification-item {{item.isRead ? 'read' : ''}}" 
        wx:for="{{notifications}}" 
        wx:key="_id"
        bindtap="onNotificationTap"
        data-id="{{item._id}}"
        data-index="{{index}}"
        data-type="{{item.type}}"
        data-itemid="{{item.relatedItem.itemId}}"
        data-itemtype="{{item.relatedItem.itemType}}"
      >
        <!-- 通知图标 -->
        <view class="notification-icon {{item.iconClass}}"></view>
        
        <!-- 通知内容 -->
        <view class="notification-content">
          <view class="notification-text">{{item.content}}</view>
          <view class="notification-time">{{item.timeText}}</view>
          
          <!-- 回复按钮，支持多种通知类型 -->
          <!-- 1. 对question_voted类型且带有理由的通知显示回复按钮 -->
          <view 
            class="reply-btn" 
            wx:if="{{item.type === 'question_voted' && item.metadata && item.metadata.hasReason && item.metadata.answerId}}"
            catchtap="showReplyModal"
            data-id="{{item._id}}"
            data-answerid="{{item.metadata.answerId}}"
            data-replytype="answer"
          >
            添加评论
          </view>
          
          <!-- 2. 对answer_commented类型的通知显示回复按钮 -->
          <view 
            class="reply-btn" 
            wx:if="{{item.type === 'answer_commented' && item.metadata && item.metadata.commentId}}"
            catchtap="showReplyModal"
            data-id="{{item._id}}"
            data-answerid="{{item.relatedItem.itemId}}"
            data-commentid="{{item.metadata.commentId}}"
            data-replytype="comment"
          >
            回复
          </view>
          
          <!-- 3. 对comment_replied类型的通知显示回复按钮 -->
          <view 
            class="reply-btn" 
            wx:if="{{item.type === 'comment_replied' && item.metadata && item.metadata.commentId}}"
            catchtap="showReplyModal"
            data-id="{{item._id}}"
            data-commentid="{{item.metadata.commentId}}"
            data-replytype="comment"
            data-needinfo="true"
          >
            回复
          </view>
        </view>
        
        <!-- 未读标记 -->
        <view class="unread-dot" wx:if="{{!item.isRead}}"></view>
      </view>
      
      <!-- 加载更多指示器 -->
      <view class="loading-more" wx:if="{{loadingMore}}">
        <view class="loading-spinner"></view>
        <text>加载更多通知...</text>
      </view>
      
      <!-- 没有更多数据 -->
      <view class="no-more" wx:if="{{!hasMore && notifications.length > 0}}">
        没有更多通知了
      </view>
    </scroll-view>

    <!-- 空状态 -->
    <view class="empty-state" wx:if="{{empty && !loading}}">
      <view class="empty-icon">
        <image src="/assets/images/notification-empty.png" mode="aspectFit"></image>
      </view>
      <view class="empty-text">暂无通知</view>
      <view class="empty-desc">用户还没有收到任何通知</view>
    </view>

    <!-- 加载指示器 -->
    <view class="loading-container" wx:if="{{loading && !refreshing}}">
      <view class="loading-spinner"></view>
      <view class="loading-text">加载中...</view>
    </view>
    
    <!-- 回复对话框 -->
    <view class="reply-modal" wx:if="{{showReplyModal}}">
      <view class="reply-modal-mask" bindtap="closeReplyModal"></view>
      <view class="reply-modal-content">
        <view class="reply-modal-header">
          <text class="reply-modal-title">{{currentReplyType === 'comment' ? '回复评论' : '回复'}}</text>
          <text class="reply-modal-close" bindtap="closeReplyModal">×</text>
        </view>
        <view class="reply-modal-body">
          <textarea 
            class="reply-textarea" 
            placeholder="请输入回复内容..." 
            bindinput="inputReply"
            value="{{replyContent}}"
            maxlength="200"
            auto-focus
          ></textarea>
          
          <!-- 匿名选项 -->
          <view class="anonymous-option" bindtap="toggleAnonymous">
            <view class="checkbox {{isAnonymous ? 'checked' : ''}}">
              <view class="checkbox-inner" wx:if="{{isAnonymous}}"></view>
            </view>
            <text class="anonymous-text">匿名提交</text>
          </view>
          
          <view class="button-group">
            <button class="btn-secondary cancel-btn" bindtap="closeReplyModal">取消</button>
            <button class="btn-primary submit-btn" bindtap="submitReply">回复</button>
          </view>
        </view>
      </view>
    </view>
  </block>
</view> 