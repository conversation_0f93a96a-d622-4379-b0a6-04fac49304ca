<!--components/ai-compare-history-show/ai-compare-history-show.wxml-->
<view class="ai-compare-history-show">
  <!-- 历史记录列表 -->
  <view class="history-list" wx:if="{{!isEmpty}}">
    <block wx:for="{{formattedHistoryList}}" wx:key="id" wx:for-item="item">
      <view class="history-item" bindtap="viewComparisonDetail" data-item="{{item}}">
        <!-- 头部信息 -->
        <view class="item-header">
          <view class="header-left">
            <text class="type-tag">AI对比</text>
            <text class="product-count">{{item.productCount}}个产品</text>
          </view>
          <view class="header-right">
            <text class="time-text">{{item.formattedTime}}</text>
          </view>
        </view>

        <!-- 产品信息展示区 -->
        <view class="products-section">
          <view class="query-content">
            <text class="query-text">{{item.queryText || '产品对比查询'}}</text>
          </view>
          
          <view class="products-preview" wx:if="{{item.productNames && item.productNames.length > 0}}">
            <block wx:for="{{item.productNames}}" wx:key="index" wx:for-item="productName" wx:for-index="idx">
              <view class="product-tag" wx:if="{{idx < 3}}">
                <text class="product-name">{{productName}}</text>
              </view>
            </block>
            <view class="more-products" wx:if="{{item.productNames.length > 3}}">
              <text class="more-text">+{{item.productNames.length - 3}}</text>
            </view>
          </view>

          <view class="empty-products" wx:else>
            <text class="empty-text">暂无产品信息</text>
          </view>
        </view>

        <!-- 底部操作区 -->
        <view class="item-footer">
          <view class="status-badge">
            <text class="status-text status-success" wx:if="{{item.status === 'completed'}}">已完成</text>
            <text class="status-text status-processing" wx:elif="{{item.status === 'processing'}}">处理中</text>
            <text class="status-text status-failed" wx:else>失败</text>
          </view>
          <view class="action-tip">
            <text class="action-text">点击查看详情</text>
            <text class="action-arrow">→</text>
          </view>
        </view>
      </view>
    </block>

    <!-- 加载更多提示 -->
    <view class="load-more" wx:if="{{loading}}">
      <text class="loading-text">加载中...</text>
    </view>

    <view class="load-more" wx:elif="{{!hasMore && formattedHistoryList.length > 0}}">
      <text class="loading-text">没有更多记录了</text>
    </view>
  </view>

  <!-- 空状态 -->
  <view class="empty-state" wx:if="{{isEmpty && !loading}}">
    <view class="empty-icon">
      <text class="empty-emoji">📊</text>
    </view>
    <view class="empty-content">
      <view class="empty-title">暂无对比历史</view>
      <view class="empty-desc">您还没有使用过AI产品对比功能</view>
    </view>
    <view class="empty-action">
      <view class="action-btn" bindtap="goToCompare">
        去体验对比功能
      </view>
    </view>
  </view>

  <!-- 首次加载状态 -->
  <view class="loading-state" wx:if="{{loading && formattedHistoryList.length === 0}}">
    <view class="loading-icon">
      <text class="loading-spinner">⟳</text>
    </view>
    <view class="loading-content">
      <text class="loading-text">加载中...</text>
    </view>
  </view>
</view>
