<!--pages/user/feedback/detail/detail.wxml-->
<view class="page">
  <view class="container-fluid py-20">
    <!-- 加载状态 -->
    <view class="loading-state text-center py-40" wx:if="{{loading}}">
      <text class="text-secondary">加载中...</text>
    </view>
    
    <!-- 错误状态 -->
    <view class="error-state text-center py-40" wx:if="{{error && !loading}}">
      <text class="text-danger mb-40 d-block">{{error}}</text>
      <button class="btn btn-primary" bindtap="loadFeedbackDetail">重试</button>
    </view>
    
    <!-- 反馈详情 -->
    <view class="feedback-detail" wx:if="{{feedbackDetail && !loading && !error}}">
      <!-- 反馈头部信息 -->
      <view class="card">
        <view class="card-body">
          <view class="flex-between mb-20">
            <view class="type-badge type-{{feedbackDetail.type}}">
              {{getTypeText(feedbackDetail.type)}}
            </view>
            <view class="status-badge {{getStatusClass(feedbackDetail.status)}}">
              {{getStatusText(feedbackDetail.status)}}
            </view>
          </view>
          <view class="feedback-info">
            <text class="feedback-id text-secondary mb-10 d-block">反馈编号：{{feedbackDetail._id}}</text>
            <text class="create-time text-light">{{util.formatDateTime(feedbackDetail.createdAt)}}</text>
          </view>
        </view>
      </view>
      
      <!-- 反馈内容 -->
      <view class="card">
        <view class="card-body">
          <view class="section-title flex-between mb-20 pb-20">
            <text class="title-text">反馈内容</text>
            <view class="copy-btn flex items-center px-20 py-10" bindtap="copyContent">
              <text class="copy-icon mr-10">📋</text>
              <text class="copy-text">复制</text>
            </view>
          </view>
          <view class="content-text">{{feedbackDetail.content}}</view>
        </view>
      </view>
      
      <!-- 相关图片 -->
      <view class="card" wx:if="{{feedbackDetail.images && feedbackDetail.images.length > 0}}">
        <view class="card-body">
          <view class="section-title flex-between mb-20 pb-20">
            <text class="title-text">相关图片</text>
            <text class="image-count px-20 py-10">{{feedbackDetail.images.length}}张</text>
          </view>
          <view class="images-grid">
            <block wx:for="{{feedbackDetail.images}}" wx:key="index">
              <view class="image-wrapper">
                <image 
                  src="{{item}}" 
                  mode="aspectFill"
                  class="detail-image"
                  bindtap="previewImage"
                  data-index="{{index}}"
                ></image>
              </view>
            </block>
          </view>
        </view>
      </view>
      
      <!-- 管理员回复 -->
      <view class="card" wx:if="{{feedbackDetail.adminReply}}">
        <view class="card-body">
          <view class="section-title flex-between mb-20 pb-20">
            <text class="title-text">官方回复</text>
            <text class="reply-time text-light">{{util.formatDateTime(feedbackDetail.adminReply.createdAt)}}</text>
          </view>
          <view class="reply-content flex">
            <view class="reply-avatar mr-20">
              <image src="/assets/images/admin-avatar.png"></image>
            </view>
            <view class="reply-text flex-1">
              <view class="reply-author mb-10">客服小助手</view>
              <view class="reply-message">{{feedbackDetail.adminReply.content}}</view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 设备信息（可折叠） -->
      <view class="card" wx:if="{{feedbackDetail.deviceInfo}}">
        <view class="card-body">
          <view class="section-title flex-between pb-20" bindtap="toggleDeviceInfo">
            <text class="title-text">设备信息</text>
            <text class="toggle-icon {{showDeviceInfo ? 'expanded' : ''}}">></text>
          </view>
          <view class="device-details mt-20" wx:if="{{showDeviceInfo}}">
            <view class="device-item flex mb-20">
              <text class="device-label text-secondary">设备型号：</text>
              <text class="device-value text-dark">{{feedbackDetail.deviceInfo.model || '未知'}}</text>
            </view>
            <view class="device-item flex mb-20">
              <text class="device-label text-secondary">系统版本：</text>
              <text class="device-value text-dark">{{feedbackDetail.deviceInfo.system || '未知'}}</text>
            </view>
            <view class="device-item flex mb-20">
              <text class="device-label text-secondary">应用版本：</text>
              <text class="device-value text-dark">{{feedbackDetail.deviceInfo.appVersion || '未知'}}</text>
            </view>
            <view class="device-item flex">
              <text class="device-label text-secondary">屏幕尺寸：</text>
              <text class="device-value text-dark">{{feedbackDetail.deviceInfo.screenWidth}}×{{feedbackDetail.deviceInfo.screenHeight}}</text>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 处理记录 -->
      <view class="card" wx:if="{{feedbackDetail.processHistory && feedbackDetail.processHistory.length > 0}}">
        <view class="card-body">
          <view class="section-title pb-20 mb-20">
            <text class="title-text">处理记录</text>
          </view>
          <view class="history-timeline">
            <block wx:for="{{feedbackDetail.processHistory}}" wx:key="index">
              <view class="timeline-item flex mb-30">
                <view class="timeline-dot mr-20"></view>
                <view class="timeline-content flex-1">
                  <view class="timeline-title mb-10">{{item.action}}</view>
                  <view class="timeline-time text-light mb-10">{{util.formatDateTime(item.createdAt)}}</view>
                  <view class="timeline-desc text-secondary" wx:if="{{item.note}}">{{item.note}}</view>
                </view>
              </view>
            </block>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>

<!-- 引入工具函数 -->
<wxs module="util">
  function formatDateTime(time) {
    if (!time) return '';
    
    var date = getDate(time);
    var year = date.getFullYear();
    var month = date.getMonth() + 1;
    var day = date.getDate();
    var hour = date.getHours();
    var minute = date.getMinutes();
    
    if (month < 10) month = '0' + month;
    if (day < 10) day = '0' + day;
    if (hour < 10) hour = '0' + hour;
    if (minute < 10) minute = '0' + minute;
    
    return year + '-' + month + '-' + day + ' ' + hour + ':' + minute;
  }
  
  module.exports = {
    formatDateTime: formatDateTime
  };
</wxs> 