/* pages/user/ai-comparison-history/ai-comparison-history.wxss */

/**
 * AI对比产品历史页面样式
 * AI Comparison History Page Styles
 * 
 * 📋 功能说明:
 * - AI对比历史页面的主容器样式
 * - 页面布局和基础样式定义
 * 
 * 🎨 设计特色:
 * - 简洁的页面布局
 * - 与组件样式保持一致
 * 
 * 依赖: styles/variables.wxss, styles/components.wxss, styles/utilities.wxss
 */

/* ==================== 页面容器 Page Container ==================== */

.page {
  min-height: 100vh;
  background-color: #f5f5f5;
}

.container {
  background-color: transparent;
}

