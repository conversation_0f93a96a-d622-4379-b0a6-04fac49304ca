/**
 * 产品库页面样式
 * Product Library Page Styles
 * 依赖：全局样式系统（variables.wxss, components.wxss, utilities.wxss）
 */

/* ==================== 页面容器 Page Container ==================== */
.container {
  background-color: #f8f9fa;
  min-height: 100vh;
}

/* ==================== 页面特有样式覆盖 Page-specific Overrides ==================== */

/* ==================== 搜索区域样式 Search Section ==================== */
.search-title::before,
.filter-title::before,
.products-title::before {
  content: '';
  width: 6rpx;
  height: 24rpx;
  background: linear-gradient(135deg, #3B7ADB, #5B9BD5);
  border-radius: 3rpx;
  margin-right: 12rpx;
}

.search-row {
  display: flex;
  gap: 16rpx; /* 减少间距，让搜索框和按钮更紧密 */
  align-items: center;
}

.search-input-container {
  flex: 4; /* 增加flex权重，让搜索框占据更多空间 */
  min-width: 0; /* 确保flex子元素能够正确收缩 */
}

.search-input {
  width: 100%;
  height: 80rpx;
  padding: 0 20rpx;
  border: 2rpx solid #e6e6e6;
  border-radius: 8rpx;
  background-color: #fff;
  font-size: 28rpx;
  color: #333;
  transition: border-color 0.3s ease;
}

.search-input:focus {
  border-color: #3B7ADB;
  outline: none;
}

.search-input::placeholder {
  color: #999;
}

.search-btn {
  width: 120rpx !important; /* 使用!important覆盖全局样式 */
  min-width: 120rpx !important; /* 覆盖全局.btn的min-width */
  height: 80rpx;
  padding: 0 12rpx;
  font-size: 26rpx;
  border-radius: 8rpx;
  flex-shrink: 0; /* 防止按钮被压缩 */
}

/* 产品列表头部样式调整 */
.products-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 2rpx solid #f0f0f0;
}

.products-count {
  font-size: 24rpx;
  color: #666;
  background: #f8f9fa;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}

/* ==================== 筛选选项组件 Filter Options Component ==================== */
.filter-options {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
}

.filter-options.disabled {
  opacity: 0.5;
  pointer-events: none;
}

/* 筛选选项按钮 */
.option-item {
  padding: 16rpx 24rpx;
  border: 2rpx solid #e6e6e6;
  border-radius: 8rpx;
  background-color: #fff;
  font-size: 26rpx;
  color: #666;
  transition: all 0.3s ease;
  cursor: pointer;
  min-width: 120rpx;
  text-align: center;
}

.option-item:active {
  transform: scale(0.95);
}

.option-item.selected {
  background: linear-gradient(135deg, #3B7ADB, #5B9BD5);
  border-color: #3B7ADB;
  color: white;
  box-shadow: 0 2rpx 8rpx rgba(59, 122, 219, 0.3);
}

/* 无选项状态 */
.no-options {
  padding: 20rpx;
  text-align: center;
  color: #999;
  font-size: 26rpx;
  width: 100%;
  background: #f8f9fa;
  border-radius: 8rpx;
  border: 2rpx dashed #e6e6e6;
}

/* ==================== 筛选按钮区域 Filter Buttons ==================== */
.filter-buttons {
  display: flex;
  gap: 20rpx;
}

/* ==================== 产品列表组件 Products List Component ==================== */
/* 产品列表相关样式已移至 product-show 组件 */

/* ==================== 产品元信息 Product Meta ==================== */
/* 产品元信息相关样式已移至 product-show 组件 */

/* ==================== 状态组件 State Components ==================== */
/* 状态组件相关样式已移至 product-show 组件 */

/* ==================== scroll-view 滚动容器样式 ==================== */
/* scroll-view 相关样式已移至 product-show 组件 */

/* ==================== 欢迎页面样式优化 ==================== */
/* 欢迎页面相关样式已移至 product-show 组件 */

/* ==================== 加载更多文字链接区域 Load More Text Link Area ==================== */
/* 加载更多相关样式已移至 product-show 组件 */

/* ==================== 响应式适配 Responsive Design ==================== */
@media (max-width: 750rpx) {
  .search-row {
    flex-direction: column;
    gap: 16rpx;
  }

  .search-btn {
    width: 100%;
  }

  .filter-buttons {
    flex-direction: column;
  }

  /* 产品项相关响应式样式已移至 product-show 组件 */
}
