/**
 * AI通用产品对比工具使用示例
 * 展示如何使用ai_universal_product_comparison.js进行各种产品对比
 */

const { 
  compareProductsAI, 
  getProductRecommendations,
  saveComparisonResult 
} = require('./ai_universal_product_comparison.js');

/**
 * 示例1: 智能手机对比
 */
async function example1_smartphones() {
  console.log('\n📱 示例1: 智能手机对比');
  console.log('='.repeat(50));
  
  const phones = ['iPhone 15 Pro Max', 'Samsung Galaxy S24 Ultra', 'Google Pixel 8 Pro'];
  
  try {
    const result = await compareProductsAI(phones);
    
    if (result.success) {
      console.log(`✅ 对比成功`);
      console.log(`📊 产品类别: ${result.productCategory}`);
      console.log(`🔢 对比产品数: ${result.productCount}`);
      
      // 保存结果
      await saveComparisonResult(result, 'smartphone_comparison_example.json');
      
      // 显示部分报告
      console.log('\n📋 对比报告摘要:');
      console.log(result.comparisonReport.substring(0, 500) + '...');
      
    } else {
      console.log(`❌ 对比失败: ${result.error}`);
    }
  } catch (error) {
    console.error('示例1执行失败:', error.message);
  }
}

/**
 * 示例2: 笔记本电脑对比
 */
async function example2_laptops() {
  console.log('\n💻 示例2: 笔记本电脑对比');
  console.log('='.repeat(50));
  
  const laptops = ['MacBook Air M2', 'ThinkPad X1 Carbon Gen 11', 'Surface Laptop 5'];
  
  try {
    const result = await compareProductsAI(laptops);
    
    if (result.success) {
      console.log(`✅ 对比成功 - ${result.productCategory}`);
      await saveComparisonResult(result, 'laptop_comparison_example.json');
    }
  } catch (error) {
    console.error('示例2执行失败:', error.message);
  }
}

/**
 * 示例3: 汽车对比
 */
async function example3_cars() {
  console.log('\n🚗 示例3: 新能源汽车对比');
  console.log('='.repeat(50));
  
  const cars = ['特斯拉Model 3', '比亚迪海豹', '小鹏P7'];
  
  try {
    const result = await compareProductsAI(cars);
    
    if (result.success) {
      console.log(`✅ 对比成功 - ${result.productCategory}`);
      await saveComparisonResult(result, 'car_comparison_example.json');
    }
  } catch (error) {
    console.error('示例3执行失败:', error.message);
  }
}

/**
 * 示例4: 跨类别产品对比
 */
async function example4_cross_category() {
  console.log('\n🔀 示例4: 跨类别产品对比');
  console.log('='.repeat(50));
  
  const products = ['iPhone 15', 'MacBook Air', 'AirPods Pro'];
  
  try {
    const result = await compareProductsAI(products);
    
    if (result.success) {
      console.log(`✅ 跨类别对比 - 主类别: ${result.productCategory}`);
      console.log(`🔄 同类别产品: ${result.isSameCategory ? '是' : '否'}`);
      
      if (result.crossCategoryNote) {
        console.log(`📝 跨类别说明: ${result.crossCategoryNote}`);
      }
      
      await saveComparisonResult(result, 'cross_category_comparison_example.json');
    }
  } catch (error) {
    console.error('示例4执行失败:', error.message);
  }
}

/**
 * 示例5: 家电产品对比
 */
async function example5_appliances() {
  console.log('\n🏠 示例5: 家电产品对比');
  console.log('='.repeat(50));
  
  const appliances = ['戴森V15吸尘器', '小米扫地机器人', 'iRobot Roomba'];
  
  try {
    const result = await compareProductsAI(appliances);
    
    if (result.success) {
      console.log(`✅ 对比成功 - ${result.productCategory}`);
      await saveComparisonResult(result, 'appliance_comparison_example.json');
    }
  } catch (error) {
    console.error('示例5执行失败:', error.message);
  }
}

/**
 * 示例6: 产品推荐功能
 */
async function example6_recommendations() {
  console.log('\n💡 示例6: 产品推荐功能');
  console.log('='.repeat(50));
  
  try {
    const requirements = {
      budget: '3000-8000元',
      usage: '日常办公和轻度游戏',
      preferences: ['便携性', '续航长', '性价比高']
    };
    
    const recommendations = await getProductRecommendations('笔记本电脑', requirements);
    
    console.log('📋 推荐结果:');
    console.log(JSON.stringify(recommendations, null, 2));
    
  } catch (error) {
    console.error('示例6执行失败:', error.message);
  }
}

/**
 * 自定义产品对比函数
 * @param {Array<String>} productNames 产品名称列表
 * @param {String} customName 自定义保存文件名
 */
async function customComparison(productNames, customName = 'custom_comparison') {
  console.log(`\n🔧 自定义对比: ${productNames.join(' vs ')}`);
  console.log('='.repeat(50));
  
  try {
    const result = await compareProductsAI(productNames);
    
    if (result.success) {
      console.log(`✅ 对比成功`);
      console.log(`📊 产品类别: ${result.productCategory}`);
      console.log(`🔢 产品数量: ${result.productCount}`);
      console.log(`⏰ 分析时间: ${result.analysisTimestamp}`);
      
      // 显示产品规格概览
      if (result.productSpecs && result.productSpecs.length > 0) {
        console.log('\n📋 产品概览:');
        result.productSpecs.forEach((product, index) => {
          console.log(`  ${index + 1}. ${product.name} - ${product.basicInfo?.brand || '未知品牌'}`);
          if (product.basicInfo?.priceRange) {
            console.log(`     价格: ${product.basicInfo.priceRange}`);
          }
        });
      }
      
      await saveComparisonResult(result, `${customName}.json`);
      
      return result;
    } else {
      console.log(`❌ 对比失败: ${result.error}`);
      return null;
    }
  } catch (error) {
    console.error('自定义对比执行失败:', error.message);
    return null;
  }
}

/**
 * 主函数 - 运行所有示例
 */
async function runAllExamples() {
  console.log('🤖 AI通用产品对比工具 - 使用示例');
  console.log('='.repeat(80));
  console.log('⚠️  注意: 请确保已设置DEEPSEEK_API_KEY环境变量');
  console.log('='.repeat(80));
  
  const examples = [
    example1_smartphones,
    example2_laptops,
    example3_cars,
    example4_cross_category,
    example5_appliances,
    example6_recommendations
  ];
  
  for (let i = 0; i < examples.length; i++) {
    try {
      await examples[i]();
      
      // 示例间隔
      if (i < examples.length - 1) {
        console.log('\n' + '-'.repeat(40));
        console.log('⏳ 等待2秒后继续下一个示例...');
        await new Promise(resolve => setTimeout(resolve, 2000));
      }
    } catch (error) {
      console.error(`示例${i + 1}执行失败:`, error.message);
    }
  }
  
  console.log('\n✅ 所有示例执行完成！');
  console.log('📁 检查test文件夹中的生成文件');
}

/**
 * 快速测试函数
 */
async function quickTest() {
  console.log('🚀 快速测试 - 对比三款流行耳机');
  
  const headphones = ['AirPods Pro 2', 'Sony WH-1000XM5', 'Bose QuietComfort'];
  
  const result = await customComparison(headphones, 'quick_test_headphones');
  
  if (result) {
    console.log('\n🎧 快速测试完成！');
    console.log('📄 详细报告已保存到 quick_test_headphones.json 和 .txt 文件');
  }
}

// 导出函数供其他模块使用
module.exports = {
  example1_smartphones,
  example2_laptops,
  example3_cars,
  example4_cross_category,
  example5_appliances,
  example6_recommendations,
  customComparison,
  runAllExamples,
  quickTest
};

// 如果直接运行此文件
if (require.main === module) {
  // 检查命令行参数
  const args = process.argv.slice(2);
  
  if (args.length > 0) {
    // 如果提供了产品名称，进行自定义对比
    console.log('🔧 执行自定义产品对比...');
    customComparison(args, 'command_line_comparison')
      .then(() => process.exit(0))
      .catch(error => {
        console.error('执行失败:', error.message);
        process.exit(1);
      });
  } else {
    // 运行快速测试
    console.log('🚀 运行快速测试...');
    console.log('💡 提示: 你也可以通过命令行参数指定产品进行对比');
    console.log('   例如: node usage_examples.js "iPhone 15" "Samsung S24" "Pixel 8"');
    
    quickTest()
      .then(() => {
        console.log('\n💡 想要运行完整示例？请取消注释下面的代码行：');
        console.log('   // runAllExamples();');
      })
      .catch(error => {
        console.error('快速测试失败:', error.message);
        process.exit(1);
      });
    
    // 如果想运行所有示例，取消下面这行的注释
    // runAllExamples();
  }
}