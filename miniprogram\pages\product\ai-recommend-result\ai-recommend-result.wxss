/**
 * AI推荐结果页面样式
 * AI Recommend Result Page Styles
 */

/* ==================== 页面布局 Page Layout ==================== */
.page-container {
  padding: 0 0 120rpx 0;
  background-color: var(--bg-color-secondary, #f5f5f5);
  min-height: 100vh;
  box-sizing: border-box;
}

.page-header {
  padding: 30rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f8ff 100%);
  position: relative;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: var(--text-color-primary, #333);
  margin-bottom: 10rpx;
}

.page-desc {
  font-size: 28rpx;
  color: var(--text-color-secondary, #666);
}

/* ==================== 用户问题信息展示 ==================== */
.question-summary {
  margin: 20rpx 30rpx;
  background-color: #fff;
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.summary-header {
  margin-bottom: 20rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #f0f0f0;
}

.summary-title {
  font-size: 30rpx;
  font-weight: 600;
  color: var(--text-color-primary, #333);
}

.summary-content {
  display: flex;
  flex-direction: column;
  gap: 12rpx;
}

.summary-item {
  display: flex;
  align-items: flex-start;
  line-height: 1.5;
}

.summary-label {
  font-size: 26rpx;
  color: var(--text-color-secondary, #666);
  min-width: 140rpx;
  flex-shrink: 0;
}

.summary-value {
  font-size: 26rpx;
  color: var(--text-color-primary, #333);
  flex: 1;
}

/* ==================== 推荐结果区域 ==================== */
.results-container {
  padding: 0 30rpx;
}

.section-header {
  margin-bottom: 24rpx;
  text-align: center;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-color-primary, #333);
  display: block;
  margin-bottom: 8rpx;
}

.section-desc {
  font-size: 26rpx;
  color: var(--text-color-secondary, #666);
}

/* ==================== 产品列表样式 ==================== */
.products-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.product-item {
  background-color: #f8fcff;
  border: 1rpx solid #e6f7ff;
  border-radius: 12rpx;
  padding: 24rpx;
  transition: all 0.3s ease;
}

.product-item:hover {
  box-shadow: 0 4rpx 12rpx rgba(24, 144, 255, 0.1);
}

.product-info {
  margin-bottom: 20rpx;
}

.product-name {
  font-size: 30rpx;
  font-weight: 600;
  color: var(--text-color-primary, #333);
  line-height: 1.4;
  margin-bottom: 12rpx;
  display: block;
}

/* ==================== AI推荐理由样式 ==================== */
.ai-recommend-reason {
  margin-top: 16rpx;
  background-color: rgba(59, 122, 219, 0.05);
  border: 1rpx solid rgba(59, 122, 219, 0.1);
  border-radius: 8rpx;
  overflow: hidden;
}

.reason-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12rpx 16rpx;
  background-color: rgba(59, 122, 219, 0.08);
  border-bottom: 1rpx solid rgba(59, 122, 219, 0.1);
}

.ai-tag {
  font-size: 24rpx;
  color: var(--primary-color, #3B7ADB);
  font-weight: 500;
}

.expand-btn {
  font-size: 24rpx;
  color: var(--primary-color, #3B7ADB);
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
  background-color: rgba(59, 122, 219, 0.1);
}

.reason-content {
  padding: 16rpx;
  transition: all 0.3s ease;
}

.reason-content.collapsed {
  max-height: 0;
  padding: 0 16rpx;
  overflow: hidden;
}

.reason-content.expanded {
  max-height: none;
}

.recommend-reason {
  margin-bottom: 12rpx;
}

.reason-text {
  font-size: 26rpx;
  color: var(--text-color-primary, #333);
  line-height: 1.5;
}

.highlights-section {
  margin-top: 12rpx;
}

.highlights-title {
  font-size: 24rpx;
  color: var(--text-color-secondary, #666);
  font-weight: 500;
  margin-bottom: 8rpx;
  display: block;
}

.highlights-list {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.highlight-item {
  font-size: 24rpx;
  color: var(--text-color-primary, #333);
  line-height: 1.4;
}

/* ==================== 产品操作按钮样式 ==================== */
.product-actions {
  display: flex;
  gap: 12rpx;
  margin-top: 16rpx;
}

.action-btn {
  flex: 1;
  height: 64rpx;
  border-radius: 8rpx;
  font-size: 26rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6rpx;
  transition: all 0.3s ease;
  border: none;
}

.compare-btn {
  background-color: #f0f8ff;
  color: var(--primary-color, #3B7ADB);
  border: 1rpx solid #e6f7ff;
}

.compare-btn.in-compare {
  background-color: var(--primary-color, #3B7ADB);
  color: #fff;
}

.detail-btn {
  background-color: #fff;
  color: var(--text-color-primary, #333);
  border: 1rpx solid #e0e0e0;
}

.detail-btn:hover {
  background-color: #f5f5f5;
}

.btn-icon {
  font-size: 20rpx;
}

/* ==================== 空状态样式 ==================== */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 80rpx 40rpx;
  text-align: center;
}

.empty-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.empty-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-color-primary, #333);
  margin-bottom: 12rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: var(--text-color-secondary, #666);
  line-height: 1.5;
}

/* ==================== 底部操作按钮样式 ==================== */
.bottom-actions {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 20rpx 30rpx;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
}

.action-button {
  width: 100%;
  height: 88rpx;
  border-radius: 44rpx;
  font-size: 32rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8rpx;
  transition: all 0.3s ease;
  border: none;
}

.secondary-btn {
  background-color: #f0f8ff;
  color: var(--primary-color, #3B7ADB);
  border: 1rpx solid #e6f7ff;
}

.secondary-btn:hover {
  background-color: #e6f7ff;
}

/* ==================== 响应式调整 ==================== */
@media (max-width: 750rpx) {
  .product-actions {
    flex-direction: column;
    gap: 12rpx;
  }

  .action-btn {
    width: 100%;
    justify-content: center;
  }

  .summary-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 4rpx;
  }

  .summary-label {
    min-width: auto;
  }
}