/**
 * 手动检查过期问题的命令行工具
 * 用法: node checkExpiredQuestionsUtil.js
 */

// 加载环境变量
require('dotenv').config();

const mongoose = require('mongoose');
const { checkExpiredQuestions } = require('./scheduleService');

// 连接数据库
const connectDB = async () => {
  try {
    const conn = await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
    console.log(`📦 MongoDB 连接成功: ${conn.connection.host}`);
    return conn;
  } catch (error) {
    console.error(`❌ MongoDB 连接失败: ${error.message}`);
    process.exit(1);
  }
};

// 主函数
const main = async () => {
  console.log('🔍 开始手动检查过期问题...');
  
  // 连接数据库
  const conn = await connectDB();
  
  try {
    // 执行检查
    const result = await checkExpiredQuestions();
    console.log(`✅ 检查完成! 已关闭 ${result.modifiedCount} 个过期问题`);
  } catch (error) {
    console.error('❌ 检查过程出错:', error);
  } finally {
    // 关闭数据库连接
    await mongoose.connection.close();
    console.log('📦 数据库连接已关闭');
  }
};

// 执行主函数
if (require.main === module) {
  main().catch(err => {
    console.error('❌ 程序执行失败:', err);
    process.exit(1);
  });
}

module.exports = { main }; 