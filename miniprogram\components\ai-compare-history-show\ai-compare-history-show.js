// components/ai-compare-history-show/ai-compare-history-show.js
const util = require('../../utils/util');

Component({
  /**
   * 组件的属性列表
   */
  properties: {
    // 历史记录列表
    historyList: {
      type: Array,
      value: [],
      observer: function(newVal) {
        // 当历史记录列表更新时，格式化时间
        this.formatHistoryTimes(newVal);
      }
    },
    // 加载状态
    loading: {
      type: Boolean,
      value: false
    },
    // 是否有更多数据
    hasMore: {
      type: Boolean,
      value: true
    },
    // 是否为空状态
    isEmpty: {
      type: Boolean,
      value: false
    }
  },

  /**
   * 组件的初始数据
   */
  data: {
    formattedHistoryList: []
  },

  /**
   * 组件的方法列表
   */
  methods: {
    /**
     * 格式化历史记录的时间
     */
    formatHistoryTimes: function(historyList) {
      if (!historyList || !Array.isArray(historyList)) {
        this.setData({
          formattedHistoryList: []
        });
        return;
      }

      const formattedList = historyList.map(item => {
        const formattedItem = { ...item };
        
        // 使用 formatUTCToBeijing 转换UTC时间为北京时间
        // 优先使用 updatedAt，如果没有则使用 createdAt
        const timeToUse = item.updatedAt || item.createdAt;
        if (timeToUse) {
          formattedItem.formattedTime = this.formatFriendlyTime(timeToUse);
          formattedItem.beijingTime = util.formatUTCToBeijing(timeToUse, 'MM-DD HH:mm');
        } else {
          formattedItem.formattedTime = '';
          formattedItem.beijingTime = '';
        }
        
        return formattedItem;
      });

      this.setData({
        formattedHistoryList: formattedList
      });
    },

    /**
     * 格式化友好时间显示
     */
    formatFriendlyTime: function(utcTimeString) {
      if (!utcTimeString) return '';
      
      try {
        // 先转换为北京时间
        const beijingTimeStr = util.formatUTCToBeijing(utcTimeString, 'YYYY-MM-DD HH:mm:ss');
        if (!beijingTimeStr) return '';
        
        // 创建北京时间的Date对象
        const beijingDate = new Date(beijingTimeStr.replace(/-/g, '/'));
        const now = new Date();
        const diff = now.getTime() - beijingDate.getTime();
        
        // 计算时间差
        const minutes = Math.floor(diff / (1000 * 60));
        const hours = Math.floor(diff / (1000 * 60 * 60));
        const days = Math.floor(diff / (1000 * 60 * 60 * 24));
        
        if (minutes < 60) {
          return minutes <= 0 ? '刚刚' : `${minutes}分钟前`;
        } else if (hours < 24) {
          return `${hours}小时前`;
        } else if (days < 7) {
          return `${days}天前`;
        } else {
          // 超过7天显示具体日期
          return util.formatUTCToBeijing(utcTimeString, 'MM-DD HH:mm');
        }
      } catch (error) {
        console.error('时间格式化错误:', error);
        return util.formatUTCToBeijing(utcTimeString, 'MM-DD HH:mm') || '';
      }
    },

    /**
     * 查看对比详情
     */
    viewComparisonDetail: function(event) {
      const item = event.currentTarget.dataset.item;
      if (!item) {
        util.showToast('对比记录无效');
        return;
      }

      // 检查产品名称数据
      if (!item.productNames || !Array.isArray(item.productNames) || item.productNames.length < 2) {
        console.warn('产品名称数据不完整:', item);
        util.showToast('产品信息不完整，无法查看对比详情');
        return;
      }

      // 触发自定义事件，让父组件处理导航
      this.triggerEvent('viewDetail', {
        item: item
      });
    },

    /**
     * 点击去体验对比功能
     */
    goToCompare: function() {
      // 触发自定义事件，让父组件处理导航
      this.triggerEvent('goToCompare');
    }
  },

  /**
   * 组件生命周期
   */
  lifetimes: {
    attached: function() {
      // 组件挂载时格式化时间
      this.formatHistoryTimes(this.data.historyList);
    }
  }
})
