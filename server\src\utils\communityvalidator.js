const Joi = require('joi');

/**
 * 创建问题验证模式
 */
exports.createQuestionSchema = Joi.object({
  title: Joi.string()
    .max(50)
    .required()
    .messages({
      'string.empty': '问题标题不能为空',
      'string.max': '问题标题最多50个字符',
      'any.required': '请输入问题标题'
    }),
  scene: Joi.string()
    .max(500)
    .allow('')
    .optional()
    .messages({
      'string.max': '使用场景描述最多500个字符'
    }),
  keyFactors: Joi.string()
    .max(200)
    .allow('')
    .optional()
    .messages({
      'string.max': '关键考量因素最多200个字符'
    }),
  budget: Joi.object({
    min: Joi.number().min(0).allow(null).optional(),
    max: Joi.number().min(0).allow(null).optional(),
    currency: Joi.string().valid('CNY').default('CNY')
  }).optional().default({}),
  tags: Joi.array()
    .items(Joi.string().valid('手机', '电脑'))
    .max(2)
    .optional()
    .messages({
      'array.max': '最多选择2个标签',
      'any.only': '标签必须是"手机"或"电脑"'
    }),
  options: Joi.array()
    .items(
      Joi.object({
        content: Joi.string().max(100).required().messages({
          'string.empty': '选项内容不能为空',
          'string.max': '选项内容最多100个字符',
          'any.required': '请输入选项内容'
        })
      })
    )
    .min(2)
    .required()
    .messages({
      'array.min': '问题至少需要2个选项',
      'any.required': '请提供问题选项'
    }),
  isAnonymous: Joi.boolean().default(false),
  requireReason: Joi.boolean().default(false),
  visibility: Joi.object({
    type: Joi.string().valid('public', 'filtered').default('public'),
    filters: Joi.object({
      gender: Joi.array().items(Joi.string().valid('male', 'female', 'secret')).optional(),
      minAge: Joi.number().optional().allow(null),
      maxAge: Joi.number().optional().allow(null),
      regions: Joi.array().items(Joi.string()).optional(),
      occupations: Joi.array().items(Joi.string()).optional()
    }).optional()
  }).default({ type: 'public' }),
  expiryTime: Joi.date().optional().allow(null)
});

/**
 * 更新问题验证模式
 */
exports.updateQuestionSchema = Joi.object({
  title: Joi.string().max(50).optional().messages({
    'string.max': '问题标题最多50个字符'
  }),
  scene: Joi.string().max(500).allow('').optional().messages({
    'string.max': '使用场景描述最多500个字符'
  }),
  keyFactors: Joi.string()
    .max(200)
    .allow('')
    .optional()
    .messages({
      'string.max': '关键考量因素最多200个字符'
    }),
  budget: Joi.object({
    min: Joi.number().min(0).allow(null).optional(),
    max: Joi.number().min(0).allow(null).optional(),
    currency: Joi.string().valid('CNY').default('CNY')
  }).optional(),
  tags: Joi.array()
    .items(Joi.string().valid('手机', '电脑'))
    .max(2)
    .optional()
    .messages({
      'array.max': '最多选择2个标签',
      'any.only': '标签必须是"手机"或"电脑"'
    }),
  options: Joi.array()
    .items(
      Joi.object({
        content: Joi.string().max(100).required().messages({
          'string.empty': '选项内容不能为空',
          'string.max': '选项内容最多100个字符'
        })
      })
    )
    .min(2)
    .optional()
    .messages({
      'array.min': '问题至少需要2个选项'
    }),
  isAnonymous: Joi.boolean().optional(),
  requireReason: Joi.boolean().optional(),
  visibility: Joi.object({
    type: Joi.string().valid('public', 'filtered'),
    filters: Joi.object({
      gender: Joi.array().items(Joi.string().valid('male', 'female', 'secret')).optional(),
      minAge: Joi.number().optional().allow(null),
      maxAge: Joi.number().optional().allow(null),
      regions: Joi.array().items(Joi.string()).optional(),
      occupations: Joi.array().items(Joi.string()).optional()
    }).optional()
  }).optional(),
  expiryTime: Joi.date().optional().allow(null)
}).min(1);

/**
 * 创建回答验证模式
 */
exports.createAnswerSchema = Joi.object({
  optionId: Joi.string()
    .required()
    .messages({
      'string.empty': '选项ID不能为空',
      'any.required': '请选择一个选项'
    }),
  content: Joi.string()
    .max(500)
    .allow('')
    .optional()
    .custom((value, helpers) => {
      // 如果提供了内容，检查是否只包含空白字符
      if (value && value.trim().length === 0) {
        return helpers.error('string.empty');
      }
      return value;
    })
    .messages({
      'string.max': '回答理由最多500个字符',
      'string.empty': '回答理由不能只包含空白字符'
    }),
  isAnonymous: Joi.boolean().default(false)
});

/**
 * 点赞回答验证模式
 */
exports.likeAnswerSchema = Joi.object({
  action: Joi.string()
    .valid('like', 'unlike')
    .required()
    .messages({
      'string.empty': '操作类型不能为空',
      'any.only': '无效的操作类型',
      'any.required': '请提供操作类型'
    })
});

/**
 * 创建评论验证模式
 */
exports.createCommentSchema = Joi.object({
  content: Joi.string()
    .max(200)
    .required()
    .custom((value, helpers) => {
      // 检查是否只包含空白字符
      if (!value || value.trim().length === 0) {
        return helpers.error('string.empty');
      }
      // 检查最少字符数（去除空白字符后）
      if (value.trim().length < 2) {
        return helpers.error('string.min');
      }
      return value;
    })
    .messages({
      'string.empty': '评论内容不能为空或只包含空白字符',
      'string.max': '评论内容最多200个字符',
      'string.min': '评论内容至少需要2个字符',
      'any.required': '请输入评论内容'
    }),
  parentId: Joi.string().allow(null).optional(),
  isAnonymous: Joi.boolean().default(false),
  targetUserId: Joi.string().allow(null).optional()
});

/**
 * 获取评论列表验证模式
 */
exports.getCommentsSchema = Joi.object({
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(20)
});

/**
 * 获取回答列表验证模式
 */
exports.getAnswersSchema = Joi.object({
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(50).default(10),
  sortBy: Joi.string().valid('newest', 'oldest', 'mostLiked').default('newest'),
  optionId: Joi.string().allow(null, '').optional()
});

/**
 * 获取问题列表验证模式
 */
exports.getQuestionsSchema = Joi.object({
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(50).default(10),
  status: Joi.string().valid('open', 'closed').optional(),
  userId: Joi.string().optional(),
  // 修改验证规则，同时支持字符串和数组类型的标签
  tags: Joi.alternatives().try(
    Joi.string(),
    Joi.array().items(Joi.string())
  ).optional(),
  sortBy: Joi.string().valid('newest', 'oldest', 'popular').default('newest')
});

/**
 * 获取评论回复列表验证模式
 */
exports.getRepliesSchema = Joi.object({
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(50).default(10),
  isTopLevel: Joi.boolean().default(false)
});

/**
 * 搜索问题验证模式
 */
exports.searchQuestionsSchema = Joi.object({
  keyword: Joi.string()
    .max(100)
    .allow('')
    .optional()
    .messages({
      'string.max': '搜索关键词最多100个字符'
    }),
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(50).default(10),
  status: Joi.string().valid('open', 'closed').optional(),
  // 支持字符串和数组类型的标签过滤
  tags: Joi.alternatives().try(
    Joi.string().valid('手机', '电脑'),
    Joi.array().items(Joi.string().valid('手机', '电脑'))
  ).optional().messages({
    'any.only': '标签必须是"手机"或"电脑"'
  })
});

/**
 * AI推荐产品选项验证模式
 */
exports.aiRecommendProductsSchema = Joi.object({
  questionInfo: Joi.object({
    title: Joi.string()
      .max(50)
      .required()
      .messages({
        'string.empty': '问题标题不能为空',
        'string.max': '问题标题最多50个字符',
        'any.required': '请输入问题标题'
      }),
    scene: Joi.string()
      .max(500)
      .allow('')
      .optional()
      .messages({
        'string.max': '使用场景描述最多500个字符'
      }),
    keyFactors: Joi.string()
      .max(200)
      .allow('')
      .optional()
      .messages({
        'string.max': '关键考量因素最多200个字符'
      })
  }).required().messages({
    'any.required': '请提供问题信息'
  }),
  
  filterOptions: Joi.object({
    productType: Joi.string()
      // .valid('phone', 'laptop')
      .required()
      .messages({
        'string.empty': '产品类型不能为空',
        'any.required': '请选择产品类型'
      }),
    brands: Joi.array()
      .items(Joi.string().trim().min(1))
      .min(1)
      .max(10)
      .required()
      .messages({
        'array.min': '至少选择1个品牌',
        'array.max': '最多选择10个品牌',
        'any.required': '请选择至少一个品牌'
      }),
    budget: Joi.object({
      min: Joi.number().min(0).allow(null).optional(),
      max: Joi.number().min(0).allow(null).optional(),
      currency: Joi.string().valid('CNY').default('CNY')
    }).optional()
  }).required().messages({
    'any.required': '请提供筛选条件'
  })
});

/**
 * 问题产品对比验证模式
 */
exports.compareQuestionProductsSchema = Joi.object({
  questionId: Joi.string()
    .trim()
    .pattern(/^[0-9a-fA-F]{24}$/)
    .required()
    .messages({
      'string.empty': '问题ID不能为空',
      'string.pattern.base': '问题ID格式不正确',
      'any.required': '请提供问题ID'
    })
});
