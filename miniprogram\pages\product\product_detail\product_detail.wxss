/* pages/product/product_detail/product_detail.wxss */

.container {
  min-height: 100vh;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  padding: 0;
}

/* 加载状态 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  background: #fff;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #e3e3e3;
  border-top: 4rpx solid #007aff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 28rpx;
  color: #666;
}

/* 错误状态 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  padding: 40rpx;
  background: #fff;
}

.error-icon {
  font-size: 80rpx;
  margin-bottom: 20rpx;
}

.error-message {
  font-size: 28rpx;
  color: #666;
  text-align: center;
  margin-bottom: 40rpx;
  line-height: 1.5;
}

.retry-btn {
  background: #007aff;
  color: #fff;
  border: none;
  border-radius: 12rpx;
  padding: 20rpx 40rpx;
  font-size: 28rpx;
}

/* 产品详情 */
.product-detail {
  background: #fff;
  min-height: 100vh;
}

/* 产品头部 */
.product-header {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
  padding: 40rpx 30rpx 30rpx;
  position: sticky;
  top: 0;
  z-index: 100;
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
  display: flex;
  align-items: flex-start;
}

.product-image-container {
  width: 200rpx;
  height: 200rpx;
  margin-right: 30rpx;
  flex-shrink: 0;
  border-radius: 16rpx;
  overflow: hidden;
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10rpx);
  border: 1rpx solid rgba(255, 255, 255, 0.2);
}

.product-image {
  width: 100%;
  height: 100%;
  border-radius: 16rpx;
}

.image-placeholder {
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  color: rgba(255, 255, 255, 0.7);
  font-size: 24rpx;
}

.product-info {
  flex: 1;
  min-width: 0;
}

.product-name {
  font-size: 36rpx;
  font-weight: bold;
  color: #fff;
  line-height: 1.3;
  margin-bottom: 16rpx;
  word-break: break-word;
}

.product-brand {
  font-size: 26rpx;
  color: rgba(255, 255, 255, 0.8);
  margin-bottom: 8rpx;
}

.product-type {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.6);
  margin-bottom: 24rpx;
  background: rgba(255, 255, 255, 0.1);
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  display: inline-block;
}

.price-section {
  margin-bottom: 30rpx;
}

.price-range {
  font-size: 40rpx;
  font-weight: bold;
  color: #fff;
  margin-bottom: 8rpx;
  text-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.2);
}

.price-note {
  font-size: 22rpx;
  color: rgba(255, 255, 255, 0.7);
  display: flex;
  align-items: center;
}

.info-icon {
  margin-left: 8rpx;
  font-size: 22rpx;
}

.action-buttons {
  display: flex;
  gap: 20rpx;
}

.btn-copy, .btn-compare {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 20rpx 30rpx;
  border-radius: 12rpx;
  font-size: 26rpx;
  border: none;
  backdrop-filter: blur(10rpx);
}

.btn-copy {
  background: rgba(255, 255, 255, 0.15);
  color: #fff;
  border: 1rpx solid rgba(255, 255, 255, 0.3);
}

.btn-copy:active {
  background: rgba(255, 255, 255, 0.25);
}

.btn-compare {
  background: rgba(255, 255, 255, 0.2);
  color: #fff;
  border: 1rpx solid rgba(255, 255, 255, 0.4);
  font-weight: bold;
}

.btn-compare:active {
  background: rgba(255, 255, 255, 0.3);
}

.btn-icon {
  margin-right: 12rpx;
  font-size: 28rpx;
}

.btn-text {
  font-size: 26rpx;
}

/* 配置选择 */
.config-section {
  margin-bottom: 20rpx;
  background: #fff;
  border-radius: 0;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 30rpx;
  padding: 30rpx 30rpx 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

.config-selector {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 12rpx;
  margin: 0 30rpx 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.config-selector:active {
  background: #f0f0f0;
}

.config-current {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.config-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.config-price {
  font-size: 32rpx;
  color: #007aff;
  font-weight: bold;
}

.config-price-unavailable {
  font-size: 26rpx;
  color: #999;
  font-style: italic;
}

.config-arrow {
  font-size: 28rpx;
  color: #666;
  margin-left: 20rpx;
}

.config-stats {
  font-size: 24rpx;
  color: #666;
  text-align: center;
  padding: 0 30rpx 30rpx;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
}

/* 规格参数 */
.specs-section {
  margin-bottom: 20rpx;
  background: #fff;
  border-radius: 0;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.specs-section .section-title {
  padding: 30rpx;
  margin-bottom: 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1rpx solid #e9ecef;
}

.spec-category {
  border-bottom: 1rpx solid #f0f0f0;
}

.spec-category:last-child {
  border-bottom: none;
}

.category-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx;
  background: linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%);
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 12rpx;
  margin-bottom: 2rpx;
  box-shadow: 0 2rpx 8rpx rgba(0,0,0,0.1);
}

.category-header:active {
  background: #f0f0f0;
}

.category-header.expanded {
  background: linear-gradient(135deg, #e3f2fd 0%, #90caf9 100%);
  box-shadow: 0 4rpx 12rpx rgba(25,118,210,0.2);
}

.category-header.varying-category {
  background: linear-gradient(135deg, #e8f5e8 0%, #d4edda 100%);
  box-shadow: 0 2rpx 8rpx rgba(40,167,69,0.2);
}

.category-name {
  font-size: 28rpx;
  font-weight: bold;
  color: #333;
}

.category-name.varying-category-name {
  color: #28a745;
  font-weight: 600;
}

.category-toggle {
  display: flex;
  align-items: center;
}

.toggle-icon {
  font-size: 24rpx;
  color: #666;
  transition: transform 0.3s ease;
}

.category-content {
  overflow: hidden;
  transition: all 0.3s ease;
}

.category-content.show {
  max-height: none;
}

.category-content.hide {
  max-height: 0;
}

.category-content.varying-category-content {
  background: #fafbfc;
}

.spec-item {
  display: flex;
  align-items: flex-start;
  padding: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
  background: #fafbfc;
  margin: 2rpx 0;
}

.spec-item:last-child {
  border-bottom: none;
}

.param-name {
  width: 200rpx;
  font-size: 24rpx;
  color: #1976d2;
  flex-shrink: 0;
  margin-right: 30rpx;
  font-weight: 500;
}

.param-value {
  flex: 1;
  font-size: 26rpx;
  line-height: 1.4;
  word-break: break-word;
}

.normal-param-value {
  color: #333;
  font-weight: 500;
}

.varying-param-value {
  color: #28a745;
  font-weight: 600;
  background: linear-gradient(135deg, #e8f5e8 0%, #d4edda 100%);
  padding: 8rpx 16rpx;
  border-radius: 12rpx;
  border-left: 4rpx solid #28a745;
  display: inline-block;
}

/* 产品评分 */
.rating-section {
  margin-bottom: 20rpx;
  background: #fff;
  border-radius: 0;
  overflow: hidden;
  box-shadow: 0 2rpx 12rpx rgba(0, 0, 0, 0.05);
}

.rating-section .section-title {
  padding: 30rpx;
  margin-bottom: 0;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-bottom: 1rpx solid #e9ecef;
}

.rating-content {
  padding: 30rpx;
  background: #fafbfc;
}

.rating-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.rating-item:last-child {
  border-bottom: none;
}

.rating-label {
  font-size: 26rpx;
  color: #666;
  flex-shrink: 0;
  width: 200rpx;
}

.rating-value {
  display: flex;
  align-items: center;
  flex: 1;
  justify-content: flex-end;
}

.rating-stars {
  display: flex;
  align-items: center;
  margin-right: 16rpx;
}

.star {
  font-size: 28rpx;
  margin-right: 4rpx;
}

.star.filled {
  color: #ffd700;
}

.star.empty {
  color: #e0e0e0;
}

.rating-score {
  font-size: 26rpx;
  color: #333;
  font-weight: 600;
  min-width: 60rpx;
  text-align: right;
}

.rating-unavailable {
  font-size: 24rpx;
  color: #999;
  font-style: italic;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100vh;
  padding: 40rpx;
  background: #fff;
}

.empty-icon {
  font-size: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.5;
}

.empty-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #666;
  text-align: center;
  line-height: 1.5;
}

/* 配置选择弹窗 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.config-modal {
  background: #fff;
  border-radius: 24rpx;
  width: 80%;
  max-width: 600rpx;
  max-height: 70vh;
  overflow: hidden;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.2);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 40rpx;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: #fff;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #fff;
}

.modal-close {
  font-size: 48rpx;
  color: rgba(255, 255, 255, 0.8);
  cursor: pointer;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
}

.modal-close:active {
  background: rgba(255, 255, 255, 0.2);
}

.modal-content {
  max-height: 50vh;
  overflow-y: auto;
}

.config-option {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 30rpx 40rpx;
  border-bottom: 1rpx solid #f8f8f8;
  transition: all 0.3s ease;
}

.config-option:last-child {
  border-bottom: none;
}

.config-option.selected {
  background: linear-gradient(135deg, #e3f2fd 0%, #90caf9 100%);
}

.config-option:active {
  background: #f8f9fa;
}

.config-info {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.config-info .config-name {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
}

.config-info .config-price {
  font-size: 30rpx;
  color: #007aff;
  font-weight: bold;
}

.config-info .config-price-unavailable {
  font-size: 26rpx;
  color: #999;
  font-style: italic;
}

.config-selected {
  font-size: 32rpx;
  color: #007aff;
  font-weight: bold;
}

/* 响应式适配 */
@media (max-width: 750rpx) {
  .product-header {
    flex-direction: column;
    align-items: center;
    text-align: center;
  }

  .product-image-container {
    margin-right: 0;
    margin-bottom: 30rpx;
  }

  .action-buttons {
    width: 100%;
  }

  .param-name {
    width: 150rpx;
    margin-right: 20rpx;
  }
}

/* 产品对比输入组件样式 */
.product-compare-input {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 999;
}
