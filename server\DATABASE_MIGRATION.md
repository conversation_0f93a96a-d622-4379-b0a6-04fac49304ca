# 数据库迁移和问题解决指南

## 🔍 问题诊断

你遇到的错误主要有两种：

### 1. SKU ID 重复错误
```
E11000 duplicate key error collection: xuanxuan.products index: skuId_1 dup key: { skuId: "PHONE_2025_001" }
```

### 2. 旧索引冲突错误
```
E11000 duplicate key error collection: xuanxuan.products index: baseSkuId_1 dup key: { baseSkuId: null }
```

## 🛠️ 解决方案

### 方案一：快速解决（推荐）

```bash
# 1. 完整重置数据库（清理所有数据和索引）
npm run db:reset

# 2. 重新导入数据
npm run data:import
```

### 方案二：分步解决

```bash
# 1. 检查数据库当前状态
npm run db:check

# 2. 查看所有索引
npm run db:list-indexes

# 3. 清理产品数据
npm run db:clear

# 4. 重建索引
npm run db:rebuild-indexes

# 5. 导入数据
npm run data:import
```

### 方案三：强制导入（如果有其他重要数据）

```bash
# 使用强制模式，会覆盖现有数据
npm run data:import-force
```

## 📋 命令说明

| 命令 | 作用 | 安全性 |
|------|------|--------|
| `npm run db:check` | 检查数据库健康状态 | ✅ 安全 |
| `npm run db:list-indexes` | 查看所有索引 | ✅ 安全 |
| `npm run db:clear` | 清理产品数据 | ⚠️ 会删除数据 |
| `npm run db:reset` | 完整重置 | ⚠️ 会删除所有数据 |
| `npm run db:rebuild-indexes` | 重建索引 | ⚠️ 可能影响性能 |
| `npm run data:import` | 导入数据 | ✅ 安全（支持更新） |
| `npm run data:import-force` | 强制重新导入 | ⚠️ 会重置数据库 |

## 🔧 故障排除

### 如果重置后仍然失败：

1. **检查MongoDB连接**
   ```bash
   # 确保MongoDB服务运行正常
   mongosh --eval "db.runCommand('ping')"
   ```

2. **手动清理集合**
   ```javascript
   // 连接MongoDB
   use xuanxuan
   
   // 删除产品集合
   db.products.drop()
   
   // 重新创建集合
   db.createCollection("products")
   ```

3. **检查环境变量**
   ```bash
   # 确保.env文件中的数据库配置正确
   cat .env | grep MONGODB_URI
   ```

## 📊 数据结构变更说明

### 新版本Product模型特点：
- ✅ 移除了 `baseSkuId` 字段
- ✅ 使用 `skuId` 作为唯一标识
- ✅ 优化了索引结构
- ✅ 改进了产品参数存储方式

### 兼容性处理：
- 🔄 自动转换旧数据格式
- 🔄 智能处理重复数据
- 🔄 支持增量更新模式

## ⚡ 性能优化

新的导入系统包含以下优化：

1. **批量处理**: 分批导入，避免内存溢出
2. **并行处理**: 同时处理多个产品
3. **错误恢复**: 单个失败不影响整体导入
4. **智能更新**: 只更新变更的数据
5. **索引优化**: 重建最优索引结构

## 📝 预防措施

为避免将来再次遇到类似问题：

1. **定期备份**
   ```bash
   mongodump --db xuanxuan --out backup/$(date +%Y%m%d)
   ```

2. **版本控制数据模型**
   - 记录每次模型变更
   - 保持向后兼容性

3. **使用迁移脚本**
   - 所有结构变更通过脚本执行
   - 可回滚的操作

## 🆘 紧急恢复

如果操作过程中出现问题：

1. **停止所有操作**
   ```bash
   # 强制停止Node.js进程
   pkill -f node
   ```

2. **恢复备份**（如果有）
   ```bash
   mongorestore --db xuanxuan backup/20241220
   ```

3. **联系技术支持**
   - 保留错误日志
   - 记录操作步骤
   - 提供环境信息

---

**建议操作顺序**: `npm run db:reset` → `npm run data:import` 