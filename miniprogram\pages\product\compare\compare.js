const api = require('../../../utils/api');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 产品输入数组
    productInputs: ['', ''],
    // 是否正在对比
    comparing: false,
    // 对比结果
    compareResult: null,
    // AI分析映射
    analysisMap: {},
    // 错误信息
    errorMessage: '',
    // 是否可以开始对比
    canCompare: false,
    // 友好错误提示相关
    showFriendlyError: false,
    missingProducts: [],
    // 表格自适应相关
    productCount: 0,
    tableCustomStyle: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    // 初始化数据
    this.setData({
      productInputs: ['', '']
    });
    
    // 检查是否来自问题页面的对比
    if (options.fromQuestion === 'true' && options.questionId) {
      // 从全局数据中获取问题对比结果
      const app = getApp();
      const questionCompareResult = app.globalData.questionCompareResult;
      
      if (questionCompareResult) {
        console.log('接收到问题产品对比结果:', questionCompareResult);
        
        // 设置页面标题
        if (options.title) {
          wx.setNavigationBarTitle({
            title: decodeURIComponent(options.title)
          });
        }
        
        // 处理对比结果数据
        this.handleQuestionCompareResult(questionCompareResult);
        
        // 清除全局数据中的对比结果，避免重复使用
        app.globalData.questionCompareResult = null;
      } else {
        wx.showToast({
          title: '对比数据加载失败',
          icon: 'none'
        });
      }
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {

  },

  /**
   * 监听产品名称输入
   */
  onProductInput(e) {
    const index = e.currentTarget.dataset.index;
    const value = e.detail.value;
    const productInputs = [...this.data.productInputs];
    
    productInputs[index] = value;
    
    // 检查是否可以开始对比
    const validInputs = productInputs.filter(input => input.trim().length > 0);
    const canCompare = validInputs.length >= 2;
    
    this.setData({
      productInputs,
      canCompare,
      errorMessage: '' // 清除错误信息
    });
  },

  /**
   * 监听产品选择（来自智能搜索组件）
   */
  onProductSelect(e) {
    const index = e.currentTarget.dataset.index;
    const { product, value } = e.detail;
    const productInputs = [...this.data.productInputs];
    
    productInputs[index] = value;
    
    // 检查是否可以开始对比
    const validInputs = productInputs.filter(input => input.trim().length > 0);
    const canCompare = validInputs.length >= 2;
    
    this.setData({
      productInputs,
      canCompare,
      errorMessage: '' // 清除错误信息
    });

    console.log('用户选择了产品:', product);
    
    // 显示选择成功的提示
    wx.showToast({
      title: '产品已选择',
      icon: 'success',
      duration: 1000
    });
  },

  /**
   * 监听产品清空（来自智能搜索组件）
   */
  onProductClear(e) {
    const index = e.currentTarget.dataset.index;
    const productInputs = [...this.data.productInputs];
    
    productInputs[index] = '';
    
    // 检查是否可以开始对比
    const validInputs = productInputs.filter(input => input.trim().length > 0);
    const canCompare = validInputs.length >= 2;
    
    this.setData({
      productInputs,
      canCompare,
      errorMessage: '' // 清除错误信息
    });
  },

  /**
   * 添加产品输入框
   */
  addProduct() {
    const productInputs = [...this.data.productInputs];
    
    if (productInputs.length < 5) {
      productInputs.push('');
      this.setData({
        productInputs
      });
    }
  },

  /**
   * 移除产品输入框
   */
  removeProduct(e) {
    const index = e.currentTarget.dataset.index;
    const productInputs = [...this.data.productInputs];
    
    if (productInputs.length > 2) {
      productInputs.splice(index, 1);
      
      // 重新检查是否可以开始对比
      const validInputs = productInputs.filter(input => input.trim().length > 0);
      const canCompare = validInputs.length >= 2;
      
      this.setData({
        productInputs,
        canCompare
      });
    }
  },

  /**
   * 开始产品对比
   */
  async startCompare() {
    try {
      // 获取有效的产品名称
      const validProductNames = this.data.productInputs
        .map(input => input.trim())
        .filter(name => name.length > 0);

      // 验证输入
      if (validProductNames.length < 2) {
        this.setData({
          errorMessage: '请至少输入2个产品名称'
        });
        return;
      }

      if (validProductNames.length > 5) {
        this.setData({
          errorMessage: '最多只能对比5个产品'
        });
        return;
      }

      console.log('开始产品对比:', validProductNames);

      // 显示加载状态
      this.setData({
        comparing: true,
        compareResult: null,
        errorMessage: '',
        showFriendlyError: false, // 重置友好错误提示状态
        missingProducts: []
      });

      // 显示加载提示
      wx.showLoading({
        title: '对比中...',
        mask: true
      });

      // 调用产品对比API
      const result = await api.product.compareProducts(validProductNames);
      
      console.log('产品对比API原始结果:', result);

      // 隐藏加载提示
      wx.hideLoading();

      if (result.success) {
        // 处理后端返回的数据结构
        const responseData = result.data;
        
        console.log('处理前的响应数据:', responseData);
        
        // 重构对比结果数据以匹配前端期望的格式
        const processedResult = {
          productType: responseData.productType,
          products: responseData.products || [],
          comparisonTable: responseData.comparisonTable || [], // 使用后端返回的comparisonTable字段
          meta: responseData.meta || {},
          notFoundProducts: responseData.meta?.notFoundProducts || []
        };

        // 处理AI分析数据 - 使用后端返回的aiAnalysis字段
        const analysisMap = {};
        if (responseData.aiAnalysis && Array.isArray(responseData.aiAnalysis)) {
          responseData.aiAnalysis.forEach(item => {
            if (item.parameter && item.analysis) {
              analysisMap[item.parameter] = item.analysis;
            }
          });
        }

        console.log('处理后的对比结果:', processedResult);
        console.log('AI分析映射:', analysisMap);

        this.setData({
          compareResult: processedResult,
          analysisMap,
          comparing: false
        });

        // 🔧 新增：动态设置CSS变量以支持表格宽度自适应
        if (processedResult.products && processedResult.products.length > 0) {
          const productCount = processedResult.products.length;
          
          // 正确使用 wx.createSelectorQuery 需要绑定到当前页面实例
          const query = wx.createSelectorQuery().in(this);
          query.select('.comparison-table').boundingClientRect((res) => {
            if (res) {
              // 表格存在，可以进行后续操作
              console.log(`📊 表格自适应：设置产品数量为 ${productCount}，预计表格宽度：${140 + productCount * 160 + 200}rpx`);
              
              // 如果需要设置CSS变量，可以通过setData更新样式相关的数据
              this.setData({
                productCount: productCount,
                tableCustomStyle: `--product-count: ${productCount};`
              });
            }
          }).exec();
          
          console.log(`📊 表格自适应配置完成，产品数量：${productCount}`);
        }

        // 滚动到结果区域
        setTimeout(() => {
          wx.pageScrollTo({
            selector: '.result-section',
            duration: 300
          });
        }, 100);

        // 显示成功提示
        wx.showToast({
          title: '对比完成',
          icon: 'success'
        });

        // 如果有未找到的产品，显示提示
        if (processedResult.notFoundProducts && processedResult.notFoundProducts.length > 0) {
          setTimeout(() => {
            wx.showModal({
              title: '提示',
              content: `以下产品未找到：${processedResult.notFoundProducts.join('、')}`,
              showCancel: false,
              confirmText: '知道了'
            });
          }, 1500);
        }

      } else {
        // 处理对比失败的情况
        let errorMessage = '对比失败，请重试';
        
        if (result.message) {
          errorMessage = result.message;
        }
        
        // 特殊处理产品数量不足的情况
        if (result.code === 400 && result.message && result.message.includes('找到的可对比产品数量不足')) {
          // 确保状态正确设置
          this.setData({
            comparing: false
          });
          this.handleInsufficientProductsError(validProductNames, result);
          return;
        }
        
        // 如果有部分产品找到，显示详细信息
        if (result.data && result.data.notFoundProducts) {
          const notFound = result.data.notFoundProducts;
          if (notFound.length > 0) {
            errorMessage = `以下产品未找到：${notFound.join('、')}`;
          }
        }

        this.setData({
          errorMessage,
          comparing: false
        });
      }

    } catch (error) {
      console.error('产品对比出错:', error);
      
      // 隐藏加载提示
      wx.hideLoading();
      
      let errorMessage = '对比失败，请重试';
      if (error.message) {
        if (error.message.includes('网络')) {
          errorMessage = '网络连接失败，请检查网络后重试';
        } else {
          errorMessage = error.message;
        }
      }
      
      // 特殊处理产品数量不足的错误
      if (error.code === 400 && error.message && error.message.includes('找到的可对比产品数量不足')) {
        // 确保状态正确设置
        this.setData({
          comparing: false
        });
        const validProductNames = this.data.productInputs.filter(input => input.trim().length > 0);
        this.handleInsufficientProductsError(validProductNames, error);
        return;
      }
      
      this.setData({
        errorMessage,
        comparing: false
      });
      
      wx.showToast({
        title: errorMessage,
        icon: 'none',
        duration: 2000
      });
    }
  },

  /**
   * 处理产品数量不足的错误
   */
  handleInsufficientProductsError(productNames, errorResponse) {
    console.log('处理产品数量不足错误:', productNames, errorResponse);
    
    // 确保加载状态被正确清除，同时显示友好提示
    this.setData({
      comparing: false,
      errorMessage: '',
      showFriendlyError: true,
      missingProducts: productNames
    });
  },

  /**
   * 跳转到意见反馈页面
   */
  goToFeedback() {
    const missingProducts = this.data.missingProducts;
    const feedbackContent = `您好，我想对比以下产品，但在数据库中没有找到相关信息：\n\n${missingProducts.map((product, index) => `${index + 1}. ${product}`).join('\n')}\n\n希望能够添加这些产品的信息到数据库中，谢谢！`;
    
    // 跳转到反馈页面并传递预填充内容
    wx.navigateTo({
      url: `/pages/user/feedback/feedback?content=${encodeURIComponent(feedbackContent)}&type=suggestion`
    });
  },

  /**
   * 关闭友好错误提示
   */
  closeFriendlyError() {
    this.setData({
      showFriendlyError: false,
      missingProducts: [],
      comparing: false, // 确保comparing状态被重置
      errorMessage: '' // 清除可能存在的错误信息
    });
  },

  /**
   * 重新对比
   */
  resetCompare() {
    this.setData({
      compareResult: null,
      analysisMap: {},
      errorMessage: ''
    });
  },

  /**
   * 分享功能
   */
  onShareAppMessage() {
    const productNames = this.data.productInputs
      .filter(input => input.trim().length > 0)
      .slice(0, 2);
    
    const title = productNames.length >= 2 
      ? `${productNames[0]} vs ${productNames[1]} 产品对比` 
      : '产品参数对比';

    return {
      title,
      path: '/pages/product/compare/compare',
      imageUrl: '/assets/images/share-compare.png'
    };
  },

  /**
   * 分享到朋友圈
   */
  onShareTimeline() {
    const productNames = this.data.productInputs
      .filter(input => input.trim().length > 0)
      .slice(0, 2);
    
    const title = productNames.length >= 2 
      ? `${productNames[0]} vs ${productNames[1]} 产品对比` 
      : '产品参数对比 - 选选投票';

    return {
      title,
      query: '',
      imageUrl: '/assets/images/share-compare.png'
    };
  },

  /**
   * 处理来自问题页面的对比结果
   */
  handleQuestionCompareResult(result) {
    console.log('处理问题产品对比结果:', result);
    
    try {
      // 构建对比结果数据结构，匹配现有的数据格式
      // const compareData = {
      //   productType: result.productType,
      //   products: result.products || [],
      //   comparisonTable: result.comparisonTable || [],
      //   aiAnalysis: result.aiAnalysis || null,
      //   meta: result.meta || {}
      // };
      
      // // 构建AI分析映射
      // const analysisMap = {};
      // if (result.aiAnalysis) {
      //   // 如果AI分析是字符串，直接使用
      //   if (typeof result.aiAnalysis === 'string') {
      //     analysisMap.general = result.aiAnalysis;
      //   } else if (typeof result.aiAnalysis === 'object') {
      //     // 如果是对象，遍历处理
      //     Object.keys(result.aiAnalysis).forEach(key => {
      //       analysisMap[key] = result.aiAnalysis[key];
      //     });
      //   }
      // }
      const compareData = {
        productType: result.productType,
        products: result.products || [],
        comparisonTable: result.comparisonTable || [], // 使用后端返回的comparisonTable字段
        meta: result.meta || {},
        notFoundProducts: result.meta?.notFoundProducts || []
      };

      // 处理AI分析数据 - 使用后端返回的aiAnalysis字段
      const analysisMap = {};
      if (result.aiAnalysis && Array.isArray(result.aiAnalysis)) {
        result.aiAnalysis.forEach(item => {
          if (item.parameter && item.analysis) {
            analysisMap[item.parameter] = item.analysis;
          }
        });
      }
      
      // 从产品名称构建productInputs数组
      const productInputs = [];
      if (result.products && Array.isArray(result.products)) {
        result.products.forEach(product => {
          if (product.name) {
            productInputs.push(product.name);
          }
        });
      }
      
      // 确保至少有2个输入框
      while (productInputs.length < 2) {
        productInputs.push('');
      }
      
      // 设置页面数据
      this.setData({
        productInputs: productInputs,
        compareResult: compareData,
        analysisMap: analysisMap,
        comparing: false,
        canCompare: true,
        errorMessage: '',
        showFriendlyError: false,
        missingProducts: result.meta?.notFoundProducts || []
      });
      
      // 滚动到结果区域
      setTimeout(() => {
        wx.pageScrollTo({
          selector: '.result-section',
          duration: 300
        }).catch(err => {
          console.log('滚动到结果区域失败:', err);
        });
      }, 100);
      
      // 显示成功提示
      wx.showToast({
        title: '对比完成',
        icon: 'success'
      });
      
      // 如果有未找到的产品，显示提示
      const notFoundProducts = result.meta?.notFoundProducts;
      if (notFoundProducts && notFoundProducts.length > 0) {
        setTimeout(() => {
          wx.showModal({
            title: '提示',
            content: `以下产品选项未能匹配到具体产品：${notFoundProducts.join('、')}，其他产品对比正常进行`,
            showCancel: false,
            confirmText: '知道了'
          });
        }, 1500);
      }
      
    } catch (error) {
      console.error('处理问题对比结果失败:', error);
      wx.showToast({
        title: '对比数据处理失败',
        icon: 'none'
      });
    }
  },

  /**
   * 监听产品配置变更
   */
  onConfigChange(e) {
    const productId = e.currentTarget.dataset.productId;
    const selectedIndex = parseInt(e.detail.value);
    
    if (!this.data.compareResult || !this.data.compareResult.products) {
      return;
    }

    // 更新产品配置选择
    const products = this.data.compareResult.products.map(product => {
      if (product.id === productId) {
        const selectedConfig = product.configurations[selectedIndex];
        return {
          ...product,
          selectedConfigIndex: selectedIndex,
          price: selectedConfig.price,
          displayName: product.configurations.length > 1 
            ? `${product.name} (${selectedConfig.name})` 
            : product.name,
          defaultConfigDetails: {
            name: selectedConfig.name,
            ram: selectedConfig.ram,
            storage: selectedConfig.storage,
            price: selectedConfig.price,
            available: selectedConfig.available
          }
        };
      }
      return product;
    });

    // 更新对比结果
    this.setData({
      'compareResult.products': products
    });

    console.log('配置已更新:', {
      productId,
      selectedIndex,
      selectedConfig: products.find(p => p.id === productId)?.defaultConfigDetails
    });

    // 🆕 改进的用户反馈体验
    const selectedProduct = products.find(p => p.id === productId);
    const configName = selectedProduct?.defaultConfigDetails?.name || '未知配置';
    
    wx.showToast({
      title: `已切换至 ${configName}`,
      icon: 'success',
      duration: 2000
    });

    // 🆕 添加触觉反馈（如果设备支持）
    if (wx.vibrateShort) {
      wx.vibrateShort({
        type: 'light'
      }).catch(() => {
        // 静默处理振动不支持的情况
        console.log('设备不支持触觉反馈');
      });
    }
  }
}); 