const app = getApp();
const { question, answer, comment } = require('../../../utils/api');
const { formatUTCToBeijing, friendlyTime } = require('../../../utils/util');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    id: null,
    questionDetail: null,
    loading: true,
    answerList: [],
    answersLoading: false,
    commentContent: '',
    selectedOption: null,
    userInfo: null,
    showReasonModal: false,
    isAnonymous: false,
    // 评论相关数据
    showCommentModal: false,
    currentAnswerId: null,
    commentList: {},
    commentLoading: {},
    replyTo: null,
    replyComment: '',
    commentPage: {},
    commentLimit: 20,
    // 回复相关数据
    showReplies: {},
    replyLoading: {},
    replyPage: {},
    // 记录是否来自通知页面的跳转
    fromNotification: false,
    // 是否是问题创建者
    isQuestionCreator: false,
    // 记录需要显示的回答ID和评论ID
    targetAnswerId: null,
    targetCommentId: null,
    // 高亮回答ID和评论ID
    highlightedAnswerId: null,
    highlightedCommentId: null,
    // 产品对比相关状态
    comparingProducts: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    if (options.id) {
      this.setData({
        id: options.id,
        userInfo: app.globalData.userInfo,
        // 记录是否来自通知页面的跳转
        fromNotification: options.fromNotification === 'true' || options.fromNotification === true,
        // 记录需要显示的回答ID和评论ID
        targetAnswerId: options.answerId || null,
        targetCommentId: options.commentId || null
      });
      this.loadQuestionDetail();
      this.loadAnswers().then(() => {
        // 数据加载完成后，检查是否需要滚动到指定回答
        if (this.data.targetAnswerId) {
          this.scrollToAnswer(this.data.targetAnswerId);
        }
      });
    } else {
      wx.showToast({
        title: '问题ID无效',
        icon: 'none'
      });
      setTimeout(() => {
        wx.navigateBack();
      }, 1500);
    }
  },

  /**
   * 加载问题详情
   */
  loadQuestionDetail: function() {
    const { id } = this.data;
    this.setData({ loading: true });

    question.getQuestionById(id)
      .then(res => {
        // 检查当前用户是否是问题创建者
        const isQuestionCreator = this.data.userInfo && res.data.user && this.data.userInfo.id === res.data.user.id;
        
        // 格式化时间
        const questionData = res.data;
        questionData.createdAt = formatUTCToBeijing(questionData.createdAt);
        questionData.updatedAt = formatUTCToBeijing(questionData.updatedAt);
        questionData.expiryTime = formatUTCToBeijing(questionData.expiryTime);
        
        this.setData({
          questionDetail: questionData,
          loading: false,
          isQuestionCreator: isQuestionCreator
        });
      })
      .catch(err => {
        console.error('获取问题详情失败:', err);
        this.setData({ loading: false });
        
        // 检查是否是问题不存在的错误
        if (err && err.code === 404 && err.message === "问题不存在") {
          wx.showToast({
            title: '该问题已被删除',
            icon: 'none',
            duration: 2000
          });
          
          // 延迟返回，让用户有时间看到提示
          setTimeout(() => {
            wx.navigateBack();
          }, 2000);
        } else {
          wx.showToast({
            title: '获取问题详情失败',
            icon: 'none'
          });
        }
      });
  },

  /**
   * 加载问题回答
   */
  loadAnswers: function() {
    const { id } = this.data;
    this.setData({ answersLoading: true });

    return answer.getAnswers(id)
      .then(res => {
        // 过滤掉没有content的回答
        const filteredAnswers = res.data.answers
          .filter(answer => answer.content && answer.content.trim().length > 0)
          .map(answer => {
            // 格式化时间
            answer.createdAt = formatUTCToBeijing(answer.createdAt);
            answer.updatedAt = formatUTCToBeijing(answer.updatedAt);
            return answer;
          });
          
        this.setData({
          answerList: filteredAnswers,
          answersLoading: false
        });
        return Promise.resolve(); // 返回Promise以支持链式调用
      })
      .catch(err => {
        console.error('获取回答列表失败:', err);
        this.setData({ answersLoading: false });
        
        // 如果问题详情已经成功加载，则仅提示获取回答失败
        // 如果问题不存在，loadQuestionDetail会处理返回逻辑，这里不需要重复处理
        if (this.data.questionDetail) {
          wx.showToast({
            title: '获取回答失败',
            icon: 'none'
          });
        }
        
        return Promise.reject(err);
      });
  },

  /**
   * 选择选项
   */
  selectOption: function(e) {
    const { id } = e.currentTarget.dataset;
    const { questionDetail } = this.data;
    
    // 如果问题已关闭，则不允许投票
    if (questionDetail.status === 'closed') {
      wx.showToast({
        title: '该问题已停止投票',
        icon: 'none'
      });
      return;
    }
    
    const requireReason = questionDetail.requireReason;
    
    this.setData({
      selectedOption: id,
      showReasonModal: true
    });
  },

  /**
   * 关闭理由输入对话框
   */
  closeReasonModal: function() {
    this.setData({
      showReasonModal: false,
      isAnonymous: false,
      commentContent: ''
    });
  },

  /**
   * 切换匿名状态
   */
  toggleAnonymous: function() {
    this.setData({
      isAnonymous: !this.data.isAnonymous
    });
  },

  /**
   * 提交投票
   */
  submitVote: function() {
    const { id, selectedOption, commentContent, questionDetail, isAnonymous } = this.data;
    
    if (!selectedOption) {
      wx.showToast({
        title: '请选择一个选项',
        icon: 'none'
      });
      return;
    }

    if (questionDetail.requireReason && !commentContent.trim()) {
      wx.showToast({
        title: '请输入您的理由',
        icon: 'none'
      });
      return;
    }

    answer.createAnswer(id, {
      optionId: selectedOption,
      content: commentContent,
      isAnonymous: isAnonymous
    }).then(res => {
      wx.showToast({
        title: '投票成功',
        icon: 'success'
      });
      this.setData({
        commentContent: '',
        showReasonModal: false,
        isAnonymous: false
      });
      this.loadQuestionDetail();
      this.loadAnswers();
    }).catch(err => {
      console.error('投票失败:', err);
      wx.showToast({
        title: '投票失败，请重试',
        icon: 'none'
      });
    });
  },

  /**
   * 输入评论内容
   */
  inputComment: function(e) {
    this.setData({
      commentContent: e.detail.value
    });
  },

  /**
   * 显示评论模态框
   */
  showCommentModal: function(e) {
    const { answerId } = e.currentTarget.dataset;
    
    this.setData({
      showCommentModal: true,
      currentAnswerId: answerId,
      replyTo: null,
      replyComment: ''
    });
    
    // 如果没有加载过该回答的评论，则加载
    if (!this.data.commentList[answerId]) {
      const loadPromise = this.loadComments(answerId);
      // 确保loadPromise是一个Promise对象
      if (loadPromise && typeof loadPromise.then === 'function') {
        loadPromise.then(() => {
          // 如果有目标评论ID，加载完成后滚动到该评论
          if (this.data.targetCommentId && this.data.currentAnswerId === this.data.targetAnswerId) {
            this.scrollToModalComment(this.data.targetCommentId);
          }
        }).catch(err => {
          console.error('加载评论失败:', err);
        });
      }
    } else {
      // 如果已加载过评论且有目标评论ID，直接滚动到该评论
      if (this.data.targetCommentId && this.data.currentAnswerId === this.data.targetAnswerId) {
        // 稍等渲染完成后再滚动
        setTimeout(() => {
          this.scrollToModalComment(this.data.targetCommentId);
        }, 300);
      }
    }
  },

  /**
   * 关闭评论模态框
   */
  closeCommentModal: function() {
    this.setData({
      showCommentModal: false,
      currentAnswerId: null,
      replyTo: null,
      replyComment: ''
    });
  },

  /**
   * 加载评论
   */
  loadComments: function(answerId, page = 1) {
    // 检查是否已有加载状态
    if (this.data.commentLoading[answerId]) {
      return Promise.resolve(); // 返回一个已解决的Promise
    }
    
    // 设置加载状态
    const commentLoading = { ...this.data.commentLoading };
    commentLoading[answerId] = true;
    this.setData({ commentLoading });
    
    // 获取评论
    const { commentLimit } = this.data;
    return comment.getComments(answerId, page, commentLimit)
      .then(res => {
        // 处理评论数据
        const commentList = { ...this.data.commentList };
        const commentPage = { ...this.data.commentPage };
        
        // 格式化评论时间
        const formattedComments = res.data.comments.map(comment => {
          comment.createdAt = formatUTCToBeijing(comment.createdAt);
          if (comment.replies) {
            comment.replies = comment.replies.map(reply => {
              reply.createdAt = formatUTCToBeijing(reply.createdAt);
              return reply;
            });
          }
          return comment;
        });
        
        if (page === 1) {
          commentList[answerId] = formattedComments;
        } else {
          commentList[answerId] = [...(commentList[answerId] || []), ...formattedComments];
        }
        
        commentPage[answerId] = {
          current: page,
          total: res.data.total,
          hasMore: page * commentLimit < res.data.total
        };
        
        // 更新状态
        const commentLoading = { ...this.data.commentLoading };
        commentLoading[answerId] = false;
        
        this.setData({
          commentList,
          commentPage,
          commentLoading
        });
      })
      .catch(err => {
        console.error('获取评论失败:', err);
        // 更新加载状态
        const commentLoading = { ...this.data.commentLoading };
        commentLoading[answerId] = false;
        this.setData({ commentLoading });
        
        wx.showToast({
          title: '获取评论失败',
          icon: 'none'
        });
      });
  },

  /**
   * 加载评论回复
   */
  loadReplies: function(commentId, answerId, page = 1, isAllReplies = true) {
    if (!commentId || !answerId) return;
    
    // 设置加载状态
    this.setData({
      [`replyLoading.${commentId}`]: true
    });
    
    comment.getReplies(commentId, {
      page,
      limit: 10,
      isTopLevel: isAllReplies // 如果为true，则获取所有关联回复；否则只获取直接回复
    }).then(res => {
      const { replies, pagination } = res.data;
      
      // 更新评论列表中的回复
      const commentList = this.data.commentList[answerId] || [];
      const commentIndex = commentList.findIndex(c => c.id === commentId);
      
      if (commentIndex !== -1) {
        // 如果是第一页，则替换原有的回复；否则追加
        if (page === 1) {
          commentList[commentIndex].replies = replies;
        } else {
          commentList[commentIndex].replies = [
            ...(commentList[commentIndex].replies || []),
            ...replies
          ];
        }
        
        // 更新回复页码信息
        this.setData({
          [`commentList.${answerId}`]: commentList,
          [`replyPage.${commentId}`]: pagination,
          [`replyLoading.${commentId}`]: false,
          [`showReplies.${commentId}`]: true
        });
      }
    }).catch(err => {
      console.error('获取回复失败:', err);
      this.setData({
        [`replyLoading.${commentId}`]: false
      });
      
      wx.showToast({
        title: '获取回复失败',
        icon: 'none'
      });
    });
  },
  
  /**
   * 切换显示回复
   */
  toggleReplies: function(e) {
    const { commentId, answerId } = e.currentTarget.dataset;
    const showReplies = this.data.showReplies || {};
    
    // 如果已经显示，则隐藏
    if (showReplies[commentId]) {
      this.setData({
        [`showReplies.${commentId}`]: false
      });
      return;
    }
    
    // 否则加载回复并显示
    // 检查是否已经加载过回复
    const commentList = this.data.commentList[answerId] || [];
    const comment = commentList.find(c => c.id === commentId);
    
    if (comment && comment.replies && comment.replies.length > 0) {
      // 已经有回复数据，直接显示
      this.setData({
        [`showReplies.${commentId}`]: true
      });
    } else {
      // 没有回复数据，需要加载
      this.loadReplies(commentId, answerId);
    }
  },
  
  /**
   * 加载更多回复
   */
  loadMoreReplies: function(e) {
    const { commentId, answerId } = e.currentTarget.dataset;
    const replyPage = this.data.replyPage || {};
    const currentPage = replyPage[commentId] || { page: 0 };
    
    // 如果有下一页，则加载
    if (currentPage.page < currentPage.pages) {
      this.loadReplies(commentId, answerId, currentPage.page + 1);
    }
  },

  /**
   * 回复评论
   */
  replyToComment: function(e) {
    const { commentId, nickname, replyId } = e.currentTarget.dataset;
    
    this.setData({
      replyTo: {
        topCommentId: commentId, // 保存顶级评论ID
        commentId: replyId || commentId, // 如果是回复回复，则使用replyId，否则使用顶级评论ID
        nickname
      }
    });
  },

  /**
   * 删除评论
   */
  async handleDeleteComment(e) {
    try {
      const { commentId, answerId } = e.currentTarget.dataset;
      
      if (!commentId || !answerId) {
        throw new Error('缺少必要参数');
      }
      
      // 显示确认对话框
      const res = await wx.showModal({
        title: '确认删除',
        content: '确定要删除这条评论吗？',
        confirmText: '删除',
        confirmColor: '#ff4d4f'
      });

      if (res.confirm) {
        wx.showLoading({
          title: '删除中...',
          mask: true
        });

        // 调用删除评论API
        await comment.deleteComment(commentId);

        // 重新加载评论列表
        await this.loadComments(answerId);

        wx.showToast({
          title: '删除成功',
          icon: 'success'
        });
      }
    } catch (err) {
      console.error('删除评论失败:', err);
      // 根据错误类型显示不同的提示
      let errorMessage = '删除评论失败，请稍后重试';
      
      if (err.code === 403) {
        errorMessage = '您没有权限删除此评论';
      } else if (err.code === 404) {
        errorMessage = '评论不存在或已被删除';
      } else if (err.message === '缺少必要参数') {
        errorMessage = '参数错误，请刷新页面重试';
      }
      
      wx.showToast({
        title: errorMessage,
        icon: 'none',
        duration: 2000
      });
    } finally {
      wx.hideLoading();
    }
  },

  /**
   * 输入回复内容
   */
  inputReplyComment: function(e) {
    this.setData({
      replyComment: e.detail.value
    });
  },

  /**
   * 提交评论
   */
  submitComment: function() {
    const { currentAnswerId, replyTo, replyComment } = this.data;
    
    if (!replyComment.trim()) {
      wx.showToast({
        title: '请输入评论内容',
        icon: 'none'
      });
      return;
    }
    
    const commentData = {
      content: replyComment.trim(),
      parentId: replyTo ? replyTo.commentId : null,
      isAnonymous: false
    };
    
    comment.createComment(currentAnswerId, commentData)
      .then(() => {
        this.setData({
          replyTo: null,
          replyComment: ''
        });
        
        // 如果是回复某条评论（而非直接评论回答）
        if (replyTo) {
          // 判断是否是回复顶级评论还是回复的回复
          const isReplyToTopComment = replyTo.topCommentId === replyTo.commentId;
          
          if (isReplyToTopComment) {
            // 如果是回复顶级评论，重新加载该顶级评论的回复
            this.loadReplies(replyTo.topCommentId, currentAnswerId);
          } else {
            // 如果是回复其他回复，重新加载顶级评论的所有回复
            this.loadReplies(replyTo.topCommentId, currentAnswerId);
          }
        } else {
          // 直接评论回答，重新加载评论列表
          this.loadComments(currentAnswerId);
        }
        
        // 重新加载回答列表刷新评论数
        this.loadAnswers();
        
        wx.showToast({
          title: '评论成功',
          icon: 'success'
        });
      })
      .catch(err => {
        console.error('提交评论失败:', err);
        wx.showToast({
          title: '评论失败',
          icon: 'none'
        });
      });
  },

  /**
   * 点赞/取消点赞回答
   */
  toggleLike: function(e) {
    const { answerId, hasLiked } = e.currentTarget.dataset;
    const action = hasLiked ? 'unlike' : 'like';
    
    answer.likeAnswer(answerId, action)
      .then(() => {
        // 更新回答列表中的点赞状态
        const { answerList } = this.data;
        const index = answerList.findIndex(item => item.id === answerId);
        
        if (index !== -1) {
          const targetAnswer = answerList[index];
          answerList[index] = {
            ...targetAnswer,
            hasLiked: !hasLiked,
            likes: hasLiked ? targetAnswer.likes - 1 : targetAnswer.likes + 1
          };
          
          this.setData({ answerList });
        }
      })
      .catch(err => {
        console.error(`${action}操作失败:`, err);
        wx.showToast({
          title: `${action === 'like' ? '点赞' : '取消点赞'}失败`,
          icon: 'none'
        });
      });
  },

  /**
   * 预览图片
   */
  previewImage: function(e) {
    const { current, urls } = e.currentTarget.dataset;
    
    wx.previewImage({
      current,
      urls
    });
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {
    // 稍等一会再执行滚动操作，确保页面已完全渲染
    setTimeout(() => {
      // 如果有目标回答或评论，等数据加载完成后滚动到相应位置
      if (this.data.targetAnswerId || this.data.targetCommentId) {
        this.scrollToTarget();
      }
    }, 500);
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide: function () {
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {
    // 如果是从通知页面跳转过来的，返回时需要标记通知为已读
    if (this.data.fromNotification) {
      const eventChannel = this.getOpenerEventChannel();
      if (eventChannel && eventChannel.emit) {
        eventChannel.emit('markNotificationRead');
      }
    }
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    // 刷新问题详情和回答列表
    Promise.all([
      this.loadQuestionDetail(),
      this.loadAnswers()
    ]).then(() => {
      wx.stopPullDownRefresh();
    }).catch(() => {
      wx.stopPullDownRefresh();
    });
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage: function () {
    const { questionDetail } = this.data;
    
    if (questionDetail) {
      return {
        title: questionDetail.title,
        path: `/pages/question/detail/detail?id=${questionDetail.id}`
      };
    }
    
    return {
      title: '选选 - 让大家帮你选一选',
      path: '/pages/index/index'
    };
  },

  /**
   * 取消回复
   */
  cancelReply: function() {
    this.setData({
      replyTo: null,
      replyComment: ''
    });
  },

  /**
   * 删除问题
   */
  deleteQuestion: function() {
    wx.showModal({
      title: '确认删除',
      content: '确定要删除这个问题吗？删除后无法恢复。',
      success: (res) => {
        if (res.confirm) {
          const { id } = this.data;
          
          question.deleteQuestion(id)
            .then(() => {
              wx.showToast({
                title: '删除成功',
                icon: 'success'
              });
              
              setTimeout(() => {
                wx.navigateBack();
              }, 1500);
            })
            .catch(err => {
              console.error('删除问题失败:', err);
              wx.showToast({
                title: '删除失败，请重试',
                icon: 'none'
              });
            });
        }
      }
    });
  },

  /**
   * 关闭问题投票
   */
  closeQuestion: function() {
    wx.showModal({
      title: '确认停止投票',
      content: '确定要停止此问题的投票吗？停止后将无法再接收新的投票。',
      success: (res) => {
        if (res.confirm) {
          const { id } = this.data;
          
          question.closeQuestion(id)
            .then(() => {
              wx.showToast({
                title: '已停止投票',
                icon: 'success'
              });
              
              // 刷新问题详情
              this.loadQuestionDetail();
            })
            .catch(err => {
              console.error('停止投票失败:', err);
              wx.showToast({
                title: '操作失败，请重试',
                icon: 'none'
              });
            });
        }
      }
    });
  },

  /**
   * 滚动到目标回答或评论
   */
  scrollToTarget: function() {
    const { targetAnswerId, targetCommentId, answerList } = this.data;
    
    // 如果目标不存在或回答列表为空，则退出
    if ((!targetAnswerId && !targetCommentId) || !answerList || answerList.length === 0) {
      return;
    }
    
    // 设置延时，确保页面已经渲染完成
    setTimeout(() => {
      // 如果有目标回答，先滚动到回答位置
      if (targetAnswerId) {
        // 查找目标回答在列表中的索引
        const answerIndex = answerList.findIndex(item => item.id === targetAnswerId);
        
        if (answerIndex !== -1) {
          // 滚动到目标回答
          wx.createSelectorQuery()
            .select(`#answer-${targetAnswerId}`)
            .boundingClientRect(rect => {
              if (rect) {
                wx.pageScrollTo({
                  scrollTop: rect.top,
                  duration: 300
                });
                
                // 高亮显示目标回答
                this.highlightElement(`#answer-${targetAnswerId}`);
                
                // 如果有目标评论且目标回答已加载评论，再滚动到评论位置
                if (targetCommentId && this.data.commentList[targetAnswerId]) {
                  // 延时确保评论列表已渲染
                  setTimeout(() => {
                    // 打开评论模态框
                    this.showCommentModal({ currentTarget: { dataset: { answerId: targetAnswerId } } });
                    // 再延时确保模态框渲染完成后滚动到评论
                    setTimeout(() => {
                      this.scrollToModalComment(targetCommentId);
                    }, 500);
                  }, 300);
                } else if (targetCommentId) {
                  // 如果有目标评论但回答尚未加载评论，则先加载评论
                  this.showCommentModal({ currentTarget: { dataset: { answerId: targetAnswerId } } });
                }
              }
            })
            .exec();
        }
      } else if (targetCommentId) {
        // 如果只有目标评论，需要先获取该评论所属的回答，然后滚动
        // 这种情况应该很少见，因为通常通过评论通知跳转时应该已知道回答ID
        // 这里仅作为备用处理
        comment.getCommentInfo(targetCommentId)
          .then(res => {
            if (res.success && res.data) {
              const answerId = res.data.answerId;
              
              // 查找回答并滚动
              const answerIndex = answerList.findIndex(item => item.id === answerId);
              if (answerIndex !== -1) {
                // 先滚动到回答
                this.scrollToAnswer(answerId);
                
                // 延时确保回答滚动完成后再处理评论
                setTimeout(() => {
                  // 打开评论模态框
                  this.showCommentModal({ currentTarget: { dataset: { answerId: answerId } } });
                }, 500);
              }
            }
          })
          .catch(err => {
            console.error('获取评论信息失败:', err);
          });
      }
    }, 500);
  },

  /**
   * 滚动到目标评论
   */
  scrollToComment: function(answerId, commentId) {
    wx.createSelectorQuery()
      .select(`#comment-${commentId}`)
      .boundingClientRect(rect => {
        if (rect) {
          wx.pageScrollTo({
            scrollTop: rect.top,
            duration: 300
          });
          
          // 高亮显示目标评论
          this.highlightElement(`#comment-${commentId}`);
          
          // 为评论模态框中的评论项添加高亮样式
          // 在模态框内找到对应评论添加高亮类
          wx.createSelectorQuery()
            .select(`#comment-${commentId}`)
            .fields({ node: true, size: true })
            .exec(res => {
              if (res[0] && res[0].node) {
                const element = res[0].node;
                element.className += ' highlight-item';
                
                // 3秒后移除高亮
                setTimeout(() => {
                  element.className = element.className.replace(' highlight-item', '');
                }, 3000);
              }
            });
        }
      })
      .exec();
  },

  /**
   * 高亮显示元素
   */
  highlightElement: function(selector) {
    // 添加高亮类
    wx.createSelectorQuery()
      .select(selector)
      .fields({ node: true, size: true })
      .exec(res => {
        if (res[0] && res[0].node) {
          const element = res[0].node;
          element.className += ' highlight-item';
          
          // 3秒后移除高亮
          setTimeout(() => {
            element.className = element.className.replace(' highlight-item', '');
          }, 3000);
        }
      });
  },

  /**
   * 滚动到指定回答
   * @param {String} answerId 回答ID
   */
  scrollToAnswer: function(answerId) {
    // 确保回答列表已加载
    if (!this.data.answerList || this.data.answerList.length === 0) {
      console.log('回答列表为空，无法滚动');
      return;
    }

    // 查找目标回答的索引
    const index = this.data.answerList.findIndex(item => item.id === answerId);
    
    if (index === -1) {
      console.log('未找到目标回答:', answerId);
      return;
    }

    // 使用选择器获取目标回答元素的位置并滚动
    const query = wx.createSelectorQuery();
    query.select(`#answer-${answerId}`).boundingClientRect();
    query.selectViewport().scrollOffset();
    query.exec(res => {
      if (res && res[0] && res[1]) {
        // 计算需要滚动的距离
        const targetTop = res[0].top + res[1].scrollTop - 100; // 减去一个偏移量，使视图更友好
        
        // 执行滚动
        wx.pageScrollTo({
          scrollTop: targetTop,
          duration: 300
        });
        
        // 可选：突出显示目标回答
        this.highlightAnswer(answerId);
      }
    });
  },

  /**
   * 突出显示目标回答
   * @param {String} answerId 回答ID
   */
  highlightAnswer: function(answerId) {
    // 设置目标回答高亮状态
    this.setData({
      highlightedAnswerId: answerId
    });
    
    // 3秒后取消高亮
    setTimeout(() => {
      this.setData({
        highlightedAnswerId: null
      });
    }, 3000);
  },

  /**
   * 在模态框中滚动到目标评论
   */
  scrollToModalComment: function(commentId) {
    if (!commentId) {
      console.log('无效的评论ID');
      return;
    }
    
    // 标记当前评论为高亮
    this.setData({
      highlightedCommentId: commentId
    });
    
    // 3秒后取消高亮
    setTimeout(() => {
      this.setData({
        highlightedCommentId: null
      });
    }, 3000);
    
    // 使用nextTick确保WXML已渲染，防止查询不到元素
    wx.nextTick(() => {
      const query = wx.createSelectorQuery();
      
      // 查找目标评论元素和滚动容器
      query.select(`.comment-modal-body #comment-${commentId}`).boundingClientRect();
      query.select('.comment-modal-body').boundingClientRect();
      query.select('.comment-modal-body').scrollOffset();
      
      query.exec(res => {
        // 检查是否找到所有需要的元素
        if (!res || !res[0]) {
          console.log('未找到目标评论元素:', commentId);
          return;
        }
        
        if (!res[1] || !res[2]) {
          console.log('未找到滚动容器');
          return;
        }
        
        // 计算目标评论在滚动区域中的相对位置
        const commentElement = res[0];
        const scrollView = res[1];
        const scrollData = res[2];
        
        // 计算需要滚动的距离，使评论显示在滚动区域中间位置
        const targetScrollTop = scrollData.scrollTop + (commentElement.top - scrollView.top) - scrollView.height / 4;
        
        // 使用选择器滚动到目标位置
        wx.createSelectorQuery()
          .select('.comment-modal-body')
          .node()
          .exec(res => {
            if (res && res[0] && res[0].node) {
              res[0].node.scrollTop = targetScrollTop;
            }
          });
      });
    });
  },

  /**
   * 问题产品对比
   */
  compareQuestionProducts: async function() {
    try {
      const { id: questionId, questionDetail } = this.data;
      
      // 检查问题是否有效
      if (!questionId || !questionDetail) {
        wx.showToast({
          title: '问题信息不完整',
          icon: 'none'
        });
        return;
      }
      
      // 检查选项数量
      if (!questionDetail.options || questionDetail.options.length < 2) {
        wx.showToast({
          title: '该问题的选项数量不足，无法进行对比',
          icon: 'none'
        });
        return;
      }
      
      console.log('开始问题产品对比，问题ID:', questionId);
      
      // 设置加载状态
      this.setData({
        comparingProducts: true
      });
      
      // 显示加载提示
      wx.showLoading({
        title: '正在分析产品...',
        mask: true
      });
      
      // 调用API获取问题产品对比结果
      const result = await question.compareQuestionProducts(questionId);
      
      console.log('问题产品对比结果:', result);
      
      // 隐藏加载提示
      wx.hideLoading();
      
      // 检查结果
      if (!result.success) {
        throw new Error(result.message || '对比失败');
      }
      
      // 跳转到产品对比页面，传递对比结果
      wx.navigateTo({
        url: `/pages/product/compare/compare?fromQuestion=true&questionId=${questionId}&title=${encodeURIComponent(questionDetail.title || '问题产品对比')}`
      });
      
      // 在跳转成功后，将对比结果存储到全局数据中供对比页面使用
      const app = getApp();
      app.globalData.questionCompareResult = result.data;
      
    } catch (error) {
      console.error('问题产品对比失败:', error);
      
      // 隐藏加载提示
      wx.hideLoading();
      
      // 根据错误类型显示不同的提示
      let errorMessage = '产品对比失败，请稍后重试';
      
      if (error.message) {
        if (error.message.includes('问题不存在')) {
          errorMessage = '该问题不存在或已被删除';
        } else if (error.message.includes('选项不足') || error.message.includes('选项数量不足')) {
          errorMessage = '该问题的选项数量不足，无法进行对比';
        } else if (error.message.includes('找到的可对比产品数量不足')) {
          errorMessage = '该问题下找到的可对比产品数量不足';
        } else if (error.message.includes('只能对比相同类型的产品')) {
          errorMessage = '该问题下的产品类型不一致，无法进行对比';
        } else if (error.message.includes('AI')) {
          errorMessage = 'AI分析服务暂时不可用，请稍后重试';
        } else {
          errorMessage = error.message;
        }
      }
      
      // 显示错误提示
      wx.showModal({
        title: '对比失败',
        content: errorMessage,
        showCancel: false,
        confirmText: '我知道了'
      });
      
    } finally {
      // 重置加载状态
      this.setData({
        comparingProducts: false
      });
    }
  }
}) 