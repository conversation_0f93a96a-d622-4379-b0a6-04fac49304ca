const validator = require('../utils/validator');
const { success, error } = require('../utils/response');
const authService = require('../services/authService');

/**
 * 发送验证码
 * @route POST /api/v1/auth/verify-code
 * @access Public
 */
exports.sendVerifyCode = async (req, res, next) => {
  try {
    // 验证请求数据
    const { error: validationError, value } = validator.sendVerifyCodeSchema.validate(req.body);
    
    if (validationError) {
      const errors = validationError.details.map(detail => ({
        field: detail.path[0],
        message: detail.message
      }));
      
      return error(res, 400, '请求参数错误', errors);
    }
    
    // 发送验证码
    const result = await authService.sendVerifyCode(value.phone, value.purpose);
    
    // 返回成功响应
    return success(res, 200, '验证码已发送', result);
  } catch (err) {
    next(err);
  }
};

/**
 * 手机号一键认证（登录/注册）
 * @route POST /api/v1/auth/phone-auth
 * @access Public
 */
exports.phoneAuth = async (req, res, next) => {
  try {
    console.log('收到手机号认证请求:', {
      phone: req.body.phone,
      timestamp: new Date().toISOString()
    });

    // 验证请求数据
    const { error: validationError, value } = validator.phoneAuthSchema.validate(req.body);
    
    if (validationError) {
      const errors = validationError.details.map(detail => ({
        field: detail.path[0],
        message: detail.message
      }));
      
      console.log('手机号认证参数验证失败:', errors);
      return error(res, 400, '请求参数错误', errors);
    }

    // 处理手机号认证
    const result = await authService.phoneAuth(value);

    console.log('手机号认证成功:', {
      userId: result.user.id,
      isNewUser: result.isNewUser,
      timestamp: new Date().toISOString()
    });

    // 返回成功响应
    const message = result.isNewUser ? '注册成功' : '登录成功';
    return success(res, 200, message, result);
  } catch (err) {
    console.error('手机号认证控制器错误:', {
      error: err,
      body: req.body,
      timestamp: new Date().toISOString()
    });

    // 如果是业务逻辑错误，直接返回
    if (err.statusCode) {
      return error(res, err.statusCode, err.message, err.details ? [err.details] : undefined);
    }

    // 传递给全局错误处理中间件
    next(err);
  }
};

/**
 * 微信小程序登录
 * @route POST /api/v1/auth/wx-login
 * @access Public
 */
exports.wxLogin = async (req, res, next) => {
  try {
    console.log('收到微信登录请求:', {
      timestamp: new Date().toISOString()
    });

    // 验证请求数据
    const { error: validationError, value } = validator.wxLoginSchema.validate(req.body);
    
    if (validationError) {
      const errors = validationError.details.map(detail => ({
        field: detail.path[0],
        message: detail.message
      }));
      
      console.log('微信登录参数验证失败:', errors);
      return error(res, 400, '请求参数错误', errors);
    }

    // 处理微信登录
    const result = await authService.wxLogin(value.code);

    console.log('微信登录成功:', {
      userId: result.user.id,
      isNewUser: result.isNewUser,
      timestamp: new Date().toISOString()
    });

    // 返回成功响应
    const message = result.isNewUser ? '注册成功' : '登录成功';
    return success(res, 200, message, result);
  } catch (err) {
    console.error('微信登录控制器错误:', {
      error: err,
      timestamp: new Date().toISOString()
    });

    // 如果是业务逻辑错误，直接返回
    if (err.statusCode) {
      return error(res, err.statusCode, err.message, err.details ? [err.details] : undefined);
    }

    // 传递给全局错误处理中间件
    next(err);
  }
};

/**
 * 刷新令牌
 * @route POST /api/v1/auth/refresh-token
 * @access Public
 */
exports.refreshToken = async (req, res, next) => {
  try {
    // 验证请求数据
    const { error: validationError, value } = validator.refreshTokenSchema.validate(req.body);
    
    if (validationError) {
      const errors = validationError.details.map(detail => ({
        field: detail.path[0],
        message: detail.message
      }));
      
      return error(res, 400, '请求参数错误', errors);
    }

    // 刷新令牌
    const result = await authService.refreshToken(value.refreshToken);

    // 返回成功响应
    return success(res, 200, '令牌已更新', result);
  } catch (err) {
    next(err);
  }
};

/**
 * 退出登录
 * @route POST /api/v1/auth/logout
 * @access Private
 */
exports.logout = async (req, res, next) => {
  try {
    // 退出登录
    await authService.logout(req.user.id);

    // 返回成功响应
    return success(res, 200, '已成功退出登录');
  } catch (err) {
    next(err);
  }
};

/**
 * 获取当前用户信息
 * @route GET /api/v1/auth/me
 * @access Private
 */
exports.getCurrentUser = async (req, res, next) => {
  try {
    // 获取当前用户信息
    const user = await authService.getCurrentUser(req.user.id);

    // 返回成功响应
    return success(res, 200, null, user);
  } catch (err) {
    next(err);
  }
}; 