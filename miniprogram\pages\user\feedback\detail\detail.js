const { feedback } = require('../../../../utils/api');
const util = require('../../../../utils/util');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    feedbackId: '',
    feedbackDetail: null,
    loading: true,
    error: null,
    showDeviceInfo: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '反馈详情'
    });
    
    if (options.id) {
      this.setData({
        feedbackId: options.id
      });
      this.loadFeedbackDetail();
    } else {
      this.setData({
        error: '缺少反馈ID',
        loading: false
      });
    }
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    this.loadFeedbackDetail();
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 500);
  },

  /**
   * 加载反馈详情
   */
  loadFeedbackDetail: function() {
    this.setData({ loading: true, error: null });
    
    feedback.getFeedbackDetail(this.data.feedbackId)
      .then(res => {
        this.setData({ loading: false });
        
        if (res.success && res.data) {
          this.setData({
            feedbackDetail: res.data
          });
        } else {
          throw new Error(res.message || '获取反馈详情失败');
        }
      })
      .catch(err => {
        this.setData({ 
          loading: false,
          error: err.message || '获取反馈详情失败'
        });
        console.error('获取反馈详情失败:', err);
        util.showToast(err.message || '获取反馈详情失败');
      });
  },

  /**
   * 预览图片
   */
  previewImage: function(e) {
    const index = e.currentTarget.dataset.index;
    const urls = this.data.feedbackDetail.images;
    
    wx.previewImage({
      current: urls[index],
      urls: urls
    });
  },

  /**
   * 复制反馈内容
   */
  copyContent: function() {
    if (!this.data.feedbackDetail) return;
    
    wx.setClipboardData({
      data: this.data.feedbackDetail.content,
      success: () => {
        util.showToast('内容已复制', 'success');
      },
      fail: () => {
        util.showToast('复制失败');
      }
    });
  },

  /**
   * 获取反馈类型显示文本
   */
  getTypeText: function(type) {
    const typeMap = {
      'bug': '问题反馈',
      'suggestion': '建议反馈',
      'question': '疑问咨询',
      'other': '其他反馈'
    };
    return typeMap[type] || '其他反馈';
  },

  /**
   * 获取反馈状态显示文本
   */
  getStatusText: function(status) {
    const statusMap = {
      'pending': '待处理',
      'processing': '处理中',
      'resolved': '已解决',
      'rejected': '已拒绝'
    };
    return statusMap[status] || '待处理';
  },

  /**
   * 获取反馈状态样式类名
   */
  getStatusClass: function(status) {
    const classMap = {
      'pending': 'status-pending',
      'processing': 'status-processing',
      'resolved': 'status-resolved',
      'rejected': 'status-rejected'
    };
    return classMap[status] || 'status-pending';
  },

  /**
   * 返回反馈列表
   */
  goBack: function() {
    wx.navigateBack();
  },

  /**
   * 切换设备信息显示状态
   */
  toggleDeviceInfo: function() {
    this.setData({
      showDeviceInfo: !this.data.showDeviceInfo
    });
  }
}); 