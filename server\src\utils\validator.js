const Joi = require('joi');

/**
 * 发送验证码验证模式
 */
exports.sendVerifyCodeSchema = Joi.object({
  phone: Joi.string()
    .pattern(/^1[3-9]\d{9}$/)
    .required()
    .messages({
      'string.empty': '手机号不能为空',
      'string.pattern.base': '请输入有效的手机号',
      'any.required': '请输入手机号'
    }),
  purpose: Joi.string().valid('login', 'register', 'auth').required().messages({
    'string.empty': '验证码类型不能为空',
    'any.required': '请提供验证码类型'
  })
});

/**
 * 手机号一键认证验证模式
 */
exports.phoneAuthSchema = Joi.object({
  phone: Joi.string()
    .pattern(/^1[3-9]\d{9}$/)
    .required()
    .messages({
      'string.empty': '手机号不能为空',
      'string.pattern.base': '请输入有效的手机号',
      'any.required': '请输入手机号'
    }),
  verifyCode: Joi.string()
    .length(6)
    .required()
    .messages({
      'string.empty': '验证码不能为空',
      'string.length': '验证码必须是6位数字',
      'any.required': '请输入验证码'
    })
});

/**
 * 微信登录验证模式
 */
exports.wxLoginSchema = Joi.object({
  code: Joi.string()
    .required()
    .messages({
      'string.empty': '微信授权码不能为空',
      'any.required': '请提供微信授权码'
    })
});

/**
 * 刷新令牌验证模式
 */
exports.refreshTokenSchema = Joi.object({
  refreshToken: Joi.string().required().messages({
    'string.empty': '刷新令牌不能为空',
    'any.required': '请提供刷新令牌'
  })
});

/**
 * 更新用户资料验证模式
 */
exports.updateProfileSchema = Joi.object({
  nickname: Joi.string()
    .min(2)
    .max(20)
    .trim()
    .optional()
    .messages({
      'string.min': '昵称至少需要2个字符',
      'string.max': '昵称不能超过20个字符',
      'string.empty': '昵称不能为空'
    }),
  avatar: Joi.string()
    .allow('')
    .optional()
    .messages({
      'string.base': '头像必须是字符串'
    }),
  gender: Joi.string()
    .valid('male', 'female', 'secret')
    .optional()
    .messages({
      'any.only': '性别只能是 male、female 或 secret'
    }),
  age: Joi.number()
    .integer()
    .min(0)
    .max(120)
    .optional()
    .messages({
      'number.base': '年龄必须是数字',
      'number.integer': '年龄必须是整数',
      'number.min': '年龄不能为负数',
      'number.max': '年龄不合理'
    }),
  occupation: Joi.string()
    .allow('')
    .max(50)
    .optional()
    .messages({
      'string.max': '职业描述不能超过50个字符'
    }),
  region: Joi.string()
    .allow('')
    .max(50)
    .optional()
    .messages({
      'string.max': '地区信息不能超过50个字符'
    })
});

/**
 * 发送修改手机号验证码验证模式
 */
exports.sendChangePhoneCodeSchema = Joi.object({
  newPhone: Joi.string()
    .pattern(/^1[3-9]\d{9}$/)
    .required()
    .messages({
      'string.empty': '新手机号不能为空',
      'string.pattern.base': '请输入有效的手机号',
      'any.required': '请输入新手机号'
    })
});

/**
 * 修改手机号验证模式
 */
exports.changePhoneSchema = Joi.object({
  newPhone: Joi.string()
    .pattern(/^1[3-9]\d{9}$/)
    .required()
    .messages({
      'string.empty': '新手机号不能为空',
      'string.pattern.base': '请输入有效的手机号',
      'any.required': '请输入新手机号'
    }),
  verifyCode: Joi.string()
    .length(6)
    .required()
    .messages({
      'string.empty': '验证码不能为空',
      'string.length': '验证码必须是6位数字',
      'any.required': '请输入验证码'
    })
});