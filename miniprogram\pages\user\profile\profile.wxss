/* pages/user/profile/profile.wxss */

/**
 * 用户资料页面样式
 * Profile Page Styles
 * 
 * 设计原则：
 * 1. 使用全局样式系统，避免重复定义
 * 2. 依赖全局组件样式：.btn, .form-group, .form-label, .form-input-wrap, .form-input, .form-error
 * 3. 使用全局间距系统：.mt-*, .mb-*, .p-*, .px-*, .py-*
 * 4. 使用全局头像系统：.avatar, .avatar-xl
 * 5. 专注于页面特有的组件样式
 * 
 * 依赖：全局样式系统 (variables.wxss, components.wxss, utilities.wxss)
 */

/* ==================== 头像区域 Avatar Section ==================== */
/* 使用全局样式：.avatar-xl, .text-center, .text-secondary, .font-size-small, .mt-10 */

.avatar-section .avatar {
  /* 继承全局 .avatar 和 .avatar-xl 样式 */
  border: 4rpx solid #f0f0f0;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.avatar-tip {
  opacity: 0.8;
}

/* ==================== 性别选择器 Gender Selector ==================== */

.gender-text-selector {
  display: flex;
  gap: 20rpx;
  margin-top: 10rpx;
}

.gender-text-option {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 24rpx 20rpx;
  background-color: #f8f9fa;
  border: 2rpx solid #e9ecef;
  border-radius: 12rpx;
  transition: all 0.3s ease;
  cursor: pointer;
  font-size: 28rpx;
  color: #666666;
}

.gender-text-option:active {
  transform: scale(0.98);
}

.gender-text-option.active {
  background-color: #e6f3ff;
  border-color: #3B7ADB;
  box-shadow: 0 2rpx 8rpx rgba(59, 122, 219, 0.15);
  color: #3B7ADB;
  font-weight: bold;
}

/* ==================== 响应式设计 Responsive Design ==================== */

/* 小屏设备优化 */
@media (max-width: 375px) {
  .gender-text-option {
    padding: 16rpx 8rpx;
  }
  
  .gender-text-option {
    font-size: 28rpx;
  }
}

/* 大屏设备优化 */
@media (min-width: 414px) {
  .container {
    max-width: 750rpx;
    margin: 0 auto;
  }
}

/* ==================== 页面动画效果 Page Animation ==================== */

.page {
  animation: fadeInUp 0.3s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* ==================== 表单焦点增强 Form Focus Enhancement ==================== */

.form-input-wrap:focus-within {
  animation: focusGlow 0.3s ease;
}

@keyframes focusGlow {
  0% {
    box-shadow: 0 0 0 0 rgba(59, 122, 219, 0.3);
  }
  50% {
    box-shadow: 0 0 0 8rpx rgba(59, 122, 219, 0.15);
  }
  100% {
    box-shadow: 0 0 0 4rpx rgba(59, 122, 219, 0.1);
  }
}

/* ==================== 深色模式适配 Dark Mode Support ==================== */

@media (prefers-color-scheme: dark) {
  .avatar-section .avatar {
    border-color: #333333;
  }
  
  .gender-text-option {
    background-color: #2a2a2a;
    border-color: #444444;
  }
  
  .gender-text-option.active {
    background-color: #1a2b4d;
    border-color: #3B7ADB;
  }
} 