const express = require('express');
const { protect } = require('../middlewares/auth');

const router = express.Router();

// 所有路由都需要认证
router.use(protect);

// 上传相关路由占位
router.post('/images', (req, res) => {
  res.status(501).json({
    success: false,
    code: 501,
    message: '图片上传功能尚未实现',
    timestamp: Date.now()
  });
});

router.post('/avatar', (req, res) => {
  res.status(501).json({
    success: false,
    code: 501,
    message: '头像上传功能尚未实现',
    timestamp: Date.now()
  });
});

module.exports = router; 