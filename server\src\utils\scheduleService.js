/**
 * 定时任务服务
 * 负责处理各种定时任务的执行
 */

const cron = require('node-cron');
const Question = require('../models/Question');
const CacheManager = require('./cacheManager');

/**
 * 检查并关闭过期问题
 * @returns {Promise<Object>} 关闭过期问题的结果
 */
const checkExpiredQuestions = async () => {
  try {
    console.log('🔄 执行定时任务: 检查过期问题 - ' + new Date().toISOString());
    
    // 获取当前时间之前的过期问题
    const now = new Date();
    const result = await Question.closeExpiredQuestions();
    
    if (result.modifiedCount > 0) {
      console.log(`✅ 成功关闭 ${result.modifiedCount} 个过期问题`);
    } else {
      console.log('✅ 没有需要关闭的过期问题');
    }
    
    return result;
  } catch (error) {
    console.error('❌ 检查过期问题时出错:', error);
    throw error;
  }
};

/**
 * 清理过期缓存
 * @returns {Promise<Object>} 清理缓存的结果
 */
const cleanupExpiredCaches = async () => {
  try {
    console.log('🧹 执行定时任务: 清理过期缓存 - ' + new Date().toISOString());
    
    const result = await CacheManager.scheduledCleanup();
    
    if (result.cleanup.success) {
      const deletedCount = result.cleanup.deletedCount;
      const remainingCount = result.stats?.overview?.active || 0;
      
      if (deletedCount > 0) {
        console.log(`✅ 成功清理 ${deletedCount} 个过期缓存，剩余活跃缓存 ${remainingCount} 个`);
      } else {
        console.log(`✅ 没有需要清理的过期缓存，当前活跃缓存 ${remainingCount} 个`);
      }
    } else {
      console.error('❌ 缓存清理失败:', result.cleanup.error);
    }
    
    return result;
  } catch (error) {
    console.error('❌ 清理过期缓存时出错:', error);
    return {
      cleanup: {
        success: false,
        error: error.message,
        deletedCount: 0
      },
      stats: null
    };
  }
};

/**
 * 统计并输出系统健康状态
 * @returns {Promise<Object>} 系统统计信息
 */
const reportSystemHealth = async () => {
  try {
    console.log('📊 执行定时任务: 系统健康检查 - ' + new Date().toISOString());
    
    // 获取缓存统计
    const cacheStats = await CacheManager.getDetailedCacheStats();
    
    if (cacheStats) {
      console.log('📈 缓存系统状态:');
      console.log(`   - 活跃缓存: ${cacheStats.overview.active} 个`);
      console.log(`   - 缓存命中率: ${cacheStats.overview.hitRate}`);
      console.log(`   - 总缓存大小: ${cacheStats.performance.totalSizeMB}MB`);
      console.log(`   - 平均热度得分: ${cacheStats.performance.averageHeatScore}`);
      
      // 输出热门对比排行前3
      if (cacheStats.performance.topHotCaches && cacheStats.performance.topHotCaches.length > 0) {
        console.log('🔥 热门对比排行 (Top 3):');
        cacheStats.performance.topHotCaches.slice(0, 3).forEach((cache, index) => {
          console.log(`   ${index + 1}. ${cache.productInfo.productNames.join(' vs ')} (热度: ${cache.hotness.heatScore.toFixed(2)})`);
        });
      }
    }
    
    return {
      timestamp: new Date().toISOString(),
      cacheStats
    };
  } catch (error) {
    console.error('❌ 系统健康检查时出错:', error);
    throw error;
  }
};

/**
 * 初始化所有定时任务
 */
const initScheduleTasks = () => {
  console.log('📅 正在初始化定时任务服务...');
  
  // 每小时检查一次过期问题 - 在每小时的第0分钟执行
  cron.schedule('0 * * * *', async () => {
    try {
      await checkExpiredQuestions();
    } catch (error) {
      console.error('❌ 执行过期问题检查任务出错:', error);
    }
  });

  // 每天凌晨2点执行缓存清理任务
  cron.schedule('0 11 * * *', async () => {
    try {
      await cleanupExpiredCaches();
    } catch (error) {
      console.error('❌ 执行缓存清理任务出错:', error);
    }
  });

  // 每天早上8点执行系统健康检查
  cron.schedule('0 8 * * *', async () => {
    try {
      await reportSystemHealth();
    } catch (error) {
      console.error('❌ 执行系统健康检查任务出错:', error);
    }
  });

  // 在启动时立即执行一次检查
  Promise.all([
    checkExpiredQuestions()
      .then(() => console.log('✅ 启动时检查过期问题完成'))
      .catch(err => console.error('❌ 启动时检查过期问题失败:', err)),
    
    reportSystemHealth()
      .then(() => console.log('✅ 启动时系统健康检查完成'))
      .catch(err => console.error('❌ 启动时系统健康检查失败:', err))
  ]);

  console.log('✅ 定时任务服务初始化成功');
  console.log('📋 已注册的定时任务:');
  console.log('   - 过期问题检查: 每小时执行 (0 * * * *)');
  console.log('   - 缓存清理: 每天凌晨2点 (0 2 * * *)');
  console.log('   - 系统健康检查: 每天早上8点 (0 8 * * *)');
};

module.exports = {
  initScheduleTasks,
  checkExpiredQuestions,
  cleanupExpiredCaches,
  reportSystemHealth
}; 