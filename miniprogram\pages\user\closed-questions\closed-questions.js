const app = getApp();
const { user } = require('../../../utils/api');
const util = require('../../../utils/util');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    questionList: [],
    loading: false,
    page: 1,
    limit: 10,
    hasMore: true,
    refreshing: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.loadClosedQuestions(true);
  },

  /**
   * 加载用户已完成的问题
   */
  loadClosedQuestions: function(refresh = false) {
    if (this.data.loading) return;
    
    if (refresh) {
      this.setData({
        page: 1,
        hasMore: true,
        refreshing: true
      });
    }

    if (!this.data.hasMore && !refresh) return;

    this.setData({ loading: true });
    
    // 显示加载提示
    if (!this.data.refreshing) {
      wx.showLoading({
        title: '加载中...',
      });
    }
    
    user.getClosedQuestions({
      page: this.data.page,
      limit: this.data.limit
    }).then(res => {
      // 隐藏加载提示
      wx.hideLoading();
      
      console.log('已完成问题列表返回数据:', res);
      
      // 检查数据结构是否符合预期
      if (!res.data || !res.data.questions) {
        throw new Error('返回数据格式不正确');
      }
      
      const { questions, pagination } = res.data;
      const { total } = pagination;
      const hasMore = this.data.page * this.data.limit < total;
      
      // 处理时间格式化
      const formattedQuestions = questions.map(item => {
        if (item.createdAt) {
          item.createdAt = util.friendlyTime(new Date(item.createdAt));
        }
        return item;
      });
      
      if (refresh) {
        this.setData({
          questionList: formattedQuestions,
          hasMore,
          page: this.data.page + 1,
          loading: false,
          refreshing: false
        });
      } else {
        this.setData({
          questionList: [...this.data.questionList, ...formattedQuestions],
          hasMore,
          page: this.data.page + 1,
          loading: false
        });
      }
    }).catch(err => {
      // 隐藏加载提示
      wx.hideLoading();
      
      console.error('加载已完成问题列表失败:', err);
      this.setData({ 
        loading: false,
        refreshing: false
      });
      wx.showToast({
        title: '加载失败，请重试',
        icon: 'none'
      });
    });
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh: function() {
    this.loadClosedQuestions(true);
    wx.stopPullDownRefresh();
  },

  /**
   * 上拉加载更多
   */
  onReachBottom: function() {
    this.loadClosedQuestions();
  },

  /**
   * 处理问题点击事件
   */
  onQuestionTap: function(e) {
    const id = e.detail.id;
    wx.navigateTo({
      url: `/pages/question/detail/detail?id=${id}`
    });
  }
}) 