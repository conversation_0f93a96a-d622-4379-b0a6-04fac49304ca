<!--pages/product/product_compare_v4/product_compare_v4.wxml-->
<wxs module="utils">
  // 格式化价格显示
  var formatPrice = function(price) {
    if (!price || price <= 0) {
      return '暂无价格';
    }
    return '¥' + price;
  };
  
  // 截取文本长度
  var truncateText = function(text, maxLength) {
    if (!text) return '';
    if (text.length <= maxLength) return text;
    return text.substring(0, maxLength) + '...';
  };
  
  // 判断是否为推荐产品
  var isRecommended = function(productName, recommendedProduct) {
    return productName === recommendedProduct;
  };
  
  module.exports = {
    formatPrice: formatPrice,
    truncateText: truncateText,
    isRecommended: isRecommended
  };
</wxs>

<view class="container">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-spinner"></view>
    <text class="loading-text">正在分析产品对比...</text>
  </view>

  <!-- 错误状态 -->
  <view wx:elif="{{error}}" class="error-container">
    <view class="error-icon">⚠️</view>
    <text class="error-message">{{error}}</text>
    <button class="retry-btn" bindtap="onRetry">重新加载</button>
    <button class="test-btn" bindtap="loadTestData" style="margin-top: 20rpx; background: #007aff;">加载测试数据</button>
  </view>

  <!-- 对比结果 -->
  <view wx:elif="{{compareResult}}" class="compare-result">
    
    <!-- 对比产品头部 -->
    <view class="products-header">
      <view class="header-title">
        <text class="title-icon">⚖️</text>
        <text class="title-text">产品对比</text>
      </view>
      
      <!-- 产品列表 -->
      <scroll-view class="products-scroll" scroll-x="{{true}}">
        <view class="products-list">
          <view 
            class="product-card"
            wx:for="{{compareResult.products}}"
            wx:key="skuName"
            data-product-name="{{item.skuName}}"
            bindtap="viewProductDetail"
          >
            <view class="product-image-container">
              <image 
                class="product-image" 
                src="{{item.imageUrl}}" 
                mode="aspectFit"
                data-image-url="{{item.imageUrl}}"
                bindtap="previewProductImage"
                lazy-load
              />
              <view class="image-placeholder" wx:if="{{!item.imageUrl}}">
                <text>暂无图片</text>
              </view>
            </view>
            <view class="product-name">{{utils.truncateText(item.skuName, 20)}}</view>
          </view>
        </view>
      </scroll-view>
      
      <!-- 操作按钮 -->
      <view class="header-actions">
        <button class="action-btn share-btn" bindtap="shareCompareResult">
          <text class="btn-icon">📤</text>
          <text class="btn-text">分享</text>
        </button>
      </view>
    </view>



    <!-- AI分析结果 -->
    <view wx:if="{{compareResult.aiAnalysis}}" class="ai-analysis">
      
      <!-- 对比摘要 -->
      <view wx:if="{{compareResult.aiAnalysis.structuredReport.summary}}" class="summary-section">
        <view class="section-header">
          <text class="section-title">📊 对比摘要</text>
        </view>
        <view class="summary-content">
          <view wx:if="{{compareResult.aiAnalysis.structuredReport.summary.title}}" class="summary-title">{{compareResult.aiAnalysis.structuredReport.summary.title}}</view>
          <view class="summary-meta">
            <text wx:if="{{compareResult.aiAnalysis.structuredReport.summary.productCount}}" class="meta-item">产品数量: {{compareResult.aiAnalysis.structuredReport.summary.productCount}}</text>
            <text wx:if="{{compareResult.aiAnalysis.structuredReport.summary.category}}" class="meta-item">类别: {{compareResult.aiAnalysis.structuredReport.summary.category}}</text>
          </view>
          
          <!-- 关键差异 -->
          <view wx:if="{{compareResult.aiAnalysis.structuredReport.summary.keyDifferences && compareResult.aiAnalysis.structuredReport.summary.keyDifferences.length > 0}}" class="key-differences">
            <view class="differences-title">🔍 关键差异</view>
            <view class="differences-list">
              <view 
                class="difference-item"
                wx:for="{{compareResult.aiAnalysis.structuredReport.summary.keyDifferences}}"
                wx:key="index"
              >
                <text class="difference-text">{{item}}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 技术规格对比 -->
      <view wx:if="{{compareResult.aiAnalysis.structuredReport.technicalSpecs && compareResult.aiAnalysis.structuredReport.technicalSpecs.length > 0}}" class="specs-section">
        <view 
          class="main-section-header {{expandedMainSections.technicalSpecs ? 'expanded' : ''}}"
          bindtap="toggleMainSection"
          data-section="technicalSpecs"
        >
          <text class="section-title">🔧 技术规格对比</text>
          <view class="main-section-toggle">
            <text class="toggle-icon">{{expandedMainSections.technicalSpecs ? '▼' : '▶'}}</text>
          </view>
        </view>
        
        <view class="main-section-content {{expandedMainSections.technicalSpecs ? 'show' : 'hide'}}">
          <view 
            class="spec-category"
            wx:for="{{compareResult.aiAnalysis.structuredReport.technicalSpecs}}"
            wx:key="category"
          >
            <view 
              class="category-header {{expandedCategories[item.category] ? 'expanded' : ''}}"
              bindtap="toggleCategory"
              data-category="{{item.category}}"
            >
              <text class="category-name">{{item.category}}</text>
              <view class="category-toggle">
                <text class="toggle-icon">{{expandedCategories[item.category] ? '▼' : '▶'}}</text>
              </view>
            </view>
            
            <view class="category-content {{expandedCategories[item.category] ? 'show' : 'hide'}}">
              <view 
                class="spec-item"
                wx:for="{{item.items}}"
                wx:key="name"
                wx:for-item="specItem"
              >
                <view class="spec-name">{{specItem.name}}</view>
                
                <!-- 产品参数值对比 -->
                <view class="spec-values">
                  <view 
                    class="value-item"
                    wx:for="{{compareResult.products}}"
                    wx:key="skuName"
                    wx:for-item="product"
                  >
                    <view class="product-label">{{utils.truncateText(product.skuName, 15)}}</view>
                    <view class="product-value">{{specItem.productValues[product.skuName] || '暂无数据'}}</view>
                  </view>
                </view>
                
                <!-- 分析说明 -->
                <view class="spec-analysis" wx:if="{{specItem.analysis}}">
                  <text class="analysis-text">{{specItem.analysis}}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 优缺点分析 -->
      <view wx:if="{{compareResult.aiAnalysis.structuredReport.prosAndCons && compareResult.aiAnalysis.structuredReport.prosAndCons.length > 0}}" class="pros-cons-section">
        <view 
          class="main-section-header {{expandedMainSections.prosAndCons ? 'expanded' : ''}}"
          bindtap="toggleMainSection"
          data-section="prosAndCons"
        >
          <text class="section-title">⚖️ 优缺点分析</text>
          <view class="main-section-toggle">
            <text class="toggle-icon">{{expandedMainSections.prosAndCons ? '▼' : '▶'}}</text>
          </view>
        </view>
        
        <view class="main-section-content {{expandedMainSections.prosAndCons ? 'show' : 'hide'}}">
          <view 
            class="product-analysis"
            wx:for="{{compareResult.aiAnalysis.structuredReport.prosAndCons}}"
            wx:key="productName"
          >
            <view 
              class="product-analysis-header {{expandedProsAndCons[item.productName] ? 'expanded' : ''}}"
              bindtap="toggleProsAndCons"
              data-product-name="{{item.productName}}"
            >
              <text class="product-name">{{item.productName}}</text>
              <view class="product-toggle">
                <text class="toggle-icon">{{expandedProsAndCons[item.productName] ? '▼' : '▶'}}</text>
              </view>
            </view>
            
            <view class="product-analysis-content {{expandedProsAndCons[item.productName] ? 'show' : 'hide'}}">
              <!-- 优点 -->
              <view class="pros-section">
                <view class="pros-title">
                  <text class="pros-icon">✅</text>
                  <text class="pros-text">优点</text>
                </view>
                <view class="pros-list">
                  <view 
                    class="pro-item"
                    wx:for="{{item.pros}}"
                    wx:key="index"
                    wx:for-item="pro"
                  >
                    <text class="pro-text">{{pro}}</text>
                  </view>
                </view>
              </view>
              
              <!-- 缺点 -->
              <view class="cons-section">
                <view class="cons-title">
                  <text class="cons-icon">❌</text>
                  <text class="cons-text">缺点</text>
                </view>
                <view class="cons-list">
                  <view 
                    class="con-item"
                    wx:for="{{item.cons}}"
                    wx:key="index"
                    wx:for-item="con"
                  >
                    <text class="con-text">{{con}}</text>
                  </view>
                </view>
              </view>
              
              <!-- 总体评价 -->
              <view class="overall-rating">
                <view class="rating-title">💡 总体评价</view>
                <text class="rating-text">{{item.overallRating}}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 使用场景推荐 -->
      <view wx:if="{{compareResult.aiAnalysis.structuredReport.usageScenarios && compareResult.aiAnalysis.structuredReport.usageScenarios.length > 0}}" class="scenarios-section">
        <view 
          class="main-section-header {{expandedMainSections.usageScenarios ? 'expanded' : ''}}"
          bindtap="toggleMainSection"
          data-section="usageScenarios"
        >
          <text class="section-title">🎯 使用场景推荐</text>
          <view class="main-section-toggle">
            <text class="toggle-icon">{{expandedMainSections.usageScenarios ? '▼' : '▶'}}</text>
          </view>
        </view>
        
        <view class="main-section-content {{expandedMainSections.usageScenarios ? 'show' : 'hide'}}">
          <view 
            class="scenario-item"
            wx:for="{{compareResult.aiAnalysis.structuredReport.usageScenarios}}"
            wx:key="scenario"
          >
            <view 
              class="scenario-header {{expandedScenarios[item.scenario] ? 'expanded' : ''}}"
              bindtap="toggleScenario"
              data-scenario="{{item.scenario}}"
            >
              <text class="scenario-name">{{item.scenario}}</text>
              <view class="scenario-toggle">
                <text class="toggle-icon">{{expandedScenarios[item.scenario] ? '▼' : '▶'}}</text>
              </view>
            </view>
            
            <view class="scenario-content {{expandedScenarios[item.scenario] ? 'show' : 'hide'}}">
              <view class="scenario-description">
                <text class="description-text">{{item.description}}</text>
              </view>
              
              <view class="scenario-recommendation">
                <view class="recommended-product">
                  <text class="recommend-label">推荐产品:</text>
                  <text class="recommend-product">{{item.recommendedProduct}}</text>
                </view>
                <view class="recommend-reason">
                  <text class="reason-label">推荐理由:</text>
                  <text class="reason-text">{{item.reason}}</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- 购买建议 -->
      <view wx:if="{{compareResult.aiAnalysis.structuredReport.purchaseAdvice}}" class="purchase-advice-section">
        <view 
          class="main-section-header {{expandedMainSections.purchaseAdvice ? 'expanded' : ''}}"
          bindtap="toggleMainSection"
          data-section="purchaseAdvice"
        >
          <text class="section-title">💰 购买建议</text>
          <view class="main-section-toggle">
            <text class="toggle-icon">{{expandedMainSections.purchaseAdvice ? '▼' : '▶'}}</text>
          </view>
        </view>
        
        <view class="main-section-content {{expandedMainSections.purchaseAdvice ? 'show' : 'hide'}}">
          <!-- 具体建议 -->
          <view class="advice-subsection">
            <view 
              class="advice-subsection-header {{expandedPurchaseAdvice.specificAdvice ? 'expanded' : ''}}"
              bindtap="togglePurchaseAdviceSection"
              data-section="specificAdvice"
            >
              <text class="subsection-title">👥 用户群体建议</text>
              <view class="subsection-toggle">
                <text class="toggle-icon">{{expandedPurchaseAdvice.specificAdvice ? '▼' : '▶'}}</text>
              </view>
            </view>
            
            <view class="advice-subsection-content {{expandedPurchaseAdvice.specificAdvice ? 'show' : 'hide'}}">
              <view 
                class="advice-item"
                wx:for="{{compareResult.aiAnalysis.structuredReport.purchaseAdvice.specificAdvice}}"
                wx:key="userType"
              >
                <view class="user-type">{{item.userType}}</view>
                <view class="recommendation">{{item.recommendation}}</view>
              </view>
            </view>
          </view>
          
          <!-- 重要提醒 -->
          <view class="advice-subsection">
            <view 
              class="advice-subsection-header {{expandedPurchaseAdvice.importantNotes ? 'expanded' : ''}}"
              bindtap="togglePurchaseAdviceSection"
              data-section="importantNotes"
            >
              <text class="subsection-title">⚠️ 重要提醒</text>
              <view class="subsection-toggle">
                <text class="toggle-icon">{{expandedPurchaseAdvice.importantNotes ? '▼' : '▶'}}</text>
              </view>
            </view>
            
            <view class="advice-subsection-content {{expandedPurchaseAdvice.importantNotes ? 'show' : 'hide'}}">
              <view 
                class="note-item"
                wx:for="{{compareResult.aiAnalysis.structuredReport.purchaseAdvice.importantNotes}}"
                wx:key="index"
              >
                <text class="note-text">{{item}}</text>
              </view>
            </view>
          </view>
        </view>
      </view>

      <!-- AI分析结果评分 - 放在最后 -->
      <view wx:if="{{compareResult && compareResult.comparisonCacheId}}" class="rating-section">
        <view class="section-header">
          <text class="section-title">⭐ AI分析结果评分</text>
        </view>
        <view class="rating-content">
          <rate-score
            target-id="{{compareResult.comparisonCacheId}}"
            rating-type="comparison"
            average-rating="{{compareResult.ratingStats ? compareResult.ratingStats.averageRating : 0}}"
            total-ratings="{{compareResult.ratingStats ? compareResult.ratingStats.totalRatings : 0}}"
            user-rating="{{compareResult.userRating ? compareResult.userRating.rating : null}}"
            has-rated="{{compareResult.userRating ? compareResult.userRating.hasRated : false}}"
            show-stats="{{true}}"
            size="medium"
            disabled="{{false}}"
            panel-title="为这个AI分析结果评分"
            success-message="评分成功"
            bind:submitRating="onSubmitRating"
            bind:ratingUpdated="onRatingUpdated"
          />
        </view>
      </view>

    </view>
  </view>

  <!-- 空状态 -->
  <view wx:else class="empty-container">
    <view class="empty-icon">📱</view>
    <view class="empty-title">暂无对比数据</view>
    <view class="empty-desc">请返回产品库选择产品进行对比</view>
  </view>
</view>