/**
 * 常用工具函数
 */

/**
 * 格式化时间
 * @param {Date} date 日期对象
 * @param {string} format 格式
 * @returns {string} 格式化后的时间
 */
const formatTime = (date, format = 'YYYY-MM-DD HH:mm:ss') => {
  const year = date.getFullYear();
  const month = date.getMonth() + 1;
  const day = date.getDate();
  const hour = date.getHours();
  const minute = date.getMinutes();
  const second = date.getSeconds();

  format = format.replace(/YYYY/g, year);
  format = format.replace(/MM/g, formatNumber(month));
  format = format.replace(/DD/g, formatNumber(day));
  format = format.replace(/HH/g, formatNumber(hour));
  format = format.replace(/mm/g, formatNumber(minute));
  format = format.replace(/ss/g, formatNumber(second));

  return format;
};

/**
 * 将UTC时间字符串转换为北京时间并格式化
 * @param {string} utcTimeString UTC时间字符串
 * @param {string} format 格式化模式
 * @returns {string} 格式化后的北京时间
 */
const formatUTCToBeijing = (utcTimeString, format = 'YYYY-MM-DD HH:mm') => {
  if (!utcTimeString) return '';
  
  // 创建Date对象（JS会自动将UTC时间转换为本地时间）
  const date = new Date(utcTimeString);
  
  // 检查日期是否有效
  if (isNaN(date.getTime())) {
    return '';
  }
  
  return formatTime(date, format);
};

/**
 * 格式化时间为友好格式（如：刚刚、x分钟前、x小时前等）
 * @param {Date|string} dateTime 日期对象或ISO日期字符串
 * @returns {string} 友好格式的时间
 */
const friendlyTime = (dateTime) => {
  let date;
  if (typeof dateTime === 'string') {
    date = new Date(dateTime);
  } else {
    date = dateTime;
  }
  
  const now = new Date();
  const diff = (now - date) / 1000; // 时间差（秒）
  
  if (diff < 30) {
    return '刚刚';
  } else if (diff < 3600) { // 1小时内
    return Math.floor(diff / 60) + '分钟前';
  } else if (diff < 86400) { // 1天内
    return Math.floor(diff / 3600) + '小时前';
  } else if (diff < 604800) { // 7天内
    return Math.floor(diff / 86400) + '天前';
  } else {
    // 超过7天显示具体日期
    const year = date.getFullYear();
    const nowYear = now.getFullYear();
    
    if (year === nowYear) {
      // 同一年只显示月-日
      return formatTime(date, 'MM-DD HH:mm');
    } else {
      // 不同年显示年-月-日
      return formatTime(date, 'YYYY-MM-DD');
    }
  }
};

/**
 * 格式化数字
 * @param {number} n 数字
 * @returns {string} 格式化后的数字
 */
const formatNumber = n => {
  n = n.toString();
  return n[1] ? n : `0${n}`;
};

/**
 * 显示提示框
 * @param {string} title 提示内容
 * @param {string} icon 图标类型
 * @param {number} duration 持续时间
 */
const showToast = (title, icon = 'none', duration = 2000) => {
  wx.showToast({
    title,
    icon,
    duration
  });
};

/**
 * 显示加载框
 * @param {string} title 加载提示文字
 */
const showLoading = (title = '加载中...') => {
  wx.showLoading({
    title,
    mask: true
  });
};

/**
 * 隐藏加载框
 */
const hideLoading = () => {
  wx.hideLoading();
};

/**
 * 显示模态对话框
 * @param {string} title 标题
 * @param {string} content 内容
 * @param {boolean} showCancel 是否显示取消按钮
 * @returns {Promise} Promise对象
 */
const showModal = (title, content, showCancel = true) => {
  return new Promise((resolve, reject) => {
    wx.showModal({
      title,
      content,
      showCancel,
      success: res => {
        if (res.confirm) {
          resolve(true);
        } else if (res.cancel) {
          resolve(false);
        }
      },
      fail: err => {
        reject(err);
      }
    });
  });
};

/**
 * 检查手机号格式
 * @param {string} phone 手机号
 * @returns {boolean} 是否有效
 */
const isValidPhone = phone => {
  return /^1[3-9]\d{9}$/.test(phone);
};

/**
 * 检查验证码格式
 * @param {string} code 验证码
 * @returns {boolean} 是否有效
 */
const isValidVerifyCode = code => {
  return /^\d{6}$/.test(code);
};

/**
 * 倒计时
 * @param {number} seconds 倒计时秒数
 * @param {function} callback 回调函数
 * @returns {object} 倒计时控制对象
 */
const countdown = (seconds, callback) => {
  let timer = null;
  let remaining = seconds;

  const start = () => {
    remaining = seconds;
    if (timer) clearInterval(timer);
    
    callback(remaining);
    
    timer = setInterval(() => {
      remaining--;
      callback(remaining);
      
      if (remaining <= 0) {
        clearInterval(timer);
        timer = null;
      }
    }, 1000);
  };

  const stop = () => {
    if (timer) {
      clearInterval(timer);
      timer = null;
    }
  };

  return {
    start,
    stop,
    getRemainingTime: () => remaining
  };
};

module.exports = {
  formatTime,
  formatNumber,
  showToast,
  showLoading,
  hideLoading,
  showModal,
  isValidPhone,
  isValidVerifyCode,
  countdown,
  friendlyTime,
  formatUTCToBeijing
}; 