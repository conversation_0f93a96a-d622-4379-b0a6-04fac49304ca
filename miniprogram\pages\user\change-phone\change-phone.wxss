/* pages/user/change-phone/change-phone.wxss */

/* ==================== 页面特有样式 Page-specific Styles ==================== */

.page {
  background-color: #f5f5f5;
  min-height: 100vh;
}

/* 头部区域样式 */
.header-section {
  text-align: center;
  padding: 20rpx 0;
}

/* 验证码按钮特殊样式 */
.verify-code-btn {
  flex-shrink: 0;
  width: 200rpx;
  height: 72rpx;
  font-size: 24rpx;
  white-space: nowrap;
}

/* 提示信息区域 */
.tips-section {
  padding: 20rpx;
  background: #f8f9fa;
  border-radius: 12rpx;
  border-left: 6rpx solid #3B7ADB;
}

.tips-title {
  font-size: 26rpx;
  color: #666;
  font-weight: 500;
  margin-bottom: 15rpx;
}

.tips-list {
  line-height: 1.6;
}

.tips-item {
  font-size: 24rpx;
  color: #666;
  margin-bottom: 8rpx;
}

.tips-item:last-child {
  margin-bottom: 0;
}

/* 表单操作区域 */
.form-actions {
  margin-top: 20rpx;
}

/* 居中按钮样式 */
.btn-center {
  width: 300rpx;
  margin: 0 auto;
} 