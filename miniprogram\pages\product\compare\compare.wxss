/**
 * 选选小程序 - 产品对比页面样式
 * Product Compare Page Styles for XuanXuan Mini Program
 * 
 * 使用说明 Usage:
 * - 使用全局样式: .card, .btn, .form-input-wrap, .form-input 等
 * - 使用全局间距系统: .p-30, .mb-30, .mt-20 等
 * - 使用全局工具类: .text-center, .flex-between 等
 * 
 * 依赖 Dependencies:
 * - variables.wxss (设计变量)
 * - components.wxss (组件样式)
 * - layout.wxss (布局样式)
 * - utilities.wxss (工具类)
 */

/* pages/product/compare/compare.wxss */

/* ==================== 页面特有样式 Page Specific Styles ==================== */

/* 页面头部 */
.header-section {
  text-align: center;
}

.page-title {
  font-size: 40rpx;
  font-weight: 600;
  display: block;
}

.page-desc {
  font-size: 28rpx;
  line-height: 1.5;
  display: block;
}

/* ==================== 🆕 价格免责声明样式 Price Disclaimer Styles ==================== */

.price-disclaimer {
  background: linear-gradient(135deg, #fff3cd 0%, #ffeaa7 100%);
  border: 2rpx solid #ffc107;
  border-radius: 16rpx;
  position: relative;
  overflow: hidden;
}

.price-disclaimer::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 6rpx;
  height: 100%;
  background: linear-gradient(180deg, #ffc107 0%, #f39c12 100%);
}

.disclaimer-header {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.disclaimer-icon {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background: rgba(255, 193, 7, 0.2);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  color: #856404;
  flex-shrink: 0;
}

.disclaimer-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #856404;
}

.disclaimer-content {
  padding-left: 80rpx;
}

.disclaimer-text {
  font-size: 28rpx;
  color: #6c5914;
  line-height: 1.6;
  display: block;
}

/* ==================== 输入区域特有样式 Input Section Specific ==================== */

.section-title {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.title-text {
  font-size: 32rpx;
  font-weight: 600;
}

.title-count {
  font-size: 26rpx;
}

/* 输入项容器：确保不会裁剪下拉框 */
.input-item {
  /* 确保下拉框不被父容器裁剪 */
  overflow: visible;
  /* 为下拉框预留空间，避免在动画过程中被裁剪 */
  margin-bottom: 15rpx;
  /* 在最后一个输入项下方预留更多空间，以防下拉框被截断 */
}

.input-item:last-child {
  margin-bottom: 50rpx;
}

/* 新增：水平布局容器 */
.input-row {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

/* 修改：搜索输入框容器使用flex-1占满剩余空间 */
.search-input-wrap.flex-1 {
  flex: 1;
  /* 确保搜索输入组件的下拉框不被裁剪 */
  overflow: visible;
}

/* 产品输入区域：确保下拉框显示正常 */
.input-section {
  /* 确保卡片不会裁剪内部的下拉框 */
  overflow: visible;
  /* 为下拉框提供足够的空间 */
  padding-bottom: 40rpx;
}

/* 新增：删除按钮外层容器 */
.remove-btn-wrap {
  flex-shrink: 0;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 新增：智能搜索输入组件在对比页面中的样式调整 */
.input-item product-search-input {
  width: 100%;
}

/* 确保在较小屏幕上下拉列表不会超出屏幕 */
@media screen and (max-width: 400px) {
  .input-row {
    gap: 15rpx;
  }
  
  .remove-btn-wrap {
    width: 50rpx;
    height: 50rpx;
  }
}

/* 注意：删除按钮样式已移至全局组件样式 components.wxss 中的 .delete-btn */

.add-product-btn {
  border-style: dashed !important;
}

.add-product-btn[disabled] {
  opacity: 0.5;
  background: #f9fafb !important;
}

/* ==================== 产品概览样式 Products Overview ==================== */

.products-scroll {
  margin: 0 -30rpx;
}

.products-list {
  display: flex;
  padding: 0 30rpx;
  gap: 20rpx;
}

.product-card {
  flex-shrink: 0;
  width: 280rpx;
  background: #f8fafc;
  border-radius: 12rpx;
  padding: 20rpx;
  text-align: center;
  border: 2rpx solid #e2e8f0;
  transition: all 0.3s ease;
}

.product-card:active {
  transform: scale(0.98);
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.1);
}

.product-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  margin-bottom: 16rpx;
  background: #ffffff;
}

.product-name {
  display: block;
  font-size: 26rpx;
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 8rpx;
  line-height: 1.4;
  /* 限制显示2行 */
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

/* 🆕 价格信息容器 */
.price-info {
  margin-bottom: 12rpx;
}

.product-price {
  display: block;
  font-size: 30rpx;
  font-weight: 600;
  color: #ef4444;
  margin-bottom: 4rpx;
}

.price-range {
  display: block;
  font-size: 22rpx;
  color: #6b7280;
  font-weight: 400;
}

.product-brand {
  display: block;
  font-size: 24rpx;
  color: #6b7280;
  font-weight: 500;
  padding: 8rpx 16rpx;
  background: #e5e7eb;
  border-radius: 6rpx;
  margin-top: 8rpx;
}

/* 🆕 配置信息样式 */
.config-info {
  margin-top: 12rpx;
  padding: 12rpx;
  background: #ffffff;
  border-radius: 8rpx;
  border: 1rpx solid #e5e7eb;
}

.config-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 6rpx;
  font-size: 24rpx;
}

.config-item:last-child {
  margin-bottom: 0;
}

.config-label {
  color: #6b7280;
  font-weight: 500;
}

.config-value {
  color: #1f2937;
  font-weight: 600;
}

/* 🆕 配置选择器样式 - 重点优化显眼度 */
.config-selector {
  margin-top: 16rpx;
}

.selector-display {
  background: linear-gradient(135deg, #3B7ADB 0%, #2563eb 100%);
  color: #ffffff;
  border-radius: 10rpx;
  padding: 16rpx 20rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10rpx;
  font-size: 26rpx;
  font-weight: 600;
  box-shadow: 0 4rpx 12rpx rgba(59, 122, 219, 0.3);
  border: 2rpx solid #3B7ADB;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.selector-display::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: all 0.6s ease;
}

.selector-display:active {
  transform: scale(0.96);
  box-shadow: 0 2rpx 8rpx rgba(59, 122, 219, 0.4);
  background: linear-gradient(135deg, #2968cc 0%, #1d4ed8 100%);
}

.selector-display:active::before {
  left: 100%;
}

.selector-text {
  font-size: 26rpx;
  font-weight: 600;
  letter-spacing: 1rpx;
}

.selector-display .iconfont {
  font-size: 24rpx;
  font-weight: 600;
  transition: transform 0.3s ease;
}

.selector-display:active .iconfont {
  transform: rotate(180deg);
}

/* ==================== 对比表格样式 Comparison Table ==================== */

.comparison-table {
  /* 移除这里的overflow-x，改用scroll-view组件 */
  overflow: visible;
}

.table-scroll-container {
  /* 新增：表格滚动容器 */
  width: 100%;
  overflow: hidden;
  /* 🔧 确保scroll-view有足够的高度 */
  min-height: 400rpx;
  /* 🔧 优化滚动体验 */
  -webkit-overflow-scrolling: touch;
}

/* 🔧 新增：scroll-view内部滚动条样式 */
.table-scroll-container ::-webkit-scrollbar {
  height: 6rpx;
}

.table-scroll-container ::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 3rpx;
}

.table-scroll-container ::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3rpx;
}

.table-scroll-container ::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* 🔧 修复：表格容器样式 */
.table-container {
  border-radius: 12rpx;
  /* 🔧 修复：移除overflow: hidden，这是导致滚动被阻止的关键问题 */
  /* overflow: hidden; */ 
  border: 2rpx solid #e5e7eb;
  /* 🔧 修复：动态计算最小宽度，确保所有列都能显示 */
  /* 计算公式：参数列140rpx + 产品列数×160rpx + AI分析列200rpx + 边距 */
  min-width: calc(140rpx + var(--product-count, 2) * 160rpx + 200rpx + 40rpx);
  /* 新增：确保表格容器有足够的宽度 */
  width: max-content;
  background: #ffffff;
  /* 🔧 确保表格在scroll-view中正确显示 */
  display: block;
  overflow: visible;
}

/* 🔧 优化：表格行样式，确保在scroll-view中正常显示 */
.table-header,
.table-row {
  display: flex;
  /* 🔧 关键：确保行不换行，所有列在同一行显示 */
  flex-wrap: nowrap;
  /* 🔧 确保每一行都有合适的最小宽度 */
  min-width: 100%;
}

.table-header {
  background: linear-gradient(135deg, #3B7ADB 0%, #2563eb 100%);
  color: #ffffff;
  font-weight: 600;
  /* 🔧 确保表头在滚动时固定位置（可选） */
  position: sticky;
  top: 0;
  z-index: 10;
}

.table-body {
  background: #ffffff;
}

.table-row {
  border-bottom: 1rpx solid #e5e7eb;
  transition: background-color 0.2s ease;
}

.table-row:last-child {
  border-bottom: none;
}

.table-row:hover {
  background-color: #f9fafb;
}

.table-cell {
  padding: 24rpx 16rpx;
  font-size: 26rpx;
  line-height: 1.4;
  border-right: 1rpx solid #e5e7eb;
  word-break: break-word;
  /* 🔧 修复：调整单元格对齐方式，支持可变高度内容 */
  display: flex;
  align-items: flex-start;  /* 改为顶部对齐，而不是居中 */
  justify-content: center;
  /* 🆕 新增：确保单元格能够自适应内容高度 */
  min-height: 80rpx;  /* 设置最小高度保证美观 */
}

.table-cell:last-child {
  border-right: none;
}

/* 参数列 */
.param-header,
.param-cell {
  width: 140rpx;
  /* 🔧 修复：确保参数列宽度固定，不受flex影响 */
  min-width: 140rpx;
  max-width: 140rpx;
  flex-shrink: 0;
  background: #f8fafc;
  font-weight: 500;
  /* 🆕 新增：参数列左对齐，提升可读性 */
  justify-content: flex-start;
  text-align: left;
}

.param-header {
  background: rgba(255, 255, 255, 0.2);
}

.param-name {
  color: #374151;
}

/* 产品列 */
.product-header,
.value-cell {
  /* 🔧 修复：固定产品列宽度，确保滚动时列对齐 */
  width: 160rpx;
  min-width: 160rpx;
  max-width: 160rpx;
  flex-shrink: 0;
  text-align: center;
}

.product-header {
  font-size: 24rpx;
  /* 🔧 修复：移除单行限制，允许产品名称多行显示 */
  /* overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap; */
  
  /* 🆕 新增：支持多行显示的产品名称 */
  word-break: break-word;
  white-space: normal;
  line-height: 1.4;
  /* 🆕 增加垂直内边距，提供更好的视觉间距 */
  padding-top: 30rpx;
  padding-bottom: 30rpx;
  /* 🆕 最多显示3行，超出部分显示省略号 */
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  overflow: hidden;
}

.param-value {
  color: #1f2937;
  /* 🔧 新增：确保参数值换行显示 */
  word-break: break-word;
  white-space: normal;
  line-height: 1.3;
}

/* AI分析列 */
.analysis-header,
.analysis-cell {
  width: 200rpx;
  /* 🔧 修复：确保AI分析列宽度固定 */
  min-width: 200rpx;
  max-width: 200rpx;
  flex-shrink: 0;
  background: #fffbeb;
}

.analysis-header {
  background: rgba(255, 251, 235, 0.3);
}

.analysis-text {
  color: #92400e;
  font-size: 24rpx;
  line-height: 1.5;
  /* 🔧 新增：确保分析文本正确换行 */
  word-break: break-word;
  white-space: normal;
}

/* ==================== 特殊状态样式 Special States ==================== */

.empty-image {
  width: 200rpx;
  height: 200rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 28rpx;
  display: block;
}

/* 错误提示 */
.error-tip {
  border-radius: 8rpx;
  background-color: #fff2f0;
  border: 1rpx solid #ffccc7;
}

.error-text {
  color: #ff4d4f;
  font-size: 28rpx;
}

/* ==================== 友好错误提示卡片样式 Friendly Error Card Styles ==================== */

.friendly-error-card {
  background-color: #fff9e6;
  border: 2rpx solid #ffd591;
  border-radius: 16rpx;
  animation: cardSlideIn 0.3s ease-out;
}

@keyframes cardSlideIn {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.error-header {
  display: flex;
  align-items: flex-start;
  gap: 20rpx;
}

.error-icon-inline {
  width: 60rpx;
  height: 60rpx;
  border-radius: 50%;
  background-color: #fff3cd;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 36rpx;
  color: #856404;
  flex-shrink: 0;
}

.error-content {
  flex: 1;
}

.error-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.error-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
}

.close-btn-inline {
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #999;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.close-btn-inline:active {
  background-color: #e0e0e0;
  transform: scale(0.95);
}

.missing-products {
  background-color: #f8f9fa;
  border-radius: 12rpx;
  padding: 20rpx;
  border-left: 6rpx solid #3B7ADB;
}

.missing-item {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
  font-size: 28rpx;
}

.missing-item:last-child {
  margin-bottom: 0;
}

.item-number {
  color: #3B7ADB;
  font-weight: 600;
  margin-right: 10rpx;
  min-width: 30rpx;
}

.item-name {
  color: #333;
  flex: 1;
}

.help-desc {
  padding: 20rpx;
  background-color: #f0f6ff;
  border-radius: 12rpx;
  border: 1rpx solid #d4edda;
}

.help-text {
  font-size: 28rpx;
  color: #155724;
  line-height: 1.5;
}

.card-actions {
  display: flex;
  gap: 15rpx;
}

.card-actions .btn {
  height: 80rpx;
  border-radius: 12rpx;
  font-size: 30rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.card-actions .btn-secondary {
  min-width: 140rpx;
  background-color: #f8f9fa;
  color: #666;
  border: 1rpx solid #dee2e6;
}

.card-actions .btn-secondary:active {
  background-color: #e9ecef;
  transform: scale(0.98);
}

.card-actions .btn-primary {
  background-color: #3B7ADB;
  color: #fff;
  border: none;
}

.card-actions .btn-primary:active {
  background-color: #2968cc;
  transform: scale(0.98);
}

/* ==================== 响应式适配 Responsive Design ==================== */

/* 小屏设备适配 */
@media (max-width: 600rpx) {
  /* 🔧 优化：小屏幕下的表格滚动 */
  .table-scroll-container {
    min-height: 350rpx;
  }
  
  .table-cell {
    padding: 20rpx 12rpx;
    font-size: 24rpx;
  }
  
  .param-header,
  .param-cell {
    width: 120rpx;
    min-width: 120rpx;
    max-width: 120rpx;
  }
  
  .product-header,
  .value-cell {
    width: 140rpx;
    min-width: 140rpx;
    max-width: 140rpx;
  }
  
  .analysis-header,
  .analysis-cell {
    width: 180rpx;
    min-width: 180rpx;
    max-width: 180rpx;
  }
  
  /* 🔧 更新：小屏幕下的表格容器最小宽度计算 */
  .table-container {
    min-width: calc(120rpx + var(--product-count, 2) * 140rpx + 180rpx + 40rpx);
  }
  
  .product-card {
    width: 240rpx;
  }
  
  /* 🆕 价格免责声明小屏适配 */
  .price-disclaimer {
    padding: 20rpx;
  }
  
  .disclaimer-content {
    padding-left: 60rpx;
  }
  
  .disclaimer-text {
    font-size: 26rpx;
  }
  
  /* 🆕 配置选择器小屏适配 */
  .selector-display {
    padding: 12rpx 16rpx;
    font-size: 24rpx;
  }
  
  .selector-text {
    font-size: 24rpx;
  }
}

/* 横屏适配 */
@media (orientation: landscape) {
  .products-list {
    justify-content: center;
  }
  
  .product-card {
    width: 220rpx;
  }
  
  /* 🆕 横屏时价格免责声明适配 */
  .price-disclaimer {
    max-width: 800rpx;
    margin: 0 auto 30rpx;
  }
  
  /* 🔧 新增：横屏时表格滚动优化 */
  .table-scroll-container {
    min-height: 300rpx;
  }
}

/* ==================== 动画效果 Animations ==================== */

.result-section {
  animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(40rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 加载状态 */
.compare-btn[loading] {
  pointer-events: none;
}

.compare-btn[loading]::after {
  content: '';
  width: 30rpx;
  height: 30rpx;
  border: 3rpx solid transparent;
  border-top: 3rpx solid #ffffff;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-left: 10rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
} 