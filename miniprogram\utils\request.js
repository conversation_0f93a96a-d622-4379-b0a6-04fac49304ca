/**
 * 网络请求工具类
 */

// 请求配置
const requestConfig = {
  timeout: 300000, // 超时时间，单位毫秒 (5分钟)
  retryCount: 0,   // 重试次数 - 禁用重试避免重复请求
};

/**
 * 封装wx.request请求
 * @param {Object} options 请求参数
 * @param {string} options.url 请求地址
 * @param {string} options.method 请求方法
 * @param {Object} options.data 请求数据
 * @param {boolean} options.auth 是否需要认证
 * @returns {Promise} Promise对象
 */
const request = (options) => {
  // 在函数内部获取 app 实例，而不是在模块顶部
  const app = getApp();

  // 获取基础URL
  const baseUrl = app ? app.globalData.baseUrl : 'http://localhost:5000/api/v1';

  return new Promise((resolve, reject) => {
    // 先检查网络状态
    wx.getNetworkType({
      success: (networkRes) => {
        if (networkRes.networkType === 'none') {
          // 网络不可用，不显示错误提示，直接返回错误
          console.log('网络不可用，跳过请求:', options.url);
          return reject({
            code: 'NETWORK_UNAVAILABLE',
            message: '网络连接不可用',
            errMsg: 'network unavailable'
          });
        }
        
        // 网络可用，继续执行请求
        executeRequest();
      },
      fail: (networkError) => {
        console.error('检查网络状态失败:', networkError);
        // 网络检查失败时，仍然尝试发起请求
        executeRequest();
      }
    });
    
    function executeRequest() {
      // 请求头
      const header = {
        'Content-Type': 'application/json'
      };

      // 如果需要认证，添加token
      if (options.auth) {
        const token = wx.getStorageSync('token');
        if (token && token.accessToken) {
          header['Authorization'] = `Bearer ${token.accessToken}`;
        } else {
          // 不要直接跳转，而是返回特定的错误码
          return reject({
            code: 'NEED_LOGIN',
            message: '需要登录才能访问此功能',
            needLogin: true
          });
        }
      }

      // 发起请求
      wx.request({
        url: baseUrl + options.url,
        method: options.method || 'GET',
        data: options.data,
        header,
        timeout: requestConfig.timeout,
        success: (res) => {
          // 请求成功，但业务失败
          if (!res.data.success) {
            // Token过期处理
            if (res.data.code === 401) {
              // 确保 app 存在
              if (app && app.logout) {
                // 清除登录状态
                app.logout();
              } else {
                // app不可用时手动清理存储
                wx.removeStorageSync('token');
                wx.removeStorageSync('userInfo');
              }
              
              // 返回需要登录的错误，而不是直接跳转
              return reject({
                code: 'NEED_LOGIN',
                message: '登录已过期，请重新登录',
                needLogin: true,
                originalError: res.data
              });
            }
            return reject(res.data);
          }
          // 请求成功，业务成功
          resolve(res.data);
        },
        fail: (err) => {
          // 请求失败
          const errMsg = err.errMsg || '网络请求失败';
          
          console.error('网络请求失败:', errMsg, options.url);
          
          // 不显示错误提示，让调用方处理
          reject(err);
        }
      });
    }
  });
};

/**
 * GET请求
 * @param {string} url 请求地址
 * @param {Object} data 请求参数
 * @param {boolean} auth 是否需要认证
 * @returns {Promise} Promise对象
 */
const get = (url, data = {}, auth = false) => {
  return request({
    url,
    method: 'GET',
    data,
    auth
  });
};

/**
 * POST请求
 * @param {string} url 请求地址
 * @param {Object} data 请求参数
 * @param {boolean} auth 是否需要认证
 * @param {string} method 请求方法，默认为POST
 * @returns {Promise} Promise对象
 */
const post = (url, data = {}, auth = false, method = 'POST') => {
  return request({
    url,
    method,
    data,
    auth
  });
};

/**
 * DELETE请求
 * @param {string} url 请求地址
 * @param {Object} data 请求参数
 * @param {boolean} auth 是否需要认证
 * @returns {Promise} Promise对象
 */
const del = (url, data = {}, auth = false) => {
  return request({
    url,
    method: 'DELETE',
    data,
    auth
  });
};

/**
 * PUT请求
 * @param {string} url 请求地址
 * @param {Object} data 请求参数
 * @param {boolean} auth 是否需要认证
 * @returns {Promise} Promise对象
 */
const put = (url, data = {}, auth = false) => {
  return request({
    url,
    method: 'PUT',
    data,
    auth
  });
};

/**
 * PATCH请求
 * @param {string} url 请求地址
 * @param {Object} data 请求参数
 * @param {boolean} auth 是否需要认证
 * @returns {Promise} Promise对象
 */
const patch = (url, data = {}, auth = false) => {
  return request({
    url,
    method: 'PATCH',
    data,
    auth
  });
};

/**
 * 文件上传
 * @param {string} url 请求地址
 * @param {Array|string} filePaths 文件路径数组或单个文件路径
 * @param {string} name 文件对应的key，默认为'images'
 * @param {Object} formData 额外的表单数据
 * @param {boolean} auth 是否需要认证
 * @returns {Promise} Promise对象
 */
const upload = (url, filePaths, name = 'images', formData = {}, auth = true) => {
  // 在函数内部获取 app 实例
  const app = getApp();
  const baseUrl = app ? app.globalData.baseUrl : 'http://localhost:5000/api/v1';

  return new Promise((resolve, reject) => {
    // 请求头
    const header = {};

    // 如果需要认证，添加token
    if (auth) {
      const token = wx.getStorageSync('token');
      if (token && token.accessToken) {
        header['Authorization'] = `Bearer ${token.accessToken}`;
      } else {
        // 不要直接跳转，而是返回特定的错误码
        return reject({
          code: 'NEED_LOGIN',
          message: '需要登录才能上传文件',
          needLogin: true
        });
      }
    }

    // 统一处理单文件和多文件
    const fileArray = Array.isArray(filePaths) ? filePaths : [filePaths];

    // 多文件上传
    const uploadPromises = fileArray.map(filePath => {
      return new Promise((resolveUpload, rejectUpload) => {
        wx.uploadFile({
          url: baseUrl + url,
          filePath: filePath,
          name: name,
          formData: formData,
          header: header,
          timeout: requestConfig.timeout,
          success: (res) => {
            try {
              const data = JSON.parse(res.data);
              if (data.success) {
                resolveUpload(data);
              } else {
                // Token过期处理
                if (data.code === 401) {
                  // 确保 app 存在
                  if (app && app.logout) {
                    // 清除登录状态
                    app.logout();
                  } else {
                    // app不可用时手动清理存储
                    wx.removeStorageSync('token');
                    wx.removeStorageSync('userInfo');
                  }
                  
                  // 返回需要登录的错误，而不是直接跳转
                  rejectUpload({
                    code: 'NEED_LOGIN',
                    message: '上传文件需要登录',
                    needLogin: true,
                    originalError: data
                  });
                  return;
                }
                rejectUpload(data);
              }
            } catch (error) {
              rejectUpload(new Error('响应数据解析失败'));
            }
          },
          fail: (error) => {
            console.error('文件上传失败:', error);
            rejectUpload(new Error('文件上传失败'));
          }
        });
      });
    });

    // 等待所有文件上传完成
    Promise.all(uploadPromises)
      .then(results => {
        // 合并所有上传结果
        const allImages = results.reduce((acc, result) => {
          if (result.data && result.data.images && Array.isArray(result.data.images)) {
            return acc.concat(result.data.images);
          }
          return acc;
        }, []);
        
        resolve({
          success: true,
          data: {
            images: allImages,
            total: allImages.length
          }
        });
      })
      .catch(error => {
        reject(error);
      });
  });
};

module.exports = {
  request,
  get,
  post,
  del,
  put,
  patch,
  upload
};