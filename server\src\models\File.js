const mongoose = require('mongoose');

const FileSchema = new mongoose.Schema(
  {
    // 基本信息
    id: {
      type: String,
      required: true,
      unique: true,
      default: () => `file_${new mongoose.Types.ObjectId().toString()}`,
      index: true
    },
    user_id: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: [true, '文件必须关联上传用户'],
      index: true
    },
    filename: {
      type: String,
      required: [true, '请提供原始文件名'],
      maxlength: [255, '文件名不能超过255个字符']
    },
    file_type: {
      type: String,
      required: [true, '请提供文件MIME类型'],
      maxlength: [50, '文件类型不能超过50个字符']
    },
    file_size: {
      type: Number,
      required: [true, '请提供文件大小'],
      min: [0, '文件大小不能为负数']
    },
    file_path: {
      type: String,
      required: [true, '请提供文件存储路径'],
      maxlength: [255, '文件路径不能超过255个字符']
    },
    file_url: {
      type: String,
      required: [true, '请提供文件访问URL'],
      maxlength: [512, '文件URL不能超过512个字符']
    },
    storage_type: {
      type: String,
      required: [true, '请提供存储类型'],
      enum: ['local', 'oss', 'cos', 's3'],
      default: 'local'
    },
    module: {
      type: String,
      required: [true, '请提供所属模块'],
      enum: ['avatar', 'post', 'product', 'chat', 'feedback', 'product_comparison'],
      index: true
    },
    status: {
      type: Number,
      required: true,
      default: 1,
      enum: [0, 1], // 0:已删除, 1:正常
      index: true
    },
    
    // 图片/视频/音频特有信息
    width: {
      type: Number,
      min: [0, '宽度不能为负数']
    },
    height: {
      type: Number,
      min: [0, '高度不能为负数']
    },
    duration: {
      type: Number,
      min: [0, '时长不能为负数']
    },
    
    // 文件特性
    hash: {
      type: String,
      maxlength: [64, '哈希值不能超过64个字符'],
      index: true
    },
    thumbnail_url: {
      type: String,
      maxlength: [512, '缩略图URL不能超过512个字符']
    },
    processing_status: {
      type: Number,
      enum: [0, 1, 2], // 0:处理中, 1:处理完成, 2:处理失败
      default: 0
    },
    content_category: {
      type: String,
      enum: ['normal', 'sensitive'],
      default: 'normal'
    },
    is_temp: {
      type: Number,
      enum: [0, 1], // 0:否, 1:是
      default: 0
    },
    
    // 软删除支持
    deleted_at: {
      type: Date,
      default: null
    }
  },
  {
    timestamps: {
      createdAt: 'created_at',
      updatedAt: 'updated_at'
    }
  }
);

// 索引设置
FileSchema.index({ module: 1, status: 1 });
FileSchema.index({ created_at: -1 });

// 获取缩略图方法
FileSchema.methods.getThumbnail = function() {
  return this.thumbnail_url || this.file_url;
};

// 检查文件是否是图片
FileSchema.methods.isImage = function() {
  return this.file_type.startsWith('image/');
};

// 检查文件是否是视频
FileSchema.methods.isVideo = function() {
  return this.file_type.startsWith('video/');
};

// 检查文件是否是音频
FileSchema.methods.isAudio = function() {
  return this.file_type.startsWith('audio/');
};

// 获取文件大小的人类可读格式
FileSchema.methods.getHumanReadableSize = function() {
  const sizes = ['B', 'KB', 'MB', 'GB', 'TB'];
  if (this.file_size === 0) return '0 B';
  const i = parseInt(Math.floor(Math.log(this.file_size) / Math.log(1024)));
  return Math.round(this.file_size / Math.pow(1024, i), 2) + ' ' + sizes[i];
};

// 软删除方法
FileSchema.methods.softDelete = async function() {
  this.status = 0;
  this.deleted_at = new Date();
  return this.save();
};

module.exports = mongoose.model('File', FileSchema); 