const answerService = require('../services/answerService');
const validator = require('../utils/communityvalidator');
const { success, error } = require('../utils/response');
const Answer = require('../models/Answer');

/**
 * @desc    提交回答
 * @route   POST /api/v1/community/questions/:questionId/answers
 * @access  Private
 */
const createAnswer = async (req, res) => {
  try {
    // 验证请求数据
    const { error: validationError, value } = validator.createAnswerSchema.validate(req.body);
    
    if (validationError) {
      const errors = validationError.details.map(detail => ({
        field: detail.path[0],
        message: detail.message
      }));
      
      return error(res, 400, '请求参数错误', errors[0].message);
    }

    const { questionId } = req.params;
    const userId = req.user.id;

    // 创建回答
    const answer = await answerService.createAnswer(
      questionId,
      value.optionId,
      value.content,
      value.isAnonymous,
      userId
    );

    return success(res, 201, '回答提交成功', {
      id: answer._id
    });
  } catch (err) {
    // 处理特定错误
    if (err.message === '问题不存在' || err.message === '无效的参数ID') {
      return error(res, 404, err.message);
    }
    if (err.message === '问题已关闭，无法回答' || 
        err.message === '选项不存在于该问题中' ||
        err.message === '该问题需要提供回答理由') {
      return error(res, 400, err.message);
    }
    if (err.message === '用户对同一问题只能回答一次') {
      return error(res, 409, err.message);
    }
    
    // 处理内容审核错误
    if (err.message.includes('内容审核失败')) {
      return error(res, 400, err.message);
    }
    
    return error(res, 500, err.message);
  }
};

/**
 * @desc    获取问题的回答列表
 * @route   GET /api/v1/community/questions/:questionId/answers
 * @access  Public (支持未登录用户)
 */
const getAnswers = async (req, res) => {
  try {
    // 验证请求数据
    const { error: validationError, value } = validator.getAnswersSchema.validate(req.query);
    
    if (validationError) {
      const errors = validationError.details.map(detail => ({
        field: detail.path[0],
        message: detail.message
      }));
      
      return error(res, 400, '请求参数错误', errors[0].message);
    }

    const { questionId } = req.params;
    // 支持未登录用户访问，如果未登录则userId为null
    const userId = req.user ? req.user.id : null;

    // 获取回答列表
    const result = await answerService.getAnswers(
      questionId,
      {
        page: value.page,
        limit: value.limit,
        sortBy: value.sortBy,
        optionId: value.optionId
      },
      userId
    );

    return success(res, 200, '获取回答列表成功', result);
  } catch (err) {
    // 处理特定错误
    if (err.message === '问题不存在' || err.message === '无效的问题ID') {
      return error(res, 404, err.message);
    }
    return error(res, 500, err.message);
  }
};

/**
 * @desc    点赞/取消点赞回答
 * @route   POST /api/v1/community/answers/:id/like
 * @access  Private
 */
const likeAnswer = async (req, res) => {
  try {
    // 验证请求数据
    const { error: validationError, value } = validator.likeAnswerSchema.validate(req.body);
    
    if (validationError) {
      const errors = validationError.details.map(detail => ({
        field: detail.path[0],
        message: detail.message
      }));
      
      return error(res, 400, '请求参数错误', errors[0].message);
    }

    const { id } = req.params;
    const userId = req.user.id;

    // 执行点赞/取消点赞操作
    const result = await answerService.likeAnswer(id, value.action, userId);

    return success(
      res, 
      200, 
      value.action === 'like' ? '点赞成功' : '取消点赞成功', 
      result
    );
  } catch (err) {
    // 处理特定错误
    if (err.message === '回答不存在' || err.message === '无效的回答ID') {
      return error(res, 404, err.message);
    }
    return error(res, 500, err.message);
  }
};

/**
 * @desc    获取回答信息
 * @route   GET /api/v1/community/answers/:id
 * @access  Private
 */
const getAnswerInfo = async (req, res) => {
  try {
    const { id } = req.params;
    
    // 查找回答
    const answer = await Answer.findById(id).populate('questionId', 'title');
    
    if (!answer) {
      return error(res, 404, '回答不存在');
    }
    
    return success(res, 200, '获取回答信息成功', {
      id: answer._id,
      questionId: answer.questionId._id,
      questionTitle: answer.questionId.title,
      userId: answer.userId,
      content: answer.content,
      optionId: answer.optionId
    });
  } catch (err) {
    if (err.kind === 'ObjectId') {
      return error(res, 404, '无效的回答ID');
    }
    return error(res, 500, err.message);
  }
};

module.exports = {
  createAnswer,
  getAnswers,
  likeAnswer,
  getAnswerInfo
}; 