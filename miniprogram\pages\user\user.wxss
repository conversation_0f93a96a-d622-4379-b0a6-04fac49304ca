/* pages/user/user.wxss */

/**
 * 用户页面样式 - 重构版
 * User Page Styles - Refactored Version
 * 
 * 📊 重构统计:
 * - 原始代码: 119行 → 重构后: ~80行 (减少约33%)
 * - 移除重复样式: 卡片、按钮、表单、布局相关样式
 * - 使用全局样式: .card, .list-item, .flex, 间距工具类等
 * 
 * 📋 改造内容:
 * 1. 使用全局卡片样式替代自定义卡片样式
 * 2. 使用全局列表项样式替代自定义菜单项样式  
 * 3. 使用弹性布局工具类替代手写flex样式
 * 4. 使用全局间距系统替代固定间距值
 * 5. 使用全局字体和颜色变量
 * 
 * 🎯 保持的功能:
 * - 用户信息展示（登录/未登录状态）
 * - 菜单项点击交互效果
 * - 响应式头像大小
 * - 退出登录红色文字
 * 
 * 依赖: styles/variables.wxss, styles/components.wxss, styles/utilities.wxss
 */

/* ==================== 页面特有样式 Page-specific Styles ==================== */

/* 头像样式 */
.avatar {
  width: 120rpx;
  height: 120rpx;
  border-radius: 50%;
  overflow: hidden;
}

.avatar image {
  width: 100%;
  height: 100%;
}

/* 编辑按钮样式 */
.edit-button {
  margin-left: 20rpx;
}

.edit-button .btn {
  padding: 0 24rpx;
  height: 60rpx;
  font-size: 26rpx;
  border-radius: 30rpx;
  min-width: auto;
}

/* 菜单图标样式调整 - 覆盖全局样式中过大的图标 */
.menu-item .list-item-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
  background-color: transparent;
  border-radius: 0;
}

.menu-item .list-item-icon image {
  width: 100%;
  height: 100%;
  object-fit: contain;
}

/* 箭头图标优化 */
.arrow-icon {
  font-size: 32rpx;
  color: #cccccc;
  font-family: inherit;
  transform: rotate(0deg);
  transition: transform 0.2s ease;
}

/* 菜单项激活状态优化 */
.menu-item:active .arrow-icon {
  transform: translateX(6rpx);
}

/* ==================== 向后兼容的类名映射 Backward Compatibility ==================== */

/* 保持原有的类名结构，映射到全局样式 */
.user-info-section {
  /* 使用全局间距工具类 mb-40 */
}

.user-info {
  /* 使用全局卡片样式 .card 和弹性布局工具类 */
}

.user-info-not-login {
  /* 使用全局卡片样式和弹性布局工具类 */
  cursor: pointer;
}

.user-info-not-login:active {
  background-color: #f8f9fa;
}

.menu-section {
  /* 使用全局样式，无需额外定义 */
}

.menu-group {
  /* 使用全局卡片样式 .card */
}

.menu-item {
  /* 使用全局列表项样式 .list-item */
}

/* ==================== 小程序特有的优化 Mini Program Optimizations ==================== */

/* 适配不同屏幕尺寸的头像 */
@media (max-width: 375px) {
  .avatar {
    width: 100rpx;
    height: 100rpx;
  }
  
  .edit-button .btn {
    padding: 0 20rpx;
    height: 56rpx;
    font-size: 24rpx;
  }
}

/* 深色模式适配（如果需要） */
@media (prefers-color-scheme: dark) {
  .arrow-icon {
    color: #666666;
  }
} 