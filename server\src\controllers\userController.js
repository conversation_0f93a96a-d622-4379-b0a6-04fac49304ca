const userService = require('../services/userService');
const userValidator = require('../utils/validator');
const communityValidator = require('../utils/communityvalidator');
const smsService = require('../services/smsService');
const { success, error } = require('../utils/response');

/**
 * @desc    获取用户参与投票的问题列表
 * @route   GET /api/v1/users/me/voted-questions
 * @access  Private
 */
const getVotedQuestions = async (req, res) => {
  try {
    // 验证请求数据
    const { error: validationError, value } = communityValidator.getQuestionsSchema.validate(req.query);
    
    if (validationError) {
      const errors = validationError.details.map(detail => ({
        field: detail.path[0],
        message: detail.message
      }));
      
      return error(res, 400, '请求参数错误', errors[0].message);
    }

    const userId = req.user.id;

    // 获取问题列表
    const result = await userService.getUserVotedQuestions(
      userId,
      value.page,
      value.limit,
      value.sortBy
    );

    return success(res, 200, '获取用户参与投票的问题列表成功', result);
  } catch (err) {
    // 处理特定错误
    if (err.message === '无效的用户ID') {
      return error(res, 400, err.message);
    }
    return error(res, 500, err.message);
  }
};

/**
 * @desc    获取用户发起的问题列表
 * @route   GET /api/v1/users/me/created-questions
 * @access  Private
 */
const getCreatedQuestions = async (req, res) => {
  try {
    // 验证请求数据
    const { error: validationError, value } = communityValidator.getQuestionsSchema.validate(req.query);
    
    if (validationError) {
      const errors = validationError.details.map(detail => ({
        field: detail.path[0],
        message: detail.message
      }));
      
      return error(res, 400, '请求参数错误', errors[0].message);
    }

    const userId = req.user.id;

    // 获取问题列表
    const result = await userService.getUserCreatedQuestions(
      userId,
      value.page,
      value.limit,
      value.sortBy,
      value.status
    );

    return success(res, 200, '获取用户发起的问题列表成功', result);
  } catch (err) {
    // 处理特定错误
    if (err.message === '无效的用户ID') {
      return error(res, 400, err.message);
    }
    return error(res, 500, err.message);
  }
};

/**
 * @desc    更新用户基本资料
 * @route   PUT /api/v1/users/me/profile
 * @access  Private
 */
const updateProfile = async (req, res) => {
  try {
    // 记录请求日志
    console.log('更新用户资料请求:', {
      userId: req.user.id,
      requestBody: req.body,
      timestamp: new Date().toISOString()
    });

    // 验证请求数据
    const { error: validationError, value } = userValidator.updateProfileSchema.validate(req.body);
    
    if (validationError) {
      const errors = validationError.details.map(detail => ({
        field: detail.path[0],
        message: detail.message
      }));
      
      console.log('用户资料更新验证失败:', errors);
      return error(res, 400, '请求参数错误', errors[0].message);
    }

    const userId = req.user.id;

    // 更新用户资料
    const updatedUser = await userService.updateUserProfile(userId, value);

    console.log('用户资料更新成功:', {
      userId,
      updatedFields: Object.keys(value),
      timestamp: new Date().toISOString()
    });

    return success(res, 200, '用户资料更新成功', updatedUser);
  } catch (err) {
    // 记录错误日志
    console.error('更新用户资料发生错误:', {
      userId: req.user?.id,
      error: err.message,
      stack: err.stack,
      timestamp: new Date().toISOString()
    });

    // 处理特定错误
    if (err.message === '无效的用户ID') {
      return error(res, 400, err.message);
    }
    if (err.message === '用户不存在') {
      return error(res, 404, err.message);
    }
    if (err.message === '账户已被禁用') {
      return error(res, 403, err.message);
    }
    if (err.message === '昵称已被使用') {
      return error(res, 409, err.message);
    }
    if (err.message === '没有提供要更新的字段') {
      return error(res, 400, err.message);
    }
    
    return error(res, 500, '服务器内部错误，请稍后重试');
  }
};

/**
 * @desc    发送修改手机号验证码
 * @route   POST /api/v1/users/me/change-phone/send-code
 * @access  Private
 */
const sendChangePhoneCode = async (req, res) => {
  try {
    // 记录请求日志
    console.log('发送修改手机号验证码请求:', {
      userId: req.user.id,
      requestBody: req.body,
      timestamp: new Date().toISOString()
    });

    // 验证请求数据
    const { error: validationError, value } = userValidator.sendChangePhoneCodeSchema.validate(req.body);
    
    if (validationError) {
      const errors = validationError.details.map(detail => ({
        field: detail.path[0],
        message: detail.message
      }));
      
      console.log('发送修改手机号验证码验证失败:', errors);
      return error(res, 400, '请求参数错误', errors[0].message);
    }

    const { newPhone } = value;
    const userId = req.user.id;

    // 检查新手机号是否已被其他用户使用
    const result = await userService.checkPhoneAvailability(newPhone, userId);
    if (!result.available) {
      return error(res, 409, '该手机号已被其他用户使用');
    }

    // 发送验证码
    const deviceInfo = {
      userAgent: req.headers['user-agent'] || '',
      platform: req.headers['platform'] || 'unknown'
    };
    
    const smsResult = await smsService.sendVerificationCode(
      newPhone, 
      'change_phone', 
      deviceInfo, 
      req.ip
    );

    console.log('修改手机号验证码发送成功:', {
      userId,
      newPhone: smsService.maskPhoneNumber(newPhone),
      timestamp: new Date().toISOString()
    });

    return success(res, 200, '验证码发送成功', smsResult);
  } catch (err) {
    // 记录错误日志
    console.error('发送修改手机号验证码发生错误:', {
      userId: req.user?.id,
      error: err.message,
      stack: err.stack,
      timestamp: new Date().toISOString()
    });

    // 处理频率限制错误
    if (err.statusCode === 429) {
      return error(res, 429, err.message, null, { remainingTime: err.remainingTime });
    }
    
    return error(res, 500, '服务器内部错误，请稍后重试');
  }
};

/**
 * @desc    修改手机号
 * @route   PUT /api/v1/users/me/change-phone
 * @access  Private
 */
const changePhone = async (req, res) => {
  try {
    // 记录请求日志
    console.log('修改手机号请求:', {
      userId: req.user.id,
      requestBody: req.body,
      timestamp: new Date().toISOString()
    });

    // 验证请求数据
    const { error: validationError, value } = userValidator.changePhoneSchema.validate(req.body);
    
    if (validationError) {
      const errors = validationError.details.map(detail => ({
        field: detail.path[0],
        message: detail.message
      }));
      
      console.log('修改手机号验证失败:', errors);
      return error(res, 400, '请求参数错误', errors[0].message);
    }

    const { newPhone, verifyCode } = value;
    const userId = req.user.id;

    // 验证短信验证码
    const verifyResult = await smsService.verifyCode(newPhone, verifyCode, 'change_phone');
    if (!verifyResult.success) {
      return error(res, 400, verifyResult.message, null, { 
        remainingAttempts: verifyResult.remainingAttempts 
      });
    }

    // 更新用户手机号
    const updatedUser = await userService.changeUserPhone(userId, newPhone);

    console.log('用户手机号修改成功:', {
      userId,
      oldPhone: req.user.phone ? smsService.maskPhoneNumber(req.user.phone) : '无',
      newPhone: smsService.maskPhoneNumber(newPhone),
      timestamp: new Date().toISOString()
    });

    return success(res, 200, '手机号修改成功', updatedUser);
  } catch (err) {
    // 记录错误日志
    console.error('修改手机号发生错误:', {
      userId: req.user?.id,
      error: err.message,
      stack: err.stack,
      timestamp: new Date().toISOString()
    });

    // 处理特定错误
    if (err.message === '无效的用户ID') {
      return error(res, 400, err.message);
    }
    if (err.message === '用户不存在') {
      return error(res, 404, err.message);
    }
    if (err.message === '该手机号已被其他用户使用') {
      return error(res, 409, err.message);
    }
    if (err.message === '账户已被禁用') {
      return error(res, 403, err.message);
    }
    
    return error(res, 500, '服务器内部错误，请稍后重试');
  }
};

module.exports = {
  getVotedQuestions,
  getCreatedQuestions,
  updateProfile,
  sendChangePhoneCode,
  changePhone
};
