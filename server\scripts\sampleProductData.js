/**
 * 产品配置示例数据
 * 展示新的configurations格式如何使用
 */

const sampleProducts = [
  {
    // 华为 Mate70 - 多配置示例
    skuId: "huawei-mate70",
    skuName: "华为 Mate70",
    productType: "phone",
    brandName: "华为",
    imageUrl: "https://example.com/huawei-mate70.jpg",
    
    // 🆕 多配置支持
    configurations: [
      {
        name: "8+256GB",
        storage: "256GB",
        ram: "8GB",
        price: 5499,
        available: true
      },
      {
        name: "8+512GB", 
        storage: "512GB",
        ram: "8GB",
        price: 5999,
        available: true
      },
      {
        name: "12+512GB",
        storage: "512GB", 
        ram: "12GB",
        price: 6499,
        available: true
      }
    ],
    defaultConfiguration: "8+256GB",
    
    // 自动计算的价格范围
    priceRange: {
      min: 5499,
      max: 6499
    },
    
    // 产品规格（不包含RAM和存储，这些在configurations中）
    productSpecs: {
      phone: {
        screenSize: "6.69英寸",
        screenResolution: "2760×1256",
        processor: "麒麟9010",
        battery: "5300mAh",
        camera: {
          rear: "5000万像素超感知主摄",
          front: "1300万像素超广角摄像头"
        },
        operatingSystem: "HarmonyOS 4.2",
        network: "5G全网通"
      }
    },
    
    supportsComparison: true
  },

  {
    // iPhone 15 Pro - 多配置示例
    skuId: "iphone-15-pro",
    skuName: "iPhone 15 Pro", 
    productType: "phone",
    brandName: "Apple",
    imageUrl: "https://example.com/iphone-15-pro.jpg",
    
    configurations: [
      {
        name: "8+128GB",
        storage: "128GB",
        ram: "8GB", 
        price: 7999,
        available: true
      },
      {
        name: "8+256GB",
        storage: "256GB",
        ram: "8GB",
        price: 8999,
        available: true
      },
      {
        name: "8+512GB",
        storage: "512GB", 
        ram: "8GB",
        price: 10999,
        available: true
      },
      {
        name: "8+1TB",
        storage: "1TB",
        ram: "8GB",
        price: 12999,
        available: true
      }
    ],
    defaultConfiguration: "8+128GB",
    
    priceRange: {
      min: 7999,
      max: 12999
    },
    
    productSpecs: {
      phone: {
        screenSize: "6.1英寸",
        screenResolution: "2556×1179",
        processor: "A17 Pro芯片",
        battery: "3274mAh",
        camera: {
          rear: "4800万像素主摄+1200万像素超广角+1200万像素长焦",
          front: "1200万像素"
        },
        operatingSystem: "iOS 17",
        network: "5G全网通"
      }
    },
    
    supportsComparison: true
  },

  {
    // MacBook Pro - 笔记本多配置示例
    skuId: "macbook-pro-14-m3",
    skuName: "MacBook Pro 14英寸 M3",
    productType: "laptop", 
    brandName: "Apple",
    imageUrl: "https://example.com/macbook-pro-14.jpg",
    
    configurations: [
      {
        name: "8+512GB",
        storage: "512GB SSD",
        ram: "8GB",
        price: 14999,
        available: true
      },
      {
        name: "16+512GB", 
        storage: "512GB SSD",
        ram: "16GB",
        price: 17499,
        available: true
      },
      {
        name: "16+1TB",
        storage: "1TB SSD",
        ram: "16GB", 
        price: 19999,
        available: true
      },
      {
        name: "32+1TB",
        storage: "1TB SSD",
        ram: "32GB",
        price: 22499,
        available: true
      }
    ],
    defaultConfiguration: "8+512GB",
    
    priceRange: {
      min: 14999,
      max: 22499
    },
    
    productSpecs: {
      laptop: {
        screenSize: "14.2英寸",
        screenResolution: "3024×1964",
        processor: "Apple M3芯片",
        graphics: "10核GPU",
        operatingSystem: "macOS Sonoma",
        ports: "3×雷雳4接口, HDMI接口, SDXC卡槽",
        battery: "最长18小时"
      }
    },
    
    supportsComparison: true
  },

  {
    // 单配置产品示例（向后兼容）
    skuId: "sony-wh1000xm5",
    skuName: "索尼 WH-1000XM5",
    productType: "headphones",
    brandName: "索尼",
    imageUrl: "https://example.com/sony-wh1000xm5.jpg",
    
    // 单配置
    configurations: [
      {
        name: "标准版",
        storage: "N/A",
        ram: "N/A", 
        price: 2399,
        available: true
      }
    ],
    defaultConfiguration: "标准版",
    
    priceRange: {
      min: 2399,
      max: 2399
    },
    
    // 向后兼容：保留原price字段
    price: 2399,
    
    productSpecs: {
      // 耳机类型的规格
      headphones: {
        type: "头戴式无线降噪耳机",
        noiseReduction: "主动降噪",
        battery: "30小时续航",
        connectivity: "蓝牙5.2",
        weight: "250g"
      }
    },
    
    supportsComparison: true
  }
];

/**
 * 插入示例数据到数据库
 */
async function insertSampleData() {
  const mongoose = require('mongoose');
  const Product = require('../src/models/Product');
  
  // 数据库连接配置
  const mongoUrl = process.env.MONGODB_URL || 'mongodb://localhost:27017/xuanxuan';
  
  try {
    console.log('🚀 开始插入示例产品数据...');
    
    // 🆕 添加数据库连接
    console.log('🔌 连接数据库...');
    await mongoose.connect(mongoUrl, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
    console.log('✅ 数据库连接成功');
    
    for (const productData of sampleProducts) {
      // 检查产品是否已存在
      const existingProduct = await Product.findOne({ skuId: productData.skuId });
      
      if (existingProduct) {
        console.log(`⚠️  产品已存在，跳过: ${productData.skuName}`);
        continue;
      }
      
      // 创建新产品
      const product = new Product(productData);
      await product.save();
      
      console.log(`✅ 插入成功: ${productData.skuName}`);
      console.log(`   配置数量: ${productData.configurations.length}`);
      console.log(`   价格范围: ¥${productData.priceRange.min} - ¥${productData.priceRange.max}`);
    }
    
    console.log('🎉 示例数据插入完成!');
    
  } catch (error) {
    console.error('❌ 插入示例数据失败:', error);
    console.error('💡 可能的解决方案:');
    console.error('   1. 检查MongoDB服务是否启动');
    console.error('   2. 检查数据库连接字符串是否正确');
    console.error('   3. 确认数据库xuanxuan是否存在');
  } finally {
    // 🆕 确保关闭数据库连接
    if (mongoose.connection.readyState !== 0) {
      await mongoose.disconnect();
      console.log('🔌 数据库连接已关闭');
    }
  }
}

module.exports = {
  sampleProducts,
  insertSampleData
}; 