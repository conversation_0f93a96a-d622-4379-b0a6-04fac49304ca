Page({
  /**
   * 页面的初始数据
   */
  data: {
    appInfo: {
      name: '帮我选',
      version: '1.0.0',
      description: '让大众投票帮忙选择电子消费产品的智能决策平台',
      features: [
        '智能产品推荐',
        '大众投票决策',
        '专业评价体系',
        '实时数据分析'
      ]
    },
    contactInfo: {
      email: '<EMAIL>',
      phone: '************'
    },
    teamInfo: {
      company: '选选科技有限公司',
      address: '中国·深圳·南山区'
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {
    
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    
  },

  /**
   * 复制联系方式
   */
  copyContact: function(e) {
    const { type, content } = e.currentTarget.dataset;
    
    wx.setClipboardData({
      data: content,
      success: function () {
        wx.showToast({
          title: `${type}已复制`,
          icon: 'success',
          duration: 2000
        });
      }
    });
  },

  /**
   * 拨打客服电话
   */
  callService: function() {
    wx.makePhoneCall({
      phoneNumber: this.data.contactInfo.phone,
      success: function () {
        console.log('拨打电话成功');
      },
      fail: function () {
        wx.showToast({
          title: '拨打失败',
          icon: 'none',
          duration: 2000
        });
      }
    });
  }
}) 