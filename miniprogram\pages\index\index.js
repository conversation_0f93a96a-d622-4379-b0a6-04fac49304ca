const app = getApp();
const { question } = require('../../utils/api');
const util = require('../../utils/util');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    isLoggedIn: false,
    questionList: [],
    loading: false,
    page: 1,
    limit: 10,
    hasMore: true,
    refreshing: false,
    questionStatus: 'open', // 当前选中的问题状态：open-进行中，closed-已结束
    activeTab: 'all',  // 当前选中的标签
    tabs: [
      { id: 'all', name: '全部' },
      { id: '手机', name: '手机' },
      { id: '电脑', name: '电脑' }
    ],
    currentTag: '',  // 当前筛选的标签
    
    // 搜索相关状态
    searchKeyword: '',     // 搜索关键词
    isSearchMode: false,   // 是否为搜索模式
    searching: false,      // 是否正在搜索
    searchFocus: false,    // 搜索框是否聚焦
    searchResults: {},     // 搜索结果统计
    searchDebounceTimer: null  // 防抖定时器
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.checkLoginStatus();
    
    // 延迟加载问题列表，确保网络初始化完成
    setTimeout(() => {
      this.loadQuestions(true);
    }, 200);
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    // 每次页面显示时检查登录状态
    this.checkLoginStatus();
    // 每次显示页面时刷新问题列表
    this.loadQuestions(true);
    
    // 通知应用当前页面已显示，用于更新TabBar角标
    if (app && app.onPageShow) {
      app.onPageShow('pages/index/index');
    }
  },

  /**
   * 检查登录状态
   */
  checkLoginStatus: function() {
    const isLoggedIn = app.globalData.isLoggedIn;

    this.setData({
      isLoggedIn
    });

    // 注释掉自动跳转逻辑，让未登录用户也能留在首页浏览问题
    // if (!isLoggedIn) {
    //   wx.navigateTo({
    //     url: '/pages/login/login'
    //   });
    // }
  },

  /**
   * 加载问题列表
   */
  loadQuestions: function(refresh = false) {
    if (this.data.loading) return;
    
    // 检查网络状态
    wx.getNetworkType({
      success: (res) => {
        if (res.networkType === 'none') {
          wx.showToast({
            title: '网络连接不可用',
            icon: 'none',
            duration: 2000
          });
          return;
        }
        
        // 网络正常，继续加载
        this.doLoadQuestions(refresh);
      },
      fail: (error) => {
        console.error('检查网络状态失败:', error);
        // 即使检查失败也尝试加载
        this.doLoadQuestions(refresh);
      }
    });
  },
  
  /**
   * 实际执行加载问题列表的方法
   */
  doLoadQuestions: function(refresh = false) {
    if (refresh) {
      this.setData({
        page: 1,
        hasMore: true,
        refreshing: true
      });
    }

    if (!this.data.hasMore && !refresh) return;

    this.setData({ loading: true });
    
    // 显示加载提示
    if (!this.data.refreshing) {
      wx.showLoading({
        title: '加载中...',
      });
    }
    
    const params = {
      page: this.data.page,
      limit: this.data.limit,
      status: this.data.questionStatus // 根据当前选中的状态加载问题
    };
    
    // 添加标签筛选
    if (this.data.currentTag) {
      params.tags = this.data.currentTag;
    }
    
    question.getQuestions(params).then(res => {
      // 隐藏加载提示
      wx.hideLoading();
      
      console.log('问题列表返回数据:', res);
      
      // 检查数据结构是否符合预期
      if (!res.data || !res.data.questions) {
        throw new Error('返回数据格式不正确');
      }
      
      const { questions, pagination } = res.data;
      const { total } = pagination;
      const hasMore = this.data.page * this.data.limit < total;
      
      // 处理时间格式化
      const formattedQuestions = questions.map(item => {
        if (item.createdAt) {
          item.createdAt = util.friendlyTime(new Date(item.createdAt));
        }
        // 添加日志查看requireReason值
        console.log(`问题 ${item.id} 的requireReason值:`, item.requireReason);
        return item;
      });
      
      if (refresh) {
        this.setData({
          questionList: formattedQuestions,
          hasMore,
          page: this.data.page + 1,
          loading: false,
          refreshing: false
        });
      } else {
        this.setData({
          questionList: [...this.data.questionList, ...formattedQuestions],
          hasMore,
          page: this.data.page + 1,
          loading: false
        });
      }
    }).catch(err => {
      // 隐藏加载提示
      wx.hideLoading();
      
      console.error('加载问题列表失败:', err);
      this.setData({ 
        loading: false,
        refreshing: false
      });
      
      // 根据错误类型显示不同的提示
      if (err.code === 'NETWORK_UNAVAILABLE') {
        // 网络不可用时不显示错误提示
        console.log('网络不可用，静默处理');
      } else {
        // 其他错误显示提示
        wx.showToast({
          title: '加载失败，请重试',
          icon: 'none',
          duration: 2000
        });
      }
    });
  },

  /**
   * 搜索问题列表
   */
  searchQuestions: function(refresh = false) {
    if (this.data.searching && !refresh) return;
    
    const keyword = this.data.searchKeyword.trim();
    if (!keyword) {
      this.exitSearchMode();
      return;
    }
    
    if (refresh) {
      this.setData({
        page: 1,
        hasMore: true,
        refreshing: true
      });
    }

    if (!this.data.hasMore && !refresh) return;

    this.setData({ 
      searching: true,
      loading: true,
      isSearchMode: true
    });
    
    const params = {
      keyword: keyword,
      page: this.data.page,
      limit: this.data.limit,
      status: this.data.questionStatus // 使用用户当前选择的问题状态
    };
    
    // 如果有选中的标签，也加入搜索条件
    if (this.data.currentTag) {
      params.tags = this.data.currentTag;
    }
    
    console.log('搜索参数:', params);
    
    question.searchQuestions(params).then(res => {
      console.log('搜索结果:', res);
      
      if (!res.data || !res.data.questions) {
        throw new Error('搜索返回数据格式不正确');
      }
      
      const { questions, pagination } = res.data;
      const { total } = pagination;
      const hasMore = this.data.page * this.data.limit < total;
      
      // 处理时间格式化
      const formattedQuestions = questions.map(item => {
        if (item.createdAt) {
          item.createdAt = util.friendlyTime(new Date(item.createdAt));
        }
        return item;
      });
      
      if (refresh) {
        this.setData({
          questionList: formattedQuestions,
          hasMore,
          page: this.data.page + 1,
          searching: false,
          loading: false,
          refreshing: false,
          searchResults: { total }
        });
      } else {
        this.setData({
          questionList: [...this.data.questionList, ...formattedQuestions],
          hasMore,
          page: this.data.page + 1,
          searching: false,
          loading: false,
          searchResults: { total }
        });
      }
    }).catch(err => {
      console.error('搜索问题失败:', err);
      this.setData({ 
        searching: false,
        loading: false,
        refreshing: false
      });
      wx.showToast({
        title: '搜索失败，请重试',
        icon: 'none'
      });
    });
  },

  /**
   * 防抖搜索
   */
  debouncedSearch: function() {
    // 清除之前的定时器
    if (this.data.searchDebounceTimer) {
      clearTimeout(this.data.searchDebounceTimer);
    }
    
    // 设置新的定时器
    const timer = setTimeout(() => {
      this.searchQuestions(true);
    }, 300); // 300ms防抖延迟
    
    this.setData({
      searchDebounceTimer: timer
    });
  },

  /**
   * 搜索输入处理
   */
  onSearchInput: function(e) {
    const keyword = e.detail.value;
    this.setData({
      searchKeyword: keyword
    });
    
    if (keyword.trim()) {
      // 有输入内容时，启动防抖搜索
      this.debouncedSearch();
    } else {
      // 清空输入时，退出搜索模式
      this.exitSearchMode();
    }
  },

  /**
   * 搜索确认（用户按下搜索按钮）
   */
  onSearchConfirm: function(e) {
    const keyword = e.detail.value.trim();
    if (keyword) {
      // 清除防抖定时器，立即搜索
      if (this.data.searchDebounceTimer) {
        clearTimeout(this.data.searchDebounceTimer);
      }
      this.setData({
        searchKeyword: keyword
      });
      this.searchQuestions(true);
    }
  },

  /**
   * 清除搜索
   */
  clearSearch: function() {
    this.setData({
      searchKeyword: '',
      searchFocus: false
    });
    this.exitSearchMode();
  },

  /**
   * 退出搜索模式
   */
  exitSearchMode: function() {
    // 清除防抖定时器
    if (this.data.searchDebounceTimer) {
      clearTimeout(this.data.searchDebounceTimer);
    }
    
    this.setData({
      isSearchMode: false,
      searching: false,
      searchResults: {}
    });
    
    // 重新加载普通问题列表
    this.loadQuestions(true);
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh: function() {
    this.loadQuestions(true);
    wx.stopPullDownRefresh();
  },

  /**
   * 上拉加载更多
   */
  onReachBottom: function() {
    if (this.data.isSearchMode) {
      // 搜索模式下加载更多搜索结果
      this.searchQuestions();
    } else {
      // 普通模式下加载更多问题
      this.loadQuestions();
    }
  },

  /**
   * 处理问题点击事件
   */
  goToQuestionDetail: function(e) {
    const id = e.detail.id;
    wx.navigateTo({
      url: `/pages/question/detail/detail?id=${id}`
    });
  },

  /**
   * 跳转到发布问题页面
   */
  goToCreateQuestion: function() {
    wx.navigateTo({
      url: '/pages/question/create/create'
    });
  },

  /**
   * 跳转到产品对比页面
   */
  goToProductCompare: function() {
    wx.navigateTo({
      url: '/pages/product/compare/compare'
    });
  },

  /**
   * 跳转到AI推荐页面
   */
  goToAiRecommend: function() {
    wx.navigateTo({
      url: '/pages/product/ai-recommend/ai-recommend'
    });
  },

  /**
   * 处理标签切换
   */
  switchTab: function(e) {
    const tabId = e.currentTarget.dataset.id;
    
    if (tabId === this.data.activeTab) return;
    
    // 设置当前选中的标签
    this.setData({
      activeTab: tabId,
      currentTag: tabId === 'all' ? '' : tabId
    });
    
    // 重新加载问题列表
    this.loadQuestions(true);
  },

  /**
   * 处理问题状态切换
   */
  switchStatus: function(e) {
    const status = e.currentTarget.dataset.status;
    
    if (status === this.data.questionStatus) return;
    
    // 设置当前选中的问题状态
    this.setData({
      questionStatus: status
    });
    
    // 根据当前模式重新加载数据
    if (this.data.isSearchMode) {
      // 搜索模式下重新搜索
      this.searchQuestions(true);
    } else {
      // 普通模式下重新加载问题列表
      this.loadQuestions(true);
    }
  }
})