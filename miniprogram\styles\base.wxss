/**
 * 选选小程序 - 基础样式文件
 * Base Styles for XuanXuan Mini Program
 * 依赖：variables.wxss
 */

/* ==================== 样式重置 Reset Styles ==================== */

/* 全局重置 */
page {
  height: 100%;
  background-color: #f5f5f5;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 'Helvetica Neue', Helvetica, Arial, sans-serif;
  font-size: 28rpx;
  color: #333333;
  line-height: 1.5;
  box-sizing: border-box;
}

/* 通用盒模型 - 微信小程序WXSS兼容写法 */
view, text, image, button, input, textarea, picker, scroll-view, swiper, navigator {
  box-sizing: border-box;
}

/* 移除默认边距 */
view, text, image, button, input, textarea {
  margin: 0;
  padding: 0;
  border: none;
  background: none;
  outline: none;
}

/* 图片默认样式 */
image {
  display: block;
  max-width: 100%;
  height: auto;
}

/* ==================== 基础容器 Base Containers ==================== */

/* 页面容器 */
.page {
  width: 100%;
  min-height: 100vh;
  background-color: #f5f5f5;
  box-sizing: border-box;
}

/* 主要容器 */
.container {
  width: 100%;
  padding: 0 30rpx;
  box-sizing: border-box;
}

/* 内容容器 */
.content {
  width: 100%;
  padding: 30rpx;
  box-sizing: border-box;
}

/* 安全区域容器 */
.safe-area {
  padding-bottom: constant(safe-area-inset-bottom);
  padding-bottom: env(safe-area-inset-bottom);
}

/* ==================== 基础文本样式 Text Styles ==================== */

/* 标题样式 */
.title-large {
  font-size: 40rpx;
  font-weight: bold;
  color: #333333;
  line-height: 1.4;
}

.title-medium {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
  line-height: 1.4;
}

.title-small {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  line-height: 1.4;
}

/* 正文样式 */
.text-body {
  font-size: 28rpx;
  color: #333333;
  line-height: 1.5;
}

.text-caption {
  font-size: 24rpx;
  color: #666666;
  line-height: 1.4;
}

.text-small {
  font-size: 22rpx;
  color: #999999;
  line-height: 1.4;
}

/* 文本截断 */
.text-ellipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.text-ellipsis-2 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.text-ellipsis-3 {
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
}
