<!--pages/user/user.wxml-->
<view class="page">
  <view class="container p-30">
    <!-- 用户信息区 -->
    <view class="user-info-section mb-40">
      <block wx:if="{{isLoggedIn}}">
        <view class="user-info card flex items-center p-30">
          <view class="avatar mr-30">
            <image src="{{userInfo.avatar || '/assets/images/default-avatar.png'}}"></image>
          </view>
          <view class="info flex-1">
            <view class="nickname font-size-large font-weight-bold text-dark mb-10">{{userInfo.nickname}}</view>
            <view class="phone font-size-small text-secondary">{{userInfo.phone}}</view>
          </view>
          <view class="edit-button" bindtap="goToProfile">
            <view class="btn btn-secondary btn-small">编辑资料</view>
          </view>
        </view>
      </block>
      
      <block wx:else>
        <view class="user-info-not-login card flex items-center justify-between p-30" bindtap="goToLogin">
          <view class="flex items-center">
            <view class="avatar mr-30">
              <image src="/assets/images/default-avatar.png"></image>
            </view>
            <view class="info">
              <view class="nickname font-size-large font-weight-bold text-dark mb-10">未登录</view>
              <view class="login-tip font-size-small text-primary">点击登录/注册</view>
            </view>
          </view>
          <view class="arrow flex items-center justify-center">
            <text class="arrow-icon font-size-normal text-light">></text>
          </view>
        </view>
      </block>
    </view>
    
    <!-- 功能菜单 -->
    <view class="menu-section">
      <!-- 历史记录 -->
      <block wx:if="{{isLoggedIn}}">
        <view class="menu-title font-size-small text-secondary mb-20 pl-20">历史记录</view>
        <view class="menu-group card mb-40">
          <view class="menu-item list-item" bindtap="goToAiComparisonHistory">
            <view class="list-item-icon">
              <image src="/assets/images/ai-compare.png"></image>
            </view>
            <view class="list-item-content">
              <view class="list-item-title">AI对比产品历史</view>
            </view>
            <view class="list-item-arrow">
              <text class="arrow-icon">></text>
            </view>
          </view>
        </view>
      </block>
      
      <!-- 我的选项 -->
      <block wx:if="{{isLoggedIn && (featureConfig.showMyVotes || featureConfig.showMyCreated || featureConfig.showMyCompleted)}}">
        <view class="menu-title font-size-small text-secondary mb-20 pl-20">我的选项</view>
        <view class="menu-group card mb-40">
          <view wx:if="{{featureConfig.showMyVotes}}" class="menu-item list-item" bindtap="goToVotedQuestions">
            <view class="list-item-icon">
              <image src="/assets/images/vote.png"></image>
            </view>
            <view class="list-item-content">
              <view class="list-item-title">我的投票</view>
            </view>
            <view class="list-item-arrow">
              <text class="arrow-icon">></text>
            </view>
          </view>
          
          <view wx:if="{{featureConfig.showMyCreated}}" class="menu-item list-item" bindtap="goToCreatedQuestions">
            <view class="list-item-icon">
              <image src="/assets/images/create.png"></image>
            </view>
            <view class="list-item-content">
              <view class="list-item-title">我发起的</view>
            </view>
            <view class="list-item-arrow">
              <text class="arrow-icon">></text>
            </view>
          </view>
          
          <view wx:if="{{featureConfig.showMyCompleted}}" class="menu-item list-item" bindtap="goToClosedQuestions">
            <view class="list-item-icon">
              <image src="/assets/images/completed.png"></image>
            </view>
            <view class="list-item-content">
              <view class="list-item-title">已完成的</view>
            </view>
            <view class="list-item-arrow">
              <text class="arrow-icon">></text>
            </view>
          </view>
        </view>
      </block>
      
      <!-- 账号与安全 -->
      <block wx:if="{{isLoggedIn && featureConfig.showChangePhone}}">
        <view class="menu-title font-size-small text-secondary mb-20 pl-20">账号与安全</view>
        <view class="menu-group card mb-40">
          <view class="menu-item list-item" bindtap="goToChangePhone">
            <view class="list-item-icon">
              <image src="/assets/images/phone.png"></image>
            </view>
            <view class="list-item-content">
              <view class="list-item-title">手机号</view>
            </view>
            <view class="list-item-arrow">
              <text class="arrow-icon">></text>
            </view>
          </view>
        </view>
      </block>
      
      <!-- 设置 -->
      <view class="menu-title font-size-small text-secondary mb-20 pl-20">设置</view>
      <view class="menu-group card mb-40">
        <view wx:if="{{featureConfig.showAbout}}" class="menu-item list-item" bindtap="goToAbout">
          <view class="list-item-icon">
            <image src="/assets/images/about.png"></image>
          </view>
          <view class="list-item-content">
            <view class="list-item-title">关于我们</view>
          </view>
          <view class="list-item-arrow">
            <text class="arrow-icon">></text>
          </view>
        </view>
        
        <view class="menu-item list-item" bindtap="goToFeedback">
          <view class="list-item-icon">
            <image src="/assets/images/feedback.png"></image>
          </view>
          <view class="list-item-content">
            <view class="list-item-title">意见反馈</view>
          </view>
          <view class="list-item-arrow">
            <text class="arrow-icon">></text>
          </view>
        </view>
        
        <block wx:if="{{isLoggedIn}}">
          <view class="menu-item list-item" bindtap="logout">
            <view class="list-item-icon">
              <image src="/assets/images/logout.png"></image>
            </view>
            <view class="list-item-content">
              <view class="list-item-title text-danger">退出登录</view>
            </view>
            <view class="list-item-arrow">
              <text class="arrow-icon">></text>
            </view>
          </view>
        </block>
      </view>
    </view>
  </view>
</view> 