const path = require('path');
const fs = require('fs');
const { promisify } = require('util');
const File = require('../models/File');
const fileUtils = require('../utils/fileUtils');
const crypto = require('crypto');
const storageConfig = require('../config/storage');

// 将fs的方法转换为Promise形式
const writeFile = promisify(fs.writeFile);
const copyFile = promisify(fs.copyFile);

// 上传目录配置
const UPLOAD_DIR = path.join(process.cwd(), 'uploads');
const TEMP_DIR = path.join(UPLOAD_DIR, 'temp');
const MODULES = ['avatar', 'post', 'product', 'chat', 'feedback', 'product_comparison'];

/**
 * 初始化上传目录
 */
const initUploadDirs = async () => {
  try {
    // 确保上传根目录存在
    await fileUtils.ensureDirectoryExists(UPLOAD_DIR);
    
    // 确保临时目录存在
    await fileUtils.ensureDirectoryExists(TEMP_DIR);
    
    // 确保各模块目录存在
    for (const module of MODULES) {
      await fileUtils.ensureDirectoryExists(path.join(UPLOAD_DIR, module));
      await fileUtils.ensureDirectoryExists(path.join(UPLOAD_DIR, 'thumbnails', module));
    }
    
    console.log('✅ 上传目录初始化成功');
    
    // 打印存储配置信息
    const config = storageConfig.getFullConfig();
    console.log('📊 存储配置:', {
      类型: config.storageType,
      OSS状态: config.oss.enabled ? '✅ 已启用' : '❌ 未启用',
      OSS存储桶: config.oss.bucket || '未配置'
    });
  } catch (error) {
    console.error('❌ 上传目录初始化失败:', error);
    throw error;
  }
};

/**
 * 检查文件类型和大小是否符合限制
 * @param {string} mimeType 文件MIME类型
 * @param {number} fileSize 文件大小
 * @returns {object} 包含检查结果和错误信息的对象
 */
const validateFile = (mimeType, fileSize) => {
  const limits = storageConfig.getFileLimits();
  
  // 检查是否支持该文件类型
  const supportedTypes = {};
  Object.keys(limits).forEach(type => {
    limits[type].allowedTypes.forEach(mimeType => {
      supportedTypes[mimeType] = type;
    });
  });

  if (!supportedTypes[mimeType]) {
    return { valid: false, message: '不支持的文件类型' };
  }

  // 检查文件大小是否超过限制
  const fileType = supportedTypes[mimeType];
  const sizeLimit = limits[fileType].maxSize;

  if (fileSize > sizeLimit) {
    const readableSize = fileUtils.getHumanReadableSize(sizeLimit);
    return { 
      valid: false, 
      message: `文件过大，${fileType === 'image' ? '图片' : fileType === 'video' ? '视频' : fileType === 'audio' ? '音频' : '文档'}最大支持${readableSize}` 
    };
  }

  return { valid: true };
};

/**
 * 保存上传的文件到磁盘
 * @param {Buffer} buffer 文件数据
 * @param {string} filename 文件名
 * @param {string} module 所属模块
 * @param {boolean} isTemp 是否为临时文件
 * @returns {string} 文件路径
 */
const saveFile = async (buffer, filename, module, isTemp = false) => {
  // 根据是否为临时文件，选择目录
  const baseDir = isTemp ? TEMP_DIR : path.join(UPLOAD_DIR, module);
  
  // 确保目录存在
  await fileUtils.ensureDirectoryExists(baseDir);
  
  // 生成文件保存路径
  const filePath = path.join(baseDir, filename);
  
  // 写入文件
  await writeFile(filePath, buffer);
  
  return filePath;
};

/**
 * 创建文件记录
 * @param {object} fileData 文件数据
 * @returns {object} 创建的文件记录
 */
const createFileRecord = async (fileData) => {
  try {
    const file = new File(fileData);
    await file.save();
    return file;
  } catch (error) {
    console.error('创建文件记录失败:', error);
    throw error;
  }
};

/**
 * 上传文件到阿里云OSS
 * @param {Buffer} buffer 文件数据
 * @param {string} filename 文件名
 * @param {string} module 所属模块
 * @param {boolean} isTemp 是否为临时文件
 * @returns {Object} 包含OSS URL和路径信息的对象
 */
const uploadToOSS = async (buffer, filename, module, isTemp = false) => {
  try {
    const ossClient = storageConfig.getOSSClient();
    if (!ossClient) {
      throw new Error('OSS客户端未正确初始化');
    }

    const ossConfig = storageConfig.getOSSConfig();
    
    // 构建OSS中的文件路径
    const folderPath = isTemp ? 'temp' : `${ossConfig.folder}/${module}`;
    const ossPath = `${folderPath}/${filename}`;

    // 上传到OSS
    const result = await ossClient.put(ossPath, buffer);
    
    return {
      success: true,
      ossPath,
      url: result.url,
      localPath: null // OSS存储不需要本地路径
    };
  } catch (error) {
    console.error('OSS上传失败:', error);
    throw new Error(`OSS上传失败: ${error.message}`);
  }
};

/**
 * 创建缩略图并上传到OSS
 * @param {Buffer} buffer 原图数据
 * @param {string} filename 文件名
 * @param {string} module 所属模块
 * @param {boolean} isTemp 是否为临时文件
 * @returns {Object} 缩略图信息
 */
const createThumbnailForOSS = async (buffer, filename, module, isTemp = false) => {
  try {
    const jimp = require('jimp');
    const image = await jimp.read(buffer);
    
    // 创建缩略图buffer
    const thumbnailBuffer = await image
      .resize(300, jimp.AUTO)
      .quality(80)
      .getBufferAsync(jimp.MIME_JPEG);
    
    // 生成缩略图文件名
    const thumbFilename = `thumb_${filename}`;
    
    const ossClient = storageConfig.getOSSClient();
    const ossConfig = storageConfig.getOSSConfig();
    
    // 上传缩略图到OSS
    const folderPath = isTemp ? 'temp/thumbnails' : `${ossConfig.folder}/thumbnails/${module}`;
    const thumbOssPath = `${folderPath}/${thumbFilename}`;
    
    const result = await ossClient.put(thumbOssPath, thumbnailBuffer);
    
    return {
      thumbnail_url: result.url,
      width: image.getWidth(),
      height: image.getHeight()
    };
  } catch (error) {
    console.error('创建OSS缩略图失败:', error);
    return null;
  }
};

/**
 * 处理文件上传（通用方法）
 * @param {Array|Object} files 上传的文件（单个文件或文件数组）
 * @param {Object} options 上传选项
 * @param {String} options.userId 用户ID
 * @param {String} options.module 所属模块
 * @param {Boolean} options.isTemp 是否为临时文件
 * @param {Array} options.descriptions 文件描述数组
 * @param {Object} options.fileTypeValidation 文件类型验证函数，可选
 * @param {Number} options.maxFileSize 最大文件大小限制，可选
 * @returns {Promise<Array>} 上传的文件记录数组
 */
const processFileUpload = async (files, options) => {
  try {
    const { 
      userId, 
      module, 
      isTemp = false,
      descriptions = [],
      fileTypeValidation,
      maxFileSize
    } = options;
    
    // 统一处理单文件和多文件
    const fileArray = Array.isArray(files) ? files : [files];
    const uploadedFiles = [];
    
    // 判断使用OSS还是本地存储
    const useOSS = storageConfig.getStorageType() === 'oss' && storageConfig.isOSSEnabled();

    console.log("📊 使用的存储方式:", storageConfig.getStorageType());
    console.log("📊 OSS状态:", storageConfig.isOSSEnabled() ? '已启用' : '未启用');
    
    // 处理每个文件
    for (let i = 0; i < fileArray.length; i++) {
      const file = fileArray[i];
      const { buffer, originalname, mimetype, size } = file;
      const description = descriptions[i] || '';
      
      // 验证文件类型
      if (fileTypeValidation && typeof fileTypeValidation === 'function') {
        const isValidType = fileTypeValidation(mimetype);
        if (!isValidType) {
          continue;
        }
      }
      
      // 验证文件大小
      if (maxFileSize && size > maxFileSize) {
        continue;
      }
      
      // 默认验证文件类型和大小
      if (!fileTypeValidation && !maxFileSize) {
        const validation = validateFile(mimetype, size);
        if (!validation.valid) {
          continue;
        }
      }
      
      // 生成唯一文件名
      const filename = fileUtils.generateUniqueFilename(originalname);
      
      let fileData = {
        user_id: userId,
        filename: originalname,
        file_type: mimetype,
        file_size: size,
        module,
        is_temp: isTemp ? 1 : 0,
        processing_status: 1,
        description
      };
      
      if (useOSS) {
        // 使用OSS存储
        try {
          const ossResult = await uploadToOSS(buffer, filename, module, isTemp);
          
          fileData = {
            ...fileData,
            file_path: ossResult.ossPath,
            file_url: ossResult.url,
            storage_type: 'oss'
          };
          
          // 如果是图片，创建缩略图
          if (mimetype.startsWith('image/')) {
            const thumbnailInfo = await createThumbnailForOSS(buffer, filename, module, isTemp);
            if (thumbnailInfo) {
              fileData.width = thumbnailInfo.width;
              fileData.height = thumbnailInfo.height;
              fileData.thumbnail_url = thumbnailInfo.thumbnail_url;
            }
          }
        } catch (error) {
          console.error('OSS上传失败，跳过该文件:', error);
          continue;
        }
      } else {
        // 使用本地存储（原有逻辑）
        const localConfig = storageConfig.getLocalConfig();
        const filePath = await saveFile(buffer, filename, module, isTemp);
        const fileUrl = isTemp 
          ? `/uploads/temp/${filename}` 
          : `/uploads/${module}/${filename}`;
        
        fileData = {
          ...fileData,
          file_path: filePath,
          file_url: fileUrl,
          storage_type: 'local'
        };
        
        // 如果是图片，处理宽高和缩略图
        if (mimetype.startsWith('image/')) {
          try {
            const jimp = require('jimp');
            const image = await jimp.read(buffer);
            fileData.width = image.getWidth();
            fileData.height = image.getHeight();
            
            // 创建缩略图
            const thumbDir = isTemp 
              ? path.join(process.cwd(), 'uploads', 'temp', 'thumbnails')
              : path.join(process.cwd(), 'uploads', 'thumbnails', module);
              
            await fileUtils.ensureDirectoryExists(thumbDir);
            
            const thumbFilename = `thumb_${filename}`;
            const thumbPath = path.join(thumbDir, thumbFilename);
            
            // 调整图片大小并保存
            await image
              .resize(300, jimp.AUTO)
              .quality(80)
              .writeAsync(thumbPath);
            
            fileData.thumbnail_url = isTemp 
              ? `/uploads/temp/thumbnails/${thumbFilename}` 
              : `/uploads/thumbnails/${module}/${thumbFilename}`;
          } catch (error) {
            console.error('处理图片失败:', error);
            // 图片处理失败，不影响上传
          }
        }
      }
      
      // 创建文件记录
      const savedFile = await createFileRecord(fileData);
      uploadedFiles.push(savedFile);
    }
    
    return uploadedFiles;
  } catch (error) {
    console.error('处理文件上传失败:', error);
    throw error;
  }
};

module.exports = {
  initUploadDirs,
  validateFile,
  saveFile,
  createFileRecord,
  processFileUpload,
  uploadToOSS,
  createThumbnailForOSS
}; 