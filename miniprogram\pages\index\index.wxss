/* pages/index/index.wxss */

/* ==================== 首页特有样式 Page-specific Styles ==================== */

/* ==================== 主要操作区域 Main Actions Section ==================== */
.main-actions-section {
  background: linear-gradient(135deg, #f8fbff 0%, #f0f7ff 100%);
  border-radius: 16rpx;
  padding: 24rpx;
  box-shadow: 0 2rpx 12rpx rgba(59, 122, 219, 0.08);
  animation: slideInUp 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.1s backwards;
}

/* 操作按钮组 - 并列布局 */
.action-buttons-group {
  display: flex;
  gap: 20rpx;
  width: 100%;
}

.action-btn {
  flex: 1;
  font-size: 28rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 12rpx rgba(59, 122, 219, 0.25);
  height: 88rpx;
}

.action-btn .iconfont {
  font-size: 30rpx;
}

/* ==================== 搜索区域 Search Section ==================== */
/* 搜索输入框特有样式 - 基于全局form-input-wrap */
.search-input-wrap {
  position: relative;
  border-radius: 24rpx;
  padding: 8rpx 20rpx;
}

.search-input-wrap:focus-within {
  box-shadow: 0 4rpx 12rpx rgba(59, 122, 219, 0.15);
}

.search-input {
  background-color: transparent;
  border: none;
  padding-right: 50rpx;
  height: 64rpx;
}

.search-input:focus {
  border: none;
}

.search-icon {
  position: absolute;
  right: 60rpx;
  top: 50%;
  transform: translateY(-50%);
  font-size: 32rpx;
  color: #999999;
  transition: color 0.3s ease;
}

.search-input-wrap:focus-within .search-icon {
  color: #3B7ADB;
}

.clear-btn {
  position: absolute;
  right: 20rpx;
  top: 50%;
  transform: translateY(-50%) scale(0.8);
  width: 40rpx;
  height: 40rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #e9ecef;
  border-radius: 50%;
  opacity: 0;
  transition: all 0.3s ease;
}

.clear-btn.show {
  opacity: 1;
  transform: translateY(-50%) scale(1);
}

.clear-btn text {
  font-size: 24rpx;
  color: #666666;
}

.clear-btn:active {
  background-color: #d3d7db;
  transform: translateY(-50%) scale(0.95);
}

/* ==================== 发布按钮区域 Publish Section ==================== */
/* 原有的发布按钮样式保留作为备用 */
.publish-btn {
  font-size: 30rpx;
  font-weight: 500;
  box-shadow: 0 4rpx 12rpx rgba(59, 122, 219, 0.25);
}

.publish-btn .iconfont {
  font-size: 32rpx;
}

/* ==================== 筛选控制区域 Filter Section ==================== */
/* 标签切换器 - 通用样式 */
.tab-switch {
  display: flex;
  width: 100%;
  background-color: #f5f7fa;
  border-radius: 12rpx;
  padding: 6rpx;
  overflow: hidden;
}

.tab-switch .tab-item {
  flex: 1;
  height: 64rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #666666;
  border-radius: 8rpx;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.tab-switch .tab-item.active {
  color: #ffffff;
  font-weight: 500;
  background: linear-gradient(135deg, #3B7ADB 0%, #5A67D8 100%);
  box-shadow: 0 2rpx 8rpx rgba(59, 122, 219, 0.3);
}

/* 分类标签导航 */
.category-tabs {
  width: 100%;
  background-color: #ffffff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.04);
  overflow: hidden;
}

.tab-scroll {
  width: 100%;
}

.tab-list {
  display: flex;
  white-space: nowrap;
}

.category-tabs .tab-item {
  flex-shrink: 0;
  min-width: 120rpx;
  height: 72rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 28rpx;
  color: #666666;
  position: relative;
  transition: all 0.3s ease;
  padding: 0 20rpx;
}

.category-tabs .tab-item.active {
  color: #3B7ADB;
  font-weight: 500;
}

.category-tabs .tab-item.active::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 40rpx;
  height: 4rpx;
  background: linear-gradient(90deg, #3B7ADB 0%, #5A67D8 100%);
  border-radius: 2rpx;
}

/* ==================== 状态组件 State Components ==================== */
/* 空状态图片 */
.empty-content image {
  width: 200rpx;
  height: 200rpx;
  opacity: 0.6;
}

.empty-text {
  font-size: 28rpx;
  line-height: 1.5;
}

/* 加载状态 */
.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #3B7ADB;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 24rpx;
}

/* 没有更多数据状态 */
.no-more-text {
  font-size: 24rpx;
}

/* ==================== 响应式适配 Responsive ==================== */
/* 小屏幕适配 */
@media (max-width: 375px) {
  .action-btn {
    font-size: 26rpx;
  }
}

/* ==================== 动画效果 Animations ==================== */
/* 搜索状态提示动画 */
.search-status {
  animation: fadeInUp 0.3s ease;
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(-10rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 页面进入动画 */
.main-actions-section {
  animation: slideInUp 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.1s backwards;
}

.search-section {
  animation: slideInUp 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.2s backwards;
}

.content-section {
  animation: slideInUp 0.6s cubic-bezier(0.25, 0.46, 0.45, 0.94) 0.3s backwards;
}

@keyframes slideInUp {
  from {
    opacity: 0;
    transform: translateY(30rpx);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
} 