<!--pages/question/create/create.wxml-->
<view class="page-container">
  <view class="page-header">
    <text class="page-title">创建一个新问题</text>
    <text class="page-desc">让大家帮你做出选择</text>
    <view class="template-entry" bindtap="showTemplate">
      <text class="iconfont icon-template">📋</text>
      <text>查看问题模板</text>
    </view>
  </view>

  <view class="form-container">
    <!-- 基本信息区域 -->
    <view class="section-container">
      <view class="section-header">
        <text class="section-title">基本信息</text>
      </view>
      
      <!-- 标题输入 -->
      <view class="form-item">
        <text class="form-label">问题标题</text>
        <input 
          class="title-input" 
          placeholder="请输入问题标题" 
          maxlength="50" 
          bindinput="inputTitle"
          value="{{title}}"
        ></input>
        <text class="input-counter">{{title.length}}/50</text>
      </view>
      
      <!-- 使用场景描述 -->
      <view class="form-item">
        <text class="form-label">使用场景描述（选填）</text>
        <textarea 
          class="scene-input" 
          placeholder="请描述您的使用场景，例如：日常通勤使用、家庭影音娱乐等" 
          maxlength="500" 
          bindinput="inputScene"
          value="{{scene}}"
        ></textarea>
        <text class="input-counter">{{scene.length}}/500</text>
      </view>
      
      <!-- 标签选择 -->
      <view class="form-item">
        <text class="form-label">产品类别（选填）</text>
        <view class="tags-container">
          <view 
            class="tag-item {{tagStatus[tag] ? 'active' : ''}}" 
            wx:for="{{tagOptions}}" 
            wx:for-item="tag" 
            wx:key="*this"
            bindtap="selectTag"
            data-tag="{{tag}}"
            hover-class="tag-hover"
          >
            <text>{{tag}}</text>
            <text class="tag-check" wx:if="{{tagStatus[tag]}}">✓</text>
          </view>
        </view>
        <text class="tip-text">最多选择2个标签</text>
      </view>
      
      <!-- 关键考量因素 -->
      <view class="form-item">
        <text class="form-label">关键考量因素（选填）</text>
        <textarea 
          class="factor-input" 
          placeholder="输入考量因素，如：续航、相机、性能等" 
          maxlength="200" 
          bindinput="inputKeyFactors"
          value="{{keyFactors}}"
        ></textarea>
        <text class="input-counter">{{keyFactors.length}}/200</text>
      </view>
      
      <!-- 预算区间 -->
      <view class="form-item">
        <text class="form-label">预算区间（选填）</text>
        <view class="budget-container">
          <input 
            class="budget-input" 
            type="number" 
            placeholder="最低预算" 
            bindinput="inputBudgetMin"
            value="{{budget.min}}"
          ></input>
          <text class="budget-separator">-</text>
          <input 
            class="budget-input" 
            type="number" 
            placeholder="最高预算" 
            bindinput="inputBudgetMax"
            value="{{budget.max}}"
          ></input>
          <text class="budget-currency">元</text>
        </view>
      </view>
    </view>
    
    <!-- 选项区域 -->
    <view class="section-container">
      <view class="section-header">
        <text class="section-title">投票选项</text>
        <text class="section-desc">输入产品关键词智能搜索，选择准确的产品信息</text>
      </view>

      <!-- AI推荐按钮 -->
      <view class="ai-recommend-section">
        <view class="recommend-tip">
          <text class="tip-icon">🤖</text>
          <text class="tip-text">不知道选什么产品？试试AI智能推荐</text>
        </view>
        <button class="ai-recommend-btn" bindtap="showAiRecommend">
          <text class="btn-icon">✨</text>
          <text class="btn-text">AI推荐选项</text>
        </button>
      </view>
      
      <view class="options-list">
        <view class="option-item" wx:for="{{options}}" wx:key="index">
          <view class="product-search-wrapper">
            <product-search-input
              value="{{item}}"
              placeholder="输入产品名称，如：iPhone 15 Pro"
              data-index="{{index}}"
              bindinput="onProductOptionInput"
              bindselect="onProductOptionSelect"
              bindclear="onProductOptionClear"
              limit="{{8}}"
              clearable="{{true}}"
            />
            
            <!-- AI推荐理由展示区域 -->
            <view class="ai-recommend-reason" wx:if="{{optionRecommendReasons[index] && optionRecommendReasons[index].hasRecommendReason}}">
              <view class="reason-header">
                <text class="ai-tag">🤖 AI推荐理由</text>
                <text class="expand-btn" bindtap="toggleReasonExpanded" data-index="{{index}}">
                  {{reasonExpandedStatus[index] ? '收起' : '展开'}}
                </text>
              </view>
              
              <!-- 推荐理由内容 -->
              <view class="reason-content {{reasonExpandedStatus[index] ? 'expanded' : 'collapsed'}}">
                <!-- 推荐理由 -->
                <view class="recommend-reason" wx:if="{{optionRecommendReasons[index].recommendReason}}">
                  <text class="reason-text">{{optionRecommendReasons[index].recommendReason}}</text>
                </view>
                
                <!-- 产品优势点 -->
                <view class="highlights-section" wx:if="{{optionRecommendReasons[index].highlights && optionRecommendReasons[index].highlights.length > 0}}">
                  <text class="highlights-title">产品优势：</text>
                  <view class="highlights-list">
                    <text class="highlight-item" wx:for="{{optionRecommendReasons[index].highlights}}" wx:for-item="highlight" wx:key="*this">
                      • {{highlight}}
                    </text>
                  </view>
                </view>
              </view>
            </view>
          </view>
          <view class="delete-btn-wrap">
            <view class="delete-btn" catchtap="deleteOption" data-index="{{index}}">
              <text class="iconfont icon-close"></text>
            </view>
          </view>
        </view>
      </view>
      
      <view class="add-option" bindtap="addOption">
        <text class="iconfont icon-add">+</text>
        <text>添加选项</text>
      </view>
      <text class="tips">最少2个选项，最多5个选项</text>
    </view>

    <!-- 设置区域 -->
    <view class="section-container">
      <view class="section-header">
        <text class="section-title">问题设置</text>
      </view>

      <!-- 持续时间 -->
      <view class="form-item">
        <text class="form-label">持续时间</text>
        <view class="duration-tip">设置问题的投票时长，到期后问题将自动关闭</view>
        <view class="duration-container">
          <view class="duration-input-group">
            <input 
              class="duration-input" 
              type="number" 
              placeholder="0" 
              bindinput="inputDurationDays"
              value="{{duration.days}}"
            ></input>
            <text class="duration-unit">天</text>
          </view>
          <view class="duration-input-group">
            <input 
              class="duration-input" 
              type="number" 
              placeholder="0" 
              bindinput="inputDurationHours"
              value="{{duration.hours}}"
            ></input>
            <text class="duration-unit">小时</text>
          </view>
        </view>
        <text class="tip-text">默认3天，最长30天，到期后问题将自动关闭且无法再投票</text>
        <view class="expiry-preview" wx:if="{{duration.days > 0 || duration.hours > 0}}">
          预计结束时间：{{expiryTimePreview}}
        </view>
      </view>

      <!-- 投票方式 -->
      <view class="form-item">
        <text class="form-label">投票方式</text>
        <view class="vote-options">
          <view class="vote-option {{!requireReason ? 'active' : ''}}" bindtap="toggleRequireReason" data-value="false">
            <text class="option-text">选填理由</text>
            <text class="option-check" wx:if="{{!requireReason}}">✓</text>
          </view>
          <view class="vote-option {{requireReason ? 'active' : ''}}" bindtap="toggleRequireReason" data-value="true">
            <text class="option-text">必填理由</text>
            <text class="option-check" wx:if="{{requireReason}}">✓</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 提交按钮 -->
    <view class="submit-container">
      <button 
        class="submit-btn {{submitting ? 'disabled' : ''}}" 
        bindtap="formSubmit" 
        disabled="{{submitting}}"
      >
        <text wx:if="{{submitting}}">发布中...</text>
        <text wx:else>发布问题</text>
      </button>
    </view>
  </view>

  <!-- AI推荐弹窗 -->
  <ai-recommend-modal
    show="{{showAiRecommendModal}}"
    questionInfo="{{questionInfo}}"
    bindrecommend="onAiRecommend"
    bindclose="closeAiRecommend"
  />
</view>

<!-- 模板示例弹窗 -->
<view class="template-modal {{showTemplateModal ? 'show' : ''}}" wx:if="{{showTemplateModal}}">
  <view class="modal-content">
    <view class="modal-header">
      <text class="modal-title">问题模板示例</text>
      <view class="close-btn" bindtap="closeTemplateModal">×</view>
    </view>
    <view class="modal-body">
      <view class="template-example">
        <view class="example-header">
          <text class="example-title">示例：手机决策请求</text>
        </view>
        <view class="example-item">
          <text class="example-label">标题</text>
          <text class="example-content">{{currentTemplate.title}}</text>
        </view>
        <view class="example-item">
          <text class="example-label">使用场景</text>
          <text class="example-content">{{currentTemplate.scene}}</text>
        </view>
        <view class="example-item">
          <text class="example-label">关键考量因素</text>
          <text class="example-content">{{currentTemplate.keyFactors}}</text>
        </view>
        <view class="example-item">
          <text class="example-label">预算区间</text>
          <text class="example-content">{{currentTemplate.budget.min}}-{{currentTemplate.budget.max}}元</text>
        </view>
        <view class="example-item">
          <text class="example-label">选项</text>
          <view class="example-options">
            <text class="option-text" wx:for="{{currentTemplate.options}}" wx:key="*this">- {{item}}</text>
          </view>
        </view>
        <view class="example-item">
          <text class="example-label">标签</text>
          <view class="example-tags">
            <text class="tag-text" wx:for="{{currentTemplate.tags}}" wx:key="*this">#{{item}}</text>
          </view>
        </view>
      </view>
    </view>
    <view class="modal-footer">
      <button class="use-template-btn" bindtap="useTemplate">使用该模板</button>
      <button class="close-modal-btn" bindtap="closeTemplateModal">关闭</button>
    </view>
  </view>
</view> 