const Question = require('../models/Question');
const Answer = require('../models/Answer');
const Comment = require('../models/Comment');
const QuestionComparisonCache = require('../models/QuestionComparisonCache');
const productService = require('./productService');
const mongoose = require('mongoose');
const { auditQuestionContent } = require('../utils/contentAuditUtils');
const { buildSmartSearchQuery } = require('../utils/searchUtils');
const NewProduct = require('../models/NewProduct');
const { generateProductRecommendation } = require('../utils/aiHelper');

// 添加AI推荐任务状态管理
const aiRecommendTasks = new Map(); // 存储任务状态

/**
 * 创建新问题
 * @param {Object} questionData 问题数据
 * @param {String} userId 用户ID
 * @returns {Promise<Object>} 创建的问题
 */
const createQuestion = async (questionData, userId) => {
  try {
    // 内容审核
    console.log('开始问题内容审核...');
    const auditResult = auditQuestionContent(questionData);
    
    // 如果审核未通过，抛出错误
    if (!auditResult.passed) {
      const errorMessage = auditResult.violations.length > 0 
        ? auditResult.violations[0].message 
        : '内容审核未通过';
      
      console.log('问题内容审核未通过:', {
        userId,
        violations: auditResult.violations,
        questionData: {
          title: questionData.title,
          scene: questionData.scene?.substring(0, 50) + '...'
        }
      });
      
      throw new Error(`内容审核失败: ${errorMessage}`);
    }
    
    console.log('问题内容审核通过');
    
    // 创建问题
    const question = new Question({
      ...questionData,
      userId
    });
    
    await question.save();
    
    console.log('问题创建成功:', {
      questionId: question._id,
      title: question.title,
      userId
    });
    
    return question;
  } catch (error) {
    console.error('创建问题失败:', error);
    throw error;
  }
};

/**
 * 获取问题列表
 * @param {Object} filters 过滤条件
 * @param {Number} page 页码
 * @param {Number} limit 每页数量
 * @param {String} sortBy 排序方式
 * @returns {Promise<Object>} 问题列表和分页信息
 */
const getQuestions = async (filters = {}, page = 1, limit = 10, sortBy = 'newest') => {
  const query = { ...filters };
  
  // 处理标签过滤
  if (filters.tags && filters.tags.length > 0) {
    query.tags = { $in: filters.tags };
  }
  
  // 根据排序方式设置排序条件
  let sort = {};
  switch (sortBy) {
    case 'newest':
      sort = { createdAt: -1 };
      break;
    case 'hottest':
      sort = { totalVotes: -1, createdAt: -1 };
      break;
    case 'expiringSoon':
      sort = { expiryTime: 1, createdAt: -1 };
      break;
    default:
      sort = { createdAt: -1 };
  }

  // 查询总记录数
  const total = await Question.countDocuments(query);
  
  // 分页查询
  const questions = await Question.find(query)
    .sort(sort)
    .skip((page - 1) * limit)
    .limit(limit)
    .populate({
      path: 'userId',
      select: '_id nickname avatar',
      options: { lean: true }
    })
    .lean();

  // console.log("questions", questions);

  // 格式化返回数据
  const formattedQuestions = questions.map(question => ({
    id: question._id,
    title: question.title,
    scene: question.scene,
    keyFactors: question.keyFactors,
    budget: question.budget,
    tags: question.tags,
    options: question.options.map(option => ({
      id: option._id,
      content: option.content,
      voteCount: option.voteCount
    })),
    totalVotes: question.totalVotes,
    commentCount: question.commentCount,
    createdAt: question.createdAt,
    expiryTime: question.expiryTime,
    status: question.status,
    requireReason: question.requireReason,
    user: question.isAnonymous ? 
      { id: null, nickname: '匿名用户', avatar: '/assets/images/default-avatar.png' } : 
      {
        id: question.userId._id,
        nickname: question.userId.nickname,
        avatar: question.userId.avatar
      },
    isAnonymous: question.isAnonymous
  }));

  // 计算总页数
  const pages = Math.ceil(total / limit);

  return {
    questions: formattedQuestions,
    pagination: {
      total,
      page,
      limit,
      pages
    }
  };
};

/**
 * 根据ID获取问题详情
 * @param {String} id 问题ID
 * @param {String} userId 当前用户ID
 * @returns {Promise<Object>} 问题详情
 */
const getQuestionById = async (id, userId) => {
  // 验证ObjectId有效性
  if (!mongoose.Types.ObjectId.isValid(id)) {
    throw new Error('无效的问题ID');
  }

  const question = await Question.findById(id)
    .populate({
      path: 'userId',
      select: '_id nickname avatar',
      options: { lean: true }
    })
    .lean();

  if (!question) {
    throw new Error('问题不存在');
  }

  // 获取用户是否已经投票及投票选项
  let hasVoted = false;
  let votedOption = null;

  if (userId) {
    const answer = await Answer.findOne({
      questionId: id,
      userId
    }).select('optionId').lean();

    if (answer) {
      hasVoted = true;
      votedOption = answer.optionId;
    }
  }

  // 计算每个选项的百分比
  const options = question.options.map(option => {
    const percentage = question.totalVotes > 0 
      ? Math.round((option.voteCount / question.totalVotes) * 100) 
      : 0;
    
    return {
      id: option._id,
      content: option.content,
      voteCount: option.voteCount,
      percentage
    };
  });

  // 格式化返回数据
  return {
    id: question._id,
    title: question.title,
    scene: question.scene,
    keyFactors: question.keyFactors,
    budget: question.budget,
    tags: question.tags,
    options,
    isAnonymous: question.isAnonymous,
    requireReason: question.requireReason,
    visibility: question.visibility,
    expiryTime: question.expiryTime,
    status: question.status,
    totalVotes: question.totalVotes,
    commentCount: question.commentCount,
    createdAt: question.createdAt,
    user: question.isAnonymous ? 
      { id: null, nickname: '匿名用户', avatar: '/assets/images/default-avatar.png' } : 
      {
        id: question.userId._id,
        nickname: question.userId.nickname,
        avatar: question.userId.avatar
      },
    hasVoted,
    votedOption
  };
};

/**
 * 更新问题
 * @param {String} id 问题ID
 * @param {Object} updateData 更新数据
 * @param {String} userId 用户ID
 * @returns {Promise<Object>} 更新后的问题
 */
const updateQuestion = async (id, updateData, userId) => {
  // 验证ObjectId有效性
  if (!mongoose.Types.ObjectId.isValid(id)) {
    throw new Error('无效的问题ID');
  }

  // 查找问题并验证所有权
  const question = await Question.findById(id);
  
  if (!question) {
    throw new Error('问题不存在');
  }
  
  if (question.userId.toString() !== userId) {
    throw new Error('没有权限修改此问题');
  }
  
  // 检查是否已有人回答
  const answerCount = await Answer.countDocuments({ questionId: id });
  if (answerCount > 0) {
    throw new Error('已有人回答的问题不能修改');
  }
  
  // 更新问题
  Object.keys(updateData).forEach(key => {
    question[key] = updateData[key];
  });
  
  await question.save();
  return question;
};

/**
 * 删除问题
 * @param {String} id 问题ID
 * @param {String} userId 用户ID
 * @returns {Promise<Boolean>} 是否删除成功
 */
const deleteQuestion = async (id, userId) => {
  // 验证ObjectId有效性
  if (!mongoose.Types.ObjectId.isValid(id)) {
    throw new Error('无效的问题ID');
  }

  // 查找问题并验证所有权
  const question = await Question.findById(id);
  
  if (!question) {
    throw new Error('问题不存在');
  }
  
  if (question.userId.toString() !== userId) {
    throw new Error('没有权限删除此问题');
  }
  
  try {
    // 尝试使用事务进行操作
    const session = await mongoose.startSession();
    session.startTransaction();

    try {
      // 删除问题相关的所有评论
      await Comment.deleteMany({ questionId: id }, { session });
      
      // 删除问题相关的所有回答
      await Answer.deleteMany({ questionId: id }, { session });
      
      // 删除问题对比缓存
      await QuestionComparisonCache.deleteOne({ questionId: id }, { session });
      
      // 删除问题本身
      await Question.findByIdAndDelete(id, { session });
      
      // 提交事务
      await session.commitTransaction();
      session.endSession();
      
      console.log(`问题${id}及相关数据删除成功`);
      return true;
    } catch (error) {
      // 如果出错则回滚事务
      await session.abortTransaction();
      session.endSession();
      throw error;
    }
  } catch (error) {
    // 如果事务不支持，则使用非事务方式执行
    console.warn('事务不支持或失败，使用非事务方式删除:', error.message);
    
    // 按顺序删除关联数据
    await Comment.deleteMany({ questionId: id });
    await Answer.deleteMany({ questionId: id });
    
    // 删除问题对比缓存（非事务方式）
    try {
      await QuestionComparisonCache.deleteByQuestionId(id);
    } catch (cacheError) {
      console.error('删除问题对比缓存失败:', cacheError);
      // 不阻塞主删除流程
    }
    
    await Question.findByIdAndDelete(id);
    
    console.log(`问题${id}及相关数据删除成功（非事务模式）`);
    return true;
  }
};

/**
 * 关闭问题
 * @param {String} id 问题ID
 * @param {String} userId 用户ID
 * @returns {Promise<Object>} 关闭后的问题
 */
const closeQuestion = async (id, userId) => {
  // 验证ObjectId有效性
  if (!mongoose.Types.ObjectId.isValid(id)) {
    throw new Error('无效的问题ID');
  }

  // 查找问题并验证所有权
  const question = await Question.findById(id);
  
  if (!question) {
    throw new Error('问题不存在');
  }
  
  if (question.userId.toString() !== userId) {
    throw new Error('没有权限关闭此问题');
  }
  
  // 检查问题是否已关闭
  if (question.status === 'closed') {
    throw new Error('问题已经关闭');
  }
  
  // 更新问题状态为关闭
  question.status = 'closed';
  await question.save();
  
  return question;
};

/**
 * 关闭所有过期问题
 * @returns {Promise<Object>} 操作结果
 */
const closeAllExpiredQuestions = async () => {
  try {
    const result = await Question.closeExpiredQuestions();
    return {
      success: true,
      closedCount: result.modifiedCount,
      message: `成功关闭 ${result.modifiedCount} 个过期问题`
    };
  } catch (error) {
    console.error('关闭过期问题时出错:', error);
    throw new Error('关闭过期问题失败: ' + error.message);
  }
};

/**
 * 搜索问题
 * @param {String} keyword 搜索关键词
 * @param {Object} filters 过滤条件
 * @param {Number} page 页码
 * @param {Number} limit 每页数量
 * @returns {Promise<Object>} 搜索结果和分页信息
 */
const searchQuestions = async (keyword, filters = {}, page = 1, limit = 10) => {
  // 构建查询条件 - 修复：不再默认设置status为'open'
  const query = {};

  // 状态过滤 - 优先处理状态过滤
  if (filters.status) {
    query.status = filters.status;
  } else {
    // 如果没有指定状态，默认只搜索开放状态的问题
    query.status = 'open';
  }

  // 智能分词搜索
  if (keyword && keyword.trim()) {
    const smartSearchQuery = buildSmartSearchQuery(keyword);
    if (smartSearchQuery) {
      Object.assign(query, smartSearchQuery);
    }
  }

  // 标签过滤
  if (filters.tags && filters.tags.length > 0) {
    query.tags = { $in: filters.tags };
  }

  console.log("搜索查询条件:", JSON.stringify(query, null, 2));

  // 查询总记录数
  const total = await Question.countDocuments(query);
  console.log("匹配的总记录数:", total);

  // 设置排序条件
  let sort = {};
  if (keyword && keyword.trim()) {
    // 如果有关键词，优先按文本搜索相关度排序，然后按创建时间
    sort = { createdAt: -1 };
  } else {
    // 如果没有关键词，按创建时间排序
    sort = { createdAt: -1 };
  }

  // 分页查询
  let queryBuilder = Question.find(query);

  const questions = await queryBuilder
    .sort(sort)
    .skip((page - 1) * limit)
    .limit(limit)
    .populate({
      path: 'userId',
      select: '_id nickname avatar',
      options: { lean: true }
    })
    .lean();

  // 格式化返回数据
  const formattedQuestions = questions.map(question => ({
    id: question._id,
    title: question.title,
    scene: question.scene,
    keyFactors: question.keyFactors,
    budget: question.budget,
    tags: question.tags,
    options: question.options.map(option => ({
      id: option._id,
      content: option.content,
      voteCount: option.voteCount
    })),
    totalVotes: question.totalVotes,
    commentCount: question.commentCount,
    createdAt: question.createdAt,
    expiryTime: question.expiryTime,
    status: question.status,
    requireReason: question.requireReason,
    user: question.isAnonymous ? 
      { id: null, nickname: '匿名用户', avatar: '/assets/images/default-avatar.png' } : 
      {
        id: question.userId._id,
        nickname: question.userId.nickname,
        avatar: question.userId.avatar
      },
    isAnonymous: question.isAnonymous
  }));

  // 计算总页数
  const pages = Math.ceil(total / limit);

  console.log("搜索结果数量:", formattedQuestions.length);
  console.log("匹配到的数据:", formattedQuestions.map(q => ({ 
    id: q.id, 
    title: q.title, 
    status: q.status 
  })));

  return {
    questions: formattedQuestions,
    pagination: {
      total,
      page,
      limit,
      pages
    },
    searchKeyword: keyword
  };
};

/**
 * 根据问题ID对比问题下的产品选项（使用问题专用缓存）
 * @param {String} questionId 问题ID
 * @returns {Promise<Object>} 对比结果
 */
const compareQuestionProducts = async (questionId) => {
  try {
    // 验证问题ID格式
    if (!questionId || typeof questionId !== 'string') {
      throw new Error('问题ID格式无效');
    }

    if (!mongoose.Types.ObjectId.isValid(questionId)) {
      throw new Error('无效的问题ID');
    }

    // 转换为ObjectId类型以确保查询一致性
    const questionObjectId = new mongoose.Types.ObjectId(questionId);

    console.log('开始问题产品对比，问题ID:', questionId);

    // 首先检查问题专用缓存（使用ObjectId查询）
    const existingCache = await QuestionComparisonCache.findOne({ 
      questionId: questionObjectId,
      'cacheMetadata.status': 'active'
    });

    if (existingCache && existingCache.isValid()) {
      console.log('问题对比缓存命中，问题ID:', questionId);
      
      // 更新访问统计
      await existingCache.updateAccessStats();
      
      return {
        success: true,
        data: {
          productType: existingCache.productInfo.productType,
          products: existingCache.cachedData.formattedProducts,
          comparison: existingCache.cachedData.comparisonTable,
          aiAnalysis: existingCache.cachedData.aiAnalysis,
          notFoundProducts: existingCache.cachedData.notFoundProducts || [],
          fromQuestionCache: true,
          cacheStats: {
            accessCount: existingCache.cacheMetadata.accessCount,
            lastAccessTime: existingCache.cacheMetadata.lastAccessTime
          }
        }
      };
    }

    // 缓存未命中，查询问题选项信息
    console.log('问题对比缓存未命中，查询问题选项, ID:', questionId);
    const question = await Question.findById(questionId)
      .select('options')
      .lean();

    if (!question) {
      throw new Error('问题不存在或已被删除');
    }

    // 检查问题选项数量
    if (!question.options || question.options.length < 2) {
      throw new Error('问题选项不足，至少需要2个选项才能进行对比');
    }

    // 提取产品名称列表
    const productNames = question.options
      .map(option => option.content)
      .filter(content => content && content.trim())
      .map(content => content.trim());

    if (productNames.length < 2) {
      throw new Error('问题中有效的产品选项不足，无法进行对比');
    }

    if (productNames.length > 5) {
      // 如果选项过多，只取前5个
      productNames.splice(5);
      console.log('问题选项过多，已限制为前5个进行对比');
    }

    console.log('提取到的产品名称:', productNames);

    // 调用产品服务的对比功能
    const comparisonResult = await productService.compareProductsByNames(productNames);

    if (!comparisonResult.success) {
      return {
        success: false,
        message: comparisonResult.message,
        data: comparisonResult.data
      };
    }

    // 保存到问题专用缓存（使用upsert避免重复创建）
    try {
      const cacheData = {
        comparisonTable: comparisonResult.data.comparison,
        aiAnalysis: comparisonResult.data.aiAnalysis,
        formattedProducts: comparisonResult.data.products,
        notFoundProducts: comparisonResult.data.notFoundProducts || []
      };

      const cacheSize = JSON.stringify(cacheData).length;

      // 使用findOneAndUpdate with upsert选项，避免重复创建
      const questionCache = await QuestionComparisonCache.findOneAndUpdate(
        { questionId: questionObjectId },
        {
          $set: {
            productInfo: {
              productNames: productNames,
              productType: comparisonResult.data.productType
            },
            cachedData: cacheData,
            cacheMetadata: {
              cacheSize: cacheSize,
              status: 'active',
              aiGeneratedAt: new Date(),
              accessCount: 1,
              lastAccessTime: new Date()
            }
          },
          $setOnInsert: {
            questionId: questionObjectId,
            createdAt: new Date()
          }
        },
        { 
          upsert: true, 
          new: true,
          runValidators: true
        }
      );

      console.log(`问题${questionId}对比结果已缓存，大小: ${Math.round(cacheSize / 1024)}KB`);
    } catch (cacheError) {
      console.error('保存问题对比缓存失败:', cacheError);
      // 缓存失败不影响主流程
    }

    // 返回对比结果
    return {
      success: true,
      data: {
        productType: comparisonResult.data.productType,
        products: comparisonResult.data.products,
        comparison: comparisonResult.data.comparison,
        aiAnalysis: comparisonResult.data.aiAnalysis,
        notFoundProducts: comparisonResult.data.notFoundProducts || [],
        fromQuestionCache: false
      }
    };

  } catch (error) {
    console.error('问题产品对比出错:', error);
    throw error;
  }
};

/**
 * 根据问题信息AI推荐产品选项
 * @param {Object} questionInfo 问题信息
 * @param {Object} filterOptions 筛选选项
 * @param {String|null} userId 用户ID（可为null，表示公开访问）
 * @returns {Promise<Object>} 推荐结果
 */
const aiRecommendProducts = async (questionInfo, filterOptions, userId = null) => {
  const startTime = Date.now();
  
  try {
    console.log('开始AI产品推荐，用户ID:', userId || '公开访问');
    // console.log('问题信息:', questionInfo);
    // console.log('筛选选项:', filterOptions);

    // 1. 验证必要参数
    if (!questionInfo || !filterOptions) {
      throw new Error('缺少必要的问题信息或筛选条件');
    }

    // 验证产品类型存在
    if (!filterOptions.productType) {
      throw new Error('产品类型不能为空');
    }

    // 验证筛选条件
    if (!filterOptions.brands || filterOptions.brands.length === 0) {
      throw new Error('筛选条件不足，请选择至少一个品牌');
    }

    // 2. 构建数据库查询条件
    const dbQuery = buildProductQuery(filterOptions);
    // console.log('数据库查询条件:', JSON.stringify(dbQuery, null, 2));

    // 3. 从数据库筛选候选产品
    const candidateProducts = await NewProduct.find(dbQuery)
      .sort({ 'priceRange.min': 1 }) // 按最低价格升序排列
      .limit(20) // 限制候选产品数量，避免AI处理过多数据
      .lean();
    // console.log('候选产品:', candidateProducts);
    // console.log(`找到 ${candidateProducts.length} 个候选产品`);

    if (candidateProducts.length === 0) {
      return {
        success: false,
        message: '找不到符合条件的产品，请调整筛选条件',
        data: {
          filterInfo: filterOptions,
          totalCandidates: 0
        }
      };
    }

    // 4. 准备AI分析的数据
    const aiPromptData = buildAIPromptData(questionInfo, candidateProducts, filterOptions);
    
    // 5. 调用AI进行智能推荐
    console.log('开始AI分析推荐...');
    const aiAnalysis = await generateProductRecommendation(aiPromptData);

    // console.log('AI分析推荐结果:', aiAnalysis);
    
    // 6. 解析AI推荐结果
    const recommendationResult = parseAIRecommendation(aiAnalysis, candidateProducts);

    // console.log('AI推荐解析结果:', recommendationResult);

    if (!recommendationResult.success) {
      throw new Error('AI推荐解析失败: ' + recommendationResult.message);
    }

    const processingTime = Date.now() - startTime;
    console.log(`AI产品推荐完成，耗时: ${processingTime}ms`);

    return {
      success: true,
      data: {
        products: recommendationResult.products,
        aiAnalysis: recommendationResult.analysis,
        filterInfo: filterOptions,
        totalCandidates: candidateProducts.length,
        processingTime
      }
    };

  } catch (error) {
    console.error('AI产品推荐出错:', error);
    throw error;
  }
};

/**
 * 构建产品数据库查询条件
 * @param {Object} filterOptions 筛选选项
 * @returns {Object} 数据库查询对象
 */
const buildProductQuery = (filterOptions) => {
  const query = {
    productType: filterOptions.productType,
    supportsComparison: true,
    // 确保产品有有效的价格范围（min和max不都为0）
    $and: [
      {
        $or: [
          { 'priceRange.min': { $gt: 0 } },
          { 'priceRange.max': { $gt: 0 } }
        ]
      }
    ]
  };

  // 品牌筛选
  if (filterOptions.brands && filterOptions.brands.length > 0) {
    query.brandName = { $in: filterOptions.brands };
  }



  // 价格范围筛选 - 基于NewProduct的priceRange字段
  if (filterOptions.budget) {
    const budgetConditions = [];
    
    if (filterOptions.budget.min && filterOptions.budget.max) {
      // 有最小值和最大值：筛选价格范围有交集的产品
      budgetConditions.push({
        $and: [
          { 'priceRange.min': { $lte: filterOptions.budget.max } },
          { 'priceRange.max': { $gte: filterOptions.budget.min } }
        ]
      });
    } else if (filterOptions.budget.min) {
      // 只有最小值：产品最高价格应大于等于预算最小值
      budgetConditions.push({ 'priceRange.max': { $gte: filterOptions.budget.min } });
    } else if (filterOptions.budget.max) {
      // 只有最大值：产品最低价格应小于等于预算最大值
      budgetConditions.push({ 'priceRange.min': { $lte: filterOptions.budget.max } });
    }
    
    if (budgetConditions.length > 0) {
      // 将预算条件添加到现有的$and数组中
      query.$and.push({ $or: budgetConditions });
    }
  }

  return query;
};

/**
 * 构建AI提示数据
 * @param {Object} questionInfo 问题信息
 * @param {Array} candidateProducts 候选产品列表
 * @param {Object} filterOptions 筛选选项
 * @returns {String} AI提示字符串
 */
const buildAIPromptData = (questionInfo, candidateProducts, filterOptions) => {
  const promptData = {
    questionInfo: {
      title: questionInfo.title || '',
      scene: questionInfo.scene || '',
      keyFactors: questionInfo.keyFactors || ''
    },
    filterOptions,
    candidateProducts: candidateProducts.map(product => {
      // 获取默认配置信息
      const defaultConfig = getProductDefaultConfig(product);
      // 使用NewProduct模型的commonSpecs字段
      const specs = product.commonSpecs || {};
      
      return {
        skuId: product.skuId,
        skuName: product.skuName,
        brand: product.brandName,
        price: getProductDisplayPrice(product),
        specs: {
          ...specs,
          // 使用configurations中的ram和storage
          ram: defaultConfig?.specs?.ram || specs?.ram || '未知',
          storage: defaultConfig?.specs?.storage || specs?.storage || '未知'
        }
      };
    })
  };

  // 构建结构化的提示文本
  let prompt = `用户问题分析：\n`;
  prompt += `- 问题标题：${questionInfo.title}\n`;
  prompt += `- 使用场景：${questionInfo.scene || '未指定'}\n`;
  prompt += `- 关键考量因素：${questionInfo.keyFactors || '未指定'}\n`;
  prompt += `- 产品类型：${filterOptions.productType === 'phone' ? '手机' : '笔记本电脑'}\n\n`;
  
  prompt += `候选产品列表（共${candidateProducts.length}个）：\n`;
  candidateProducts.forEach((product, index) => {
    // 获取产品配置信息
    const defaultConfig = getProductDefaultConfig(product);
    // 使用NewProduct模型的commonSpecs字段
    const specs = product.commonSpecs || {};
    
    const displayPrice = getProductDisplayPrice(product);
    const ram = defaultConfig?.specs?.ram || specs?.ram || '未知';
    const storage = defaultConfig?.specs?.storage || specs?.storage || '未知';
    
    prompt += `${index + 1}. SKU ID: ${product.skuId}\n`;
    prompt += `   产品名称: ${product.skuName}\n`;
    prompt += `   品牌: ${product.brandName}\n`;
    prompt += `   价格: ¥${displayPrice}\n`;
    prompt += `   主要配置: 处理器${specs?.processor || '未知'}, 内存${ram}, 存储${storage}\n\n`;
  });
  
  prompt += `请根据以上信息，严格按照JSON格式推荐2-5个最适合的产品。`;

  return prompt;
};

/**
 * 解析AI推荐结果
 * @param {String} aiResponse AI响应
 * @param {Array} candidateProducts 候选产品列表
 * @returns {Object} 解析结果
 */
const parseAIRecommendation = (aiResponse, candidateProducts) => {
  try {
    console.log('开始解析AI推荐响应:', aiResponse);
    
    // 创建产品映射
    const productMap = new Map(candidateProducts.map(p => [p.skuId, p]));
    
    let aiData = null;
    
    // 尝试解析JSON格式的AI响应
    try {
      // 提取JSON部分（去除可能的前后文字）
      const jsonMatch = aiResponse.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        aiData = JSON.parse(jsonMatch[0]);
      } else {
        // 如果没有找到JSON，尝试直接解析
        aiData = JSON.parse(aiResponse);
      }
    } catch (jsonError) {
      console.warn('AI响应不是有效JSON格式，使用备选解析方式');
      // 使用备选解析逻辑
      return parseAIRecommendationFallback(aiResponse, candidateProducts);
    }
    
    // 验证AI响应结构
    if (!aiData.recommendations || !Array.isArray(aiData.recommendations)) {
      console.warn('AI响应格式不正确，缺少recommendations数组');
      return parseAIRecommendationFallback(aiResponse, candidateProducts);
    }
    
    const recommendedProducts = [];
    
    // 解析每个推荐产品
    aiData.recommendations.forEach(recommendation => {
      const { skuId, recommendReason, highlights } = recommendation;
      
      // 查找对应的产品
      const product = productMap.get(skuId);
      if (product) {
        const formattedProduct = formatProductForRecommendation(
          product, 
          recommendReason || '智能推荐产品', 
          highlights || []
        );
        recommendedProducts.push(formattedProduct);
      } else {
        console.warn(`推荐的产品SKU ID不存在: ${skuId}`);
      }
    });
    
    // 如果推荐的产品太少，使用备选逻辑补充
    if (recommendedProducts.length < 2) {
      console.log('AI推荐的产品数量不足，使用备选逻辑补充');
      const fallbackResult = parseAIRecommendationFallback(aiResponse, candidateProducts);
      
      // 合并结果，避免重复
      const existingSkuIds = new Set(recommendedProducts.map(p => p.skuId));
      fallbackResult.products.forEach(product => {
        if (!existingSkuIds.has(product.skuId) && recommendedProducts.length < 5) {
          recommendedProducts.push(product);
        }
      });
    }

    return {
      success: true,
      products: recommendedProducts,
      analysis: aiData.overallAnalysis || aiResponse
    };

  } catch (error) {
    console.error('解析AI推荐结果失败:', error);
    // 使用备选解析方式
    return parseAIRecommendationFallback(aiResponse, candidateProducts);
  }
};

/**
 * 备选AI推荐解析方式（兼容旧格式）
 * @param {String} aiResponse AI响应
 * @param {Array} candidateProducts 候选产品列表
 * @returns {Object} 解析结果
 */
const parseAIRecommendationFallback = (aiResponse, candidateProducts) => {
  try {
    console.log('使用备选AI推荐解析方式');
    
    const productMap = new Map(candidateProducts.map(p => [p.skuId, p]));
    
    // 简单的解析逻辑：查找响应中提到的产品SKU ID
    const recommendedProducts = [];
    const mentionedSkuIds = [];
    
    // 通过正则表达式或其他方式从AI响应中提取产品ID
    candidateProducts.forEach(product => {
      if (aiResponse.includes(product.skuId) || aiResponse.includes(product.skuName)) {
        mentionedSkuIds.push(product.skuId);
      }
    });
    
    // 如果AI响应中没有明确提及特定产品，使用备选逻辑
    if (mentionedSkuIds.length === 0) {
      // 根据价格和规格选取2-3个代表性产品
      const sortedProducts = candidateProducts.sort((a, b) => a.price - b.price);
      const lowPrice = sortedProducts[0];
      const midPrice = sortedProducts[Math.floor(sortedProducts.length / 2)];
      const highPrice = sortedProducts[sortedProducts.length - 1];
      
      [lowPrice, midPrice, highPrice].forEach(product => {
        if (product && !mentionedSkuIds.includes(product.skuId)) {
          mentionedSkuIds.push(product.skuId);
        }
      });
    }

    // 限制推荐数量为2-5个
    const selectedSkuIds = mentionedSkuIds.slice(0, 5);
    
    selectedSkuIds.forEach(skuId => {
      const product = productMap.get(skuId);
      if (product) {
        recommendedProducts.push(formatProductForRecommendation(
          product, 
          '基于您的需求智能推荐', 
          ['性能优秀', '性价比高']
        ));
      }
    });

    // if (recommendedProducts.length < 2) {
    //   // 如果推荐的产品太少，补充一些产品
    //   const additionalProducts = candidateProducts
    //     .filter(p => !selectedSkuIds.includes(p.skuId))
    //     .slice(0, 2 - recommendedProducts.length);
        
    //   additionalProducts.forEach(product => {
    //     recommendedProducts.push(formatProductForRecommendation(
    //       product, 
    //       '备选推荐产品', 
    //       ['配置不错', '值得考虑']
    //     ));
    //   });
    // }

    return {
      success: true,
      products: recommendedProducts,
      analysis: aiResponse
    };

  } catch (error) {
    console.error('备选AI推荐解析失败:', error);
    return {
      success: false,
      message: error.message
    };
  }
};

/**
 * 格式化产品数据用于推荐结果
 * @param {Object} product 产品数据
 * @param {String} recommendReason 推荐理由
 * @param {Array} highlights 产品亮点
 * @returns {Object} 格式化的产品数据
 */
const formatProductForRecommendation = (product, recommendReason = '', highlights = []) => {
  // 获取默认配置信息
  const defaultConfig = getProductDefaultConfig(product);
  // 使用NewProduct模型的commonSpecs字段
  const specs = product.commonSpecs || {};
  
  // 获取显示价格
  const displayPrice = getProductDisplayPrice(product);
    
  return {
    skuId: product.skuId,
    skuName: product.skuName,
    brand: product.brandName,
    price: displayPrice,
    imageUrl: product.imageUrl,
    recommendReason: recommendReason,
    highlights: highlights || [],
    // 配置信息
    // configurations: product.configurations || [],
    // defaultConfiguration: product.defaultConfiguration,
    // specs: {
    //   processor: specs?.processor,
    //   // 优先使用configurations中的ram和storage
    //   ram: defaultConfig?.specs?.ram || specs?.ram,
    //   storage: defaultConfig?.specs?.storage || specs?.storage,
    //   ...(product.productType === 'phone' && {
    //     screenSize: specs?.screenSize,
    //     battery: specs?.battery,
    //     camera: specs?.camera?.rear || specs?.rearCamera
    //   }),
    //   ...(product.productType === 'laptop' && {
    //     screenSize: specs?.screenSize,
    //     graphics: specs?.graphics,
    //     battery: specs?.battery
    //   })
    // }
  };
};

/**
 * 获取产品默认配置
 * @param {Object} product 产品对象
 * @returns {Object|null} 默认配置对象
 */
const getProductDefaultConfig = (product) => {
  if (!product.configurations || product.configurations.length === 0) {
    return null;
  }
  
  // 查找默认配置
  const defaultConfig = product.configurations.find(
    config => config.name === product.defaultConfiguration
  );
  
  // 如果找不到默认配置，返回第一个配置
  return defaultConfig || product.configurations[0];
};

/**
 * 获取产品显示价格
 * @param {Object} product 产品对象
 * @returns {Number} 显示价格
 */
const getProductDisplayPrice = (product) => {
  // 如果有多个配置，使用价格范围的最小值
  if (product.configurations && product.configurations.length > 0) {
    const defaultConfig = getProductDefaultConfig(product);
    return defaultConfig?.price || product.configurations[0].price;
  }
  
  // 向后兼容：使用原有的price字段
  return product.price || 0;
};

/**
 * 异步AI推荐产品选项
 * @param {Object} questionInfo 问题信息
 * @param {Object} filterOptions 筛选选项
 * @param {String} userId 用户ID
 * @param {String} taskId 任务ID
 * @returns {Promise<void>}
 */
const aiRecommendProductsAsync = async (questionInfo, filterOptions, userId, taskId) => {
  // 标记任务开始处理
  aiRecommendTasks.set(taskId, {
    status: 'processing',
    userId: userId,
    startTime: Date.now(),
    result: null,
    error: null
  });
  
  console.log(`开始异步AI推荐任务: ${taskId}`);
  
  try {
    // 执行AI推荐
    const result = await aiRecommendProducts(questionInfo, filterOptions, userId);
    
    // 更新任务状态为完成
    aiRecommendTasks.set(taskId, {
      status: 'completed',
      userId: userId,
      startTime: aiRecommendTasks.get(taskId).startTime,
      endTime: Date.now(),
      result: result,
      error: null
    });
    
    console.log(`异步AI推荐任务完成: ${taskId}`);
    
  } catch (error) {
    console.error(`异步AI推荐任务失败: ${taskId}`, error);
    
    // 更新任务状态为失败
    aiRecommendTasks.set(taskId, {
      status: 'failed',
      userId: userId,
      startTime: aiRecommendTasks.get(taskId).startTime,
      endTime: Date.now(),
      result: null,
      error: error.message
    });
  }
};

/**
 * 获取AI推荐任务状态
 * @param {String} taskId 任务ID
 * @returns {Object} 任务状态
 */
const getAIRecommendTaskStatus = async (taskId) => {
  const task = aiRecommendTasks.get(taskId);
  
  if (!task) {
    throw new Error('任务不存在或已过期');
  }
  
  // 检查任务是否过期（30分钟）
  const now = Date.now();
  const maxAge = 30 * 60 * 1000; // 30分钟
  
  if (now - task.startTime > maxAge) {
    // 清理过期任务
    aiRecommendTasks.delete(taskId);
    throw new Error('任务已过期');
  }
  
  const response = {
    taskId: taskId,
    status: task.status,
    startTime: task.startTime
  };
  
  if (task.status === 'completed') {
    response.result = task.result;
    response.endTime = task.endTime;
    response.processingTime = task.endTime - task.startTime;
    
    // 完成的任务可以清理（延迟清理，给客户端一些时间获取结果）
    setTimeout(() => {
      aiRecommendTasks.delete(taskId);
    }, 60000); // 1分钟后清理
    
  } else if (task.status === 'failed') {
    response.error = task.error;
    response.endTime = task.endTime;
    
    // 失败的任务立即清理
    aiRecommendTasks.delete(taskId);
  } else {
    // 处理中状态，计算已耗时
    response.processingTime = now - task.startTime;
  }
  
  return response;
};

module.exports = {
  createQuestion,
  getQuestions,
  getQuestionById,
  updateQuestion,
  deleteQuestion,
  closeQuestion,
  closeAllExpiredQuestions,
  searchQuestions,
  compareQuestionProducts,
  aiRecommendProducts,
  aiRecommendProductsAsync,
  getAIRecommendTaskStatus
};