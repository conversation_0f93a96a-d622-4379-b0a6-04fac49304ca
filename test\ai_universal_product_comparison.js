#!/usr/bin/env node
/**
 * 通用产品对比工具 - 基于AI智能分析
 * 支持全领域产品对比，无需本地数据库
 * 完全依靠DeepSeek AI的知识能力进行产品分析和对比
 * 作者: AI助手
 * 创建时间: 2025-07-03
 */

const axios = require('axios');
const path = require('path');
const fs = require('fs');

// DeepSeek API配置
const DEEPSEEK_API_KEY = process.env.DEEPSEEK_API_KEY || '***********************************';
const DEEPSEEK_API_BASE = process.env.DEEPSEEK_API_BASE || 'https://api.deepseek.com';
const DEEPSEEK_MODEL = process.env.DEEPSEEK_MODEL || 'deepseek-chat';

// 默认配置
const DEFAULT_CONFIG = {
  temperature: 0.3, // 降低temperature以获得更准确的技术参数
  maxTokens: 4000,
  timeout: 150000
};

/**
 * 调用DeepSeek API
 * @param {String} userPrompt 用户提示
 * @param {String} systemPrompt 系统提示
 * @param {Object} config 配置选项
 * @returns {Promise<String>} API响应内容
 */
async function callDeepSeekAPI(userPrompt, systemPrompt = null, config = {}) {
  try {
    const aiConfig = { ...DEFAULT_CONFIG, ...config };
    
    const baseEndpoint = DEEPSEEK_API_BASE.endsWith('/') 
      ? DEEPSEEK_API_BASE.slice(0, -1) 
      : DEEPSEEK_API_BASE;
    
    const url = `${baseEndpoint}/v1/chat/completions`;
    
    console.log(`🤖 正在调用DeepSeek AI进行智能产品分析...`);
    
    const messages = [];
    if (systemPrompt) {
      messages.push({ role: "system", content: systemPrompt });
    }
    messages.push({ role: "user", content: userPrompt });
    
    const response = await axios({
      url: url,
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${DEEPSEEK_API_KEY}`,
        'Content-Type': 'application/json',
      },
      data: {
        model: DEEPSEEK_MODEL,
        messages: messages,
        temperature: aiConfig.temperature,
        max_tokens: aiConfig.maxTokens,
        stream: false
      },
      timeout: aiConfig.timeout,
    });
    
    if (response.status === 200) {
      console.log(`✅ DeepSeek AI分析完成`);
      return response.data.choices[0].message.content;
    } else {
      throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
    }
  } catch (error) {
    console.error('❌ DeepSeek API调用失败:', error.message);
    throw new Error(`DeepSeek AI分析失败: ${error.message}`);
  }
}

/**
 * 智能识别产品类型并获取详细规格参数
 * @param {Array<String>} productNames 产品名称列表
 * @returns {Promise<Object>} 产品信息和类型识别结果
 */
async function analyzeProductSpecs(productNames) {
  const systemPrompt = `你是一个专业的产品分析专家，具有丰富的各领域产品知识。

任务：分析给定的产品列表，识别产品类型，并获取详细的技术规格参数。

要求：
1. 首先识别所有产品的类型分类（如：智能手机、笔记本电脑、耳机、相机、汽车等）
2. 判断产品是否属于同一类别，如果不是，说明跨类别对比的注意事项
3. 为每个产品提供详细的技术规格参数，包括但不限于：
   - 基本信息
   - 核心技术参数（根据产品类型而定）
   - 设计和外观参数
   - 性能指标
   - 特色功能

4. 请使用JSON格式返回结果，结构如下：
{
  "productCategory": "产品主要类别",
  "isSameCategory": true/false,
  "crossCategoryNote": "跨类别对比说明（如果适用）",
  "products": [
    {
      "name": "产品名称",
      "category": "具体分类",
      "basicInfo": {
        "brand": "品牌",
        "model": "型号",
        "releaseDate": "发布时间",
        "priceRange": "价格区间"
      },
      "specifications": {
        // 根据产品类型的具体规格参数
      },
      "keyFeatures": ["特色功能1", "特色功能2"]
    }
  ]
}

注意：
- 请基于你的知识库提供准确的技术参数
- 如果某些参数不确定，请标注"待确认"
- 价格请提供大概的市场价格区间
- 发布时间精确到年月即可`;

  const userPrompt = `请分析以下产品并提供详细的技术规格参数：

产品列表：
${productNames.map((name, index) => `${index + 1}. ${name}`).join('\n')}

请返回JSON格式的分析结果。`;

  const response = await callDeepSeekAPI(userPrompt, systemPrompt);
  
  try {
    // 尝试解析JSON响应
    const jsonMatch = response.match(/```json\s*([\s\S]*?)\s*```/) || response.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      const jsonStr = jsonMatch[1] || jsonMatch[0];
      return JSON.parse(jsonStr);
    } else {
      // 如果没有找到JSON，返回原始响应
      return {
        productCategory: "未知",
        isSameCategory: true,
        rawResponse: response,
        products: productNames.map(name => ({ name, category: "未知" }))
      };
    }
  } catch (parseError) {
    console.warn('JSON解析失败，返回原始响应');
    return {
      productCategory: "未知",
      isSameCategory: true,
      rawResponse: response,
      products: productNames.map(name => ({ name, category: "未知" }))
    };
  }
}

/**
 * 生成智能产品对比报告
 * @param {Object} productSpecs 产品规格分析结果
 * @returns {Promise<String>} 对比分析报告
 */
async function generateComparisonReport(productSpecs) {
  const systemPrompt = `你是一个资深的产品评测专家，擅长进行深入的产品对比分析。

请根据提供的产品信息，生成一份专业、全面的产品对比报告。

报告要求：
1. **概述**: 简要介绍对比的产品和其类别特点
2. **核心参数对比表**: 以表格形式对比关键技术参数
3. **性能分析**: 详细分析各产品在不同方面的表现
4. **优缺点分析**: 客观评价每个产品的优势和不足
5. **使用场景推荐**: 根据不同用户需求推荐合适的产品
6. **性价比分析**: 结合价格和功能进行性价比评估
7. **购买建议**: 提供具体的购买建议和注意事项

报告风格：
- 专业客观，基于技术参数分析
- 语言通俗易懂，避免过于技术化
- 结构清晰，便于阅读
- 提供实用的建议

如果是跨类别产品对比，请特别说明对比的局限性和参考价值。`;

  const userPrompt = `请基于以下产品信息生成详细的对比分析报告：

${JSON.stringify(productSpecs, null, 2)}

请生成一份完整的产品对比报告。`;

  return await callDeepSeekAPI(userPrompt, systemPrompt, {
    temperature: 0.4,
    maxTokens: 4000
  });
}

/**
 * 主要的通用产品对比函数
 * @param {Array<String>} productNames 产品名称列表
 * @param {Object} options 对比选项
 * @returns {Promise<Object>} 产品对比结果
 */
async function compareProductsAI(productNames, options = {}) {
  try {
    console.log(`\n🔍 开始AI智能产品对比分析...`);
    console.log(`待对比产品: ${productNames.join(', ')}`);
    
    // 验证输入
    if (!Array.isArray(productNames) || productNames.length < 2) {
      throw new Error('至少需要提供2个产品名称进行对比');
    }
    
    if (productNames.length > 6) {
      throw new Error('最多支持6个产品同时对比（AI性能限制）');
    }

    // 清理和标准化产品名称
    const cleanedNames = productNames.map(name => name.trim()).filter(name => name.length > 0);
    
    if (cleanedNames.length !== productNames.length) {
      console.warn('⚠️ 已过滤空白的产品名称');
    }

    // 第一步：智能分析产品规格
    console.log(`\n📊 第一步：分析产品类型和技术规格...`);
    const productSpecs = await analyzeProductSpecs(cleanedNames);
    
    // 显示产品类型识别结果
    console.log(`✅ 产品类别识别: ${productSpecs.productCategory}`);
    console.log(`✅ 同类别产品: ${productSpecs.isSameCategory ? '是' : '否'}`);
    
    if (!productSpecs.isSameCategory && productSpecs.crossCategoryNote) {
      console.log(`⚠️ 跨类别对比提示: ${productSpecs.crossCategoryNote}`);
    }

    // 第二步：生成详细对比报告
    console.log(`\n📝 第二步：生成智能对比报告...`);
    const comparisonReport = await generateComparisonReport(productSpecs);
    
    // 构建返回结果
    const result = {
      success: true,
      requestedProducts: cleanedNames,
      productCategory: productSpecs.productCategory,
      isSameCategory: productSpecs.isSameCategory,
      crossCategoryNote: productSpecs.crossCategoryNote || null,
      productSpecs: productSpecs.products || [],
      comparisonReport: comparisonReport,
      analysisTimestamp: new Date().toISOString(),
      productCount: cleanedNames.length,
      aiModel: DEEPSEEK_MODEL
    };
    
    console.log(`\n✅ AI产品对比分析完成！`);
    console.log(`📱 产品类别: ${result.productCategory}`);
    console.log(`🔢 对比产品数量: ${result.productCount}`);
    
    return result;
    
  } catch (error) {
    console.error(`❌ AI产品对比失败: ${error.message}`);
    return {
      success: false,
      error: error.message,
      requestedProducts: productNames,
      productCategory: null,
      isSameCategory: null,
      productSpecs: [],
      comparisonReport: null,
      analysisTimestamp: new Date().toISOString(),
      productCount: 0,
      aiModel: DEEPSEEK_MODEL
    };
  }
}

/**
 * 保存对比结果到文件
 * @param {Object} result 对比结果
 * @param {String} filename 文件名（可选）
 * @returns {Promise<String>} 保存的文件路径
 */
async function saveComparisonResult(result, filename = null) {
  try {
    if (!filename) {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const category = result.productCategory ? result.productCategory.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '_') : 'unknown';
      filename = `ai_product_comparison_${category}_${timestamp}.json`;
    }
    
    const filePath = path.join(__dirname, filename);
    
    // 保存完整结果
    await fs.promises.writeFile(filePath, JSON.stringify(result, null, 2), 'utf8');
    
    // 同时保存一个纯文本版本的分析报告
    if (result.success && result.comparisonReport) {
      const txtFilename = filename.replace('.json', '.txt');
      const txtFilePath = path.join(__dirname, txtFilename);
      
      const txtContent = `AI智能产品对比分析报告
===========================================
生成时间: ${result.analysisTimestamp}
AI模型: ${result.aiModel}
产品类别: ${result.productCategory}
对比产品: ${result.requestedProducts.join(', ')}
是否同类别: ${result.isSameCategory ? '是' : '否'}
${result.crossCategoryNote ? `跨类别说明: ${result.crossCategoryNote}` : ''}

${result.comparisonReport}

===========================================
技术规格详情:
${JSON.stringify(result.productSpecs, null, 2)}`;
      
      await fs.promises.writeFile(txtFilePath, txtContent, 'utf8');
      console.log(`📄 分析报告已保存: ${txtFilename}`);
    }
    
    console.log(`💾 对比结果已保存: ${filename}`);
    return filePath;
    
  } catch (error) {
    console.error(`保存文件失败: ${error.message}`);
    throw error;
  }
}

/**
 * 快速产品建议功能
 * @param {String} category 产品类别
 * @param {Object} requirements 用户需求
 * @returns {Promise<Array>} 推荐产品列表
 */
async function getProductRecommendations(category, requirements = {}) {
  try {
    const systemPrompt = `你是一个专业的产品推荐专家，根据用户的需求推荐合适的产品。

请根据用户指定的产品类别和需求，推荐3-5个当前市场上的优质产品。

推荐要求：
1. 产品必须是真实存在的，有具体型号
2. 覆盖不同价位段（高中低端）
3. 考虑不同用户群体的需求
4. 提供推荐理由

返回JSON格式：
{
  "category": "产品类别",
  "recommendations": [
    {
      "name": "产品名称",
      "brand": "品牌",
      "priceRange": "价格区间",
      "targetUser": "目标用户群体",
      "reason": "推荐理由",
      "keyFeatures": ["特色1", "特色2"]
    }
  ]
}`;

    const userPrompt = `请为以下需求推荐产品：

产品类别: ${category}
用户需求: ${JSON.stringify(requirements, null, 2)}

请推荐3-5个产品并说明推荐理由。`;

    const response = await callDeepSeekAPI(userPrompt, systemPrompt);
    
    try {
      const jsonMatch = response.match(/```json\s*([\s\S]*?)\s*```/) || response.match(/\{[\s\S]*\}/);
      if (jsonMatch) {
        const jsonStr = jsonMatch[1] || jsonMatch[0];
        return JSON.parse(jsonStr);
      }
    } catch (parseError) {
      console.warn('推荐结果JSON解析失败');
    }
    
    return { category, rawResponse: response, recommendations: [] };
    
  } catch (error) {
    console.error(`产品推荐失败: ${error.message}`);
    throw error;
  }
}

/**
 * 演示函数 - 展示不同类型产品对比
 */
async function demo() {
  console.log('='.repeat(80));
  console.log('🤖 AI智能全领域产品对比工具演示');
  console.log('='.repeat(80));
  
  const demos = [
    {
      title: '智能手机对比',
      products: ['iPhone 15 Pro', 'Samsung Galaxy S24 Ultra', 'Google Pixel 8 Pro']
    },
    {
      title: '笔记本电脑对比', 
      products: ['MacBook Pro M3', 'ThinkPad X1 Carbon', 'Dell XPS 13']
    },
    {
      title: '跨类别产品对比（演示）',
      products: ['iPhone 15', 'iPad Air', 'AirPods Pro']
    }
  ];
  
  for (let i = 0; i < demos.length; i++) {
    const demo = demos[i];
    console.log(`\n📋 演示${i + 1}: ${demo.title}`);
    console.log(`对比产品: ${demo.products.join(' vs ')}`);
    
    try {
      const result = await compareProductsAI(demo.products);
      if (result.success) {
        console.log(`\n✅ 分析成功 - 产品类别: ${result.productCategory}`);
        console.log('📊 对比报告预览:');
        console.log(result.comparisonReport.substring(0, 300) + '...(省略)');
        
        // 保存结果
        await saveComparisonResult(result, `demo${i + 1}_${demo.title.replace(/[^a-zA-Z0-9]/g, '_')}.json`);
      } else {
        console.log(`❌ 分析失败: ${result.error}`);
      }
    } catch (error) {
      console.error(`演示${i + 1}执行失败:`, error.message);
    }
    
    // 演示间隔
    if (i < demos.length - 1) {
      console.log('\n' + '-'.repeat(40));
      await new Promise(resolve => setTimeout(resolve, 2000));
    }
  }
  
  console.log('\n✅ 所有演示完成！');
}

// 如果直接运行此文件，执行演示
if (require.main === module) {
  // 检查环境变量
  if (!DEEPSEEK_API_KEY || DEEPSEEK_API_KEY === 'your_deepseek_api_key') {
    console.error('❌ 请设置DEEPSEEK_API_KEY环境变量');
    console.log('示例: export DEEPSEEK_API_KEY=your_actual_api_key');
    process.exit(1);
  }
  
  console.log('🤖 AI智能产品对比工具');
  console.log(`AI模型: ${DEEPSEEK_MODEL}`);
  console.log('支持全领域产品智能对比分析');
  
  // 运行演示
  demo().catch(error => {
    console.error('演示执行失败:', error.message);
    process.exit(1);
  });
}

module.exports = {
  compareProductsAI,
  analyzeProductSpecs,
  generateComparisonReport,
  saveComparisonResult,
  getProductRecommendations
};