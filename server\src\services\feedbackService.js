const Feedback = require('../models/Feedback');
const mongoose = require('mongoose');
const notificationService = require('./notificationService');
const socketService = require('./socketService');

/**
 * 创建反馈
 * @param {String} userId 用户ID
 * @param {String} content 反馈内容
 * @param {String} type 反馈类型
 * @param {Array} images 图片URL数组
 * @param {Object} deviceInfo 设备信息
 * @returns {Promise<Object>} 创建的反馈
 */
const createFeedback = async (userId, content, type, images = [], deviceInfo = {}) => {
  // 创建反馈
  const feedback = new Feedback({
    userId,
    content,
    type,
    images,
    deviceInfo
  });

  await feedback.save();
  
  // 通知管理员（这里可以根据实际需求调整）
  try {
    // 查找所有管理员（在实际应用中，你需要有一个管理员角色的字段）
    const User = mongoose.model('User');
    const admins = await User.find({ 
      isAdmin: true,  // 假设User模型中有isAdmin字段标识管理员
      isActive: true
    }).select('_id');
    
    // 向每个管理员发送通知
    for (const admin of admins) {
      const notificationContent = `新的用户反馈：${content.substring(0, 20)}${content.length > 20 ? '...' : ''}`;
      
      const notification = await notificationService.createNotification(
        admin._id,
        userId,
        'new_feedback',
        notificationContent,
        feedback._id,
        'feedback',
        {
          feedbackType: type
        }
      );
      
      // 发送实时通知
      socketService.sendNotification(admin._id, notification);
    }
  } catch (error) {
    console.error('发送反馈通知失败:', error);
    // 这里我们不抛出错误，因为即使通知失败，反馈依然应该被创建
  }
  
  return feedback;
};

/**
 * 获取用户的反馈列表
 * @param {String} userId 用户ID
 * @param {Number} page 页码
 * @param {Number} limit 每页数量
 * @returns {Promise<Object>} 反馈列表和分页信息
 */
const getUserFeedbacks = async (userId, page = 1, limit = 10) => {
  // 获取总数
  const total = await Feedback.countDocuments({ userId });
  
  // 获取数据
  const feedbacks = await Feedback.find({ userId })
    .sort({ createdAt: -1 })
    .skip((page - 1) * limit)
    .limit(limit)
    .lean();
  
  return {
    feedbacks,
    pagination: {
      total,
      page,
      limit,
      pages: Math.ceil(total / limit)
    }
  };
};

/**
 * 获取所有反馈（管理员用）
 * @param {Number} page 页码
 * @param {Number} limit 每页数量
 * @param {String} status 状态筛选
 * @param {String} type 类型筛选
 * @returns {Promise<Object>} 反馈列表和分页信息
 */
const getAllFeedbacks = async (page = 1, limit = 10, status = 'all', type = 'all') => {
  // 构建查询条件
  const query = {};
  
  if (status !== 'all') {
    query.status = status;
  }
  
  if (type !== 'all') {
    query.type = type;
  }
  
  // 获取总数
  const total = await Feedback.countDocuments(query);
  
  // 获取数据
  const feedbacks = await Feedback.find(query)
    .sort({ createdAt: -1 })
    .skip((page - 1) * limit)
    .limit(limit)
    .populate({
      path: 'userId',
      select: '_id nickname avatar phone',
      options: { lean: true }
    })
    .lean();
  
  return {
    feedbacks,
    pagination: {
      total,
      page,
      limit,
      pages: Math.ceil(total / limit)
    }
  };
};

/**
 * 获取反馈详情
 * @param {String} feedbackId 反馈ID
 * @returns {Promise<Object>} 反馈详情
 */
const getFeedbackDetail = async (feedbackId) => {
  // 验证ObjectId有效性
  if (!mongoose.Types.ObjectId.isValid(feedbackId)) {
    throw new Error('无效的反馈ID');
  }
  
  // 获取反馈
  const feedback = await Feedback.findById(feedbackId)
    .populate({
      path: 'userId',
      select: '_id nickname avatar phone',
      options: { lean: true }
    })
    .populate({
      path: 'adminReply.adminId',
      select: '_id nickname avatar',
      options: { lean: true }
    })
    .lean();
  
  if (!feedback) {
    throw new Error('反馈不存在');
  }
  
  return feedback;
};

/**
 * 更新反馈状态（管理员用）
 * @param {String} feedbackId 反馈ID
 * @param {String} status 新状态
 * @param {String} adminId 管理员ID
 * @returns {Promise<Object>} 更新后的反馈
 */
const updateFeedbackStatus = async (feedbackId, status, adminId) => {
  // 验证ObjectId有效性
  if (!mongoose.Types.ObjectId.isValid(feedbackId)) {
    throw new Error('无效的反馈ID');
  }
  
  // 获取反馈
  const feedback = await Feedback.findById(feedbackId);
  
  if (!feedback) {
    throw new Error('反馈不存在');
  }
  
  // 更新状态
  feedback.status = status;
  await feedback.save();
  
  // 发送通知给用户
  try {
    const statusMap = {
      'pending': '待处理',
      'processing': '处理中',
      'resolved': '已解决',
      'rejected': '已拒绝'
    };
    
    const notificationContent = `您的反馈"${feedback.content.substring(0, 20)}${feedback.content.length > 20 ? '...' : ''}"状态已更新为：${statusMap[status]}`;
    
    const notification = await notificationService.createNotification(
      feedback.userId,
      adminId,
      'feedback_status_updated',
      notificationContent,
      feedbackId,
      'feedback',
      {
        status
      }
    );
    
    // 发送实时通知
    socketService.sendNotification(feedback.userId, notification);
  } catch (error) {
    console.error('发送反馈状态更新通知失败:', error);
  }
  
  return feedback;
};

/**
 * 管理员回复反馈
 * @param {String} feedbackId 反馈ID
 * @param {String} content 回复内容
 * @param {String} adminId 管理员ID
 * @returns {Promise<Object>} 更新后的反馈
 */
const replyFeedback = async (feedbackId, content, adminId) => {
  // 验证ObjectId有效性
  if (!mongoose.Types.ObjectId.isValid(feedbackId)) {
    throw new Error('无效的反馈ID');
  }
  
  // 获取反馈
  const feedback = await Feedback.findById(feedbackId);
  
  if (!feedback) {
    throw new Error('反馈不存在');
  }
  
  // 更新回复
  feedback.adminReply = {
    content,
    replyAt: new Date(),
    adminId
  };
  
  // 如果状态是待处理，自动更新为处理中
  if (feedback.status === 'pending') {
    feedback.status = 'processing';
  }
  
  await feedback.save();
  
  // 发送通知给用户
  try {
    const notificationContent = `您的反馈"${feedback.content.substring(0, 20)}${feedback.content.length > 20 ? '...' : ''}"收到了回复`;
    
    const notification = await notificationService.createNotification(
      feedback.userId,
      adminId,
      'feedback_replied',
      notificationContent,
      feedbackId,
      'feedback'
    );
    
    // 发送实时通知
    socketService.sendNotification(feedback.userId, notification);
  } catch (error) {
    console.error('发送反馈回复通知失败:', error);
  }
  
  return feedback;
};

module.exports = {
  createFeedback,
  getUserFeedbacks,
  getAllFeedbacks,
  getFeedbackDetail,
  updateFeedbackStatus,
  replyFeedback
}; 