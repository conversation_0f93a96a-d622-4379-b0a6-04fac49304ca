const fs = require('fs');
const path = require('path');
const crypto = require('crypto');
const { promisify } = require('util');

// 将fs的方法转换为Promise形式
const mkdir = promisify(fs.mkdir);
const stat = promisify(fs.stat);
const unlink = promisify(fs.unlink);

/**
 * 确保目录存在，如果不存在则创建
 * @param {string} directory 目录路径
 */
const ensureDirectoryExists = async (directory) => {
  try {
    await stat(directory);
  } catch (error) {
    if (error.code === 'ENOENT') {
      await mkdir(directory, { recursive: true });
    } else {
      throw error;
    }
  }
};

/**
 * 生成唯一的文件名
 * @param {string} originalname 原始文件名
 * @returns {string} 生成的唯一文件名
 */
const generateUniqueFilename = (originalname) => {
  const timestamp = Date.now();
  const randomString = crypto.randomBytes(8).toString('hex');
  const extension = path.extname(originalname);
  const filename = `${timestamp}_${randomString}${extension}`;
  return filename;
};

/**
 * 获取文件的MIME类型
 * @param {string} filename 文件名
 * @returns {string} 文件类型
 */
const getFileType = (filename) => {
  const extension = path.extname(filename).toLowerCase();
  const mimeTypes = {
    '.jpg': 'image/jpeg',
    '.jpeg': 'image/jpeg',
    '.png': 'image/png',
    '.gif': 'image/gif',
    '.webp': 'image/webp',
    '.mp4': 'video/mp4',
    '.mov': 'video/quicktime',
    '.avi': 'video/x-msvideo',
    '.wmv': 'video/x-ms-wmv',
    '.mp3': 'audio/mpeg',
    '.wav': 'audio/wav',
    '.aac': 'audio/aac',
    '.pdf': 'application/pdf',
    '.doc': 'application/msword',
    '.docx': 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
    '.xls': 'application/vnd.ms-excel',
    '.xlsx': 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
    '.ppt': 'application/vnd.ms-powerpoint',
    '.pptx': 'application/vnd.openxmlformats-officedocument.presentationml.presentation'
  };
  
  return mimeTypes[extension] || 'application/octet-stream';
};

/**
 * 检查文件类型是否是图片
 * @param {string} mimeType 文件MIME类型
 * @returns {boolean} 是否是图片
 */
const isImage = (mimeType) => {
  return mimeType.startsWith('image/');
};

/**
 * 检查文件类型是否是视频
 * @param {string} mimeType 文件MIME类型
 * @returns {boolean} 是否是视频
 */
const isVideo = (mimeType) => {
  return mimeType.startsWith('video/');
};

/**
 * 检查文件类型是否是音频
 * @param {string} mimeType 文件MIME类型
 * @returns {boolean} 是否是音频
 */
const isAudio = (mimeType) => {
  return mimeType.startsWith('audio/');
};

/**
 * 获取文件大小的可读格式
 * @param {number} size 文件大小(字节)
 * @returns {string} 可读格式的文件大小
 */
const getHumanReadableSize = (size) => {
  const units = ['B', 'KB', 'MB', 'GB', 'TB'];
  if (size === 0) return '0 B';
  const i = parseInt(Math.floor(Math.log(size) / Math.log(1024)));
  return Math.round(size / Math.pow(1024, i), 2) + ' ' + units[i];
};

/**
 * 删除文件
 * @param {string} filePath 文件路径
 */
const deleteFile = async (filePath) => {
  try {
    await unlink(filePath);
    return true;
  } catch (error) {
    console.error(`删除文件失败: ${filePath}`, error);
    return false;
  }
};

/**
 * 递归删除目录及其内容
 * @param {string} directoryPath 目录路径
 */
const removeDirectory = async (directoryPath) => {
  try {
    // 检查目录是否存在
    try {
      await stat(directoryPath);
    } catch (error) {
      if (error.code === 'ENOENT') {
        return; // 目录不存在，无需操作
      }
      throw error;
    }
    
    // 读取目录内容
    const entries = await promisify(fs.readdir)(directoryPath, { withFileTypes: true });
    
    // 递归删除文件和子目录
    for (const entry of entries) {
      const fullPath = path.join(directoryPath, entry.name);
      
      if (entry.isDirectory()) {
        await removeDirectory(fullPath);
      } else {
        await unlink(fullPath);
      }
    }
    
    // 删除空目录
    await promisify(fs.rmdir)(directoryPath);
  } catch (error) {
    console.error(`删除目录失败: ${directoryPath}`, error);
    throw error;
  }
};

module.exports = {
  ensureDirectoryExists,
  generateUniqueFilename,
  getFileType,
  isImage,
  isVideo,
  isAudio,
  getHumanReadableSize,
  deleteFile,
  removeDirectory
}; 