<!--pages/user/profile/profile.wxml-->
<view class="page">
  <view class="container p-30">
    <!-- 头像区域（暂时不支持修改） -->
    <view class="avatar-section flex justify-center mb-40 py-20">
      <view class="flex flex-column items-center">
        <view class="avatar avatar-xl">
          <image src="{{formData.avatar || '/assets/images/default-avatar.png'}}"></image>
        </view>
        <view class="avatar-tip text-center text-secondary font-size-small mt-10">
          暂不支持修改头像
        </view>
      </view>
    </view>

    <!-- 表单区域 -->
    <form catchsubmit="onSubmit">
      <!-- 昵称 -->
      <view class="form-group mb-40">
        <view class="form-label required">昵称</view>
        <view class="form-input-wrap">
          <input
            class="form-input"
            name="nickname"
            value="{{formData.nickname}}"
            placeholder="请输入昵称（2-20个字符）"
            maxlength="20"
            bindinput="onInputChange"
            data-field="nickname"
          />
        </view>
        <view wx:if="{{errors.nickname}}" class="form-error mt-10">
          {{errors.nickname}}
        </view>
      </view>

      <!-- 性别 -->
      <view class="form-group mb-40">
        <view class="form-label">性别</view>
        <view class="gender-text-selector">
          <view 
            class="gender-text-option {{formData.gender === 'male' ? 'active' : ''}}"
            bindtap="onGenderSelect"
            data-gender="male"
          >
            男
          </view>
          <view 
            class="gender-text-option {{formData.gender === 'female' ? 'active' : ''}}"
            bindtap="onGenderSelect"
            data-gender="female"
          >
            女
          </view>
          <view 
            class="gender-text-option {{formData.gender === 'secret' ? 'active' : ''}}"
            bindtap="onGenderSelect"
            data-gender="secret"
          >
            保密
          </view>
        </view>
      </view>

      <!-- 年龄 -->
      <view class="form-group mb-40">
        <view class="form-label">年龄</view>
        <view class="form-input-wrap">
          <input
            class="form-input"
            name="age"
            type="number"
            value="{{formData.age}}"
            placeholder="请输入年龄"
            bindinput="onInputChange"
            data-field="age"
          />
        </view>
        <view wx:if="{{errors.age}}" class="form-error mt-10">
          {{errors.age}}
        </view>
      </view>

      <!-- 职业 -->
      <view class="form-group mb-40">
        <view class="form-label">职业</view>
        <view class="form-input-wrap">
          <input
            class="form-input"
            name="occupation"
            value="{{formData.occupation}}"
            placeholder="请输入职业（最多50个字符）"
            maxlength="50"
            bindinput="onInputChange"
            data-field="occupation"
          />
        </view>
        <view wx:if="{{errors.occupation}}" class="form-error mt-10">
          {{errors.occupation}}
        </view>
      </view>

      <!-- 地区 -->
      <view class="form-group mb-40">
        <view class="form-label">地区</view>
        <view class="form-input-wrap">
          <input
            class="form-input"
            name="region"
            value="{{formData.region}}"
            placeholder="请输入所在地区（最多50个字符）"
            maxlength="50"
            bindinput="onInputChange"
            data-field="region"
          />
        </view>
        <view wx:if="{{errors.region}}" class="form-error mt-10">
          {{errors.region}}
        </view>
      </view>

      <!-- 提交按钮 -->
      <view class="mt-60 pb-40 flex justify-center">
        <button 
          class="btn btn-primary btn-medium"
          form-type="submit"
          disabled="{{isSubmitting}}"
        >
          {{isSubmitting ? '保存中...' : '保存'}}
        </button>
      </view>
    </form>
  </view>
</view> 