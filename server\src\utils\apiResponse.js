/**
 * 标准化API响应格式
 */

// 成功响应
exports.successResponse = (res, statusCode = 200, data = null, message = '操作成功') => {
  return res.status(statusCode).json({
    success: true,
    message,
    data
  });
};

// 错误响应
exports.errorResponse = (res, statusCode = 400, message = '操作失败', errors = null) => {
  return res.status(statusCode).json({
    success: false,
    message,
    errors
  });
};

// 分页响应
exports.paginatedResponse = (res, statusCode = 200, data, page, limit, total, message = '获取数据成功') => {
  return res.status(statusCode).json({
    success: true,
    message,
    data,
    pagination: {
      page,
      limit,
      total,
      pages: Math.ceil(total / limit)
    }
  });
}; 