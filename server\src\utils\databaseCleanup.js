const mongoose = require('mongoose');
const Product = require('../models/Product');
require('dotenv').config();

/**
 * 数据库清理和修复工具
 * 用于解决版本升级后的数据库结构冲突问题
 */

class DatabaseCleanup {
  constructor() {
    this.dbUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/xuanxuan';
  }

  /**
   * 连接数据库
   */
  async connect() {
    try {
      await mongoose.connect(this.dbUri, {
        useNewUrlParser: true,
        useUnifiedTopology: true,
      });
      console.log('📦 数据库连接成功');
    } catch (error) {
      console.error('❌ 数据库连接失败:', error);
      throw error;
    }
  }

  /**
   * 关闭数据库连接
   */
  async disconnect() {
    try {
      await mongoose.connection.close();
      console.log('📦 数据库连接已关闭');
    } catch (error) {
      console.error('❌ 关闭数据库连接失败:', error);
    }
  }

  /**
   * 查看当前数据库中的所有索引
   */
  async listIndexes() {
    try {
      const indexes = await Product.collection.getIndexes();
      console.log('📋 当前数据库索引列表:');
      Object.keys(indexes).forEach(indexName => {
        console.log(`  - ${indexName}:`, indexes[indexName]);
      });
      return indexes;
    } catch (error) {
      console.error('❌ 获取索引列表失败:', error);
      throw error;
    }
  }

  /**
   * 删除有问题的旧索引
   */
  async dropProblematicIndexes() {
    try {
      console.log('🔍 开始检查和删除有问题的索引...');
      
      const indexes = await Product.collection.getIndexes();
      const problematicIndexes = [];

      // 检查是否存在 baseSkuId 相关的索引
      Object.keys(indexes).forEach(indexName => {
        if (indexName.includes('baseSkuId')) {
          problematicIndexes.push(indexName);
        }
      });

      if (problematicIndexes.length > 0) {
        console.log(`🗑️ 发现 ${problematicIndexes.length} 个需要删除的旧索引:`);
        
        for (const indexName of problematicIndexes) {
          try {
            await Product.collection.dropIndex(indexName);
            console.log(`  ✅ 成功删除索引: ${indexName}`);
          } catch (error) {
            console.log(`  ⚠️ 删除索引失败 ${indexName}:`, error.message);
          }
        }
      } else {
        console.log('✅ 未发现需要删除的旧索引');
      }

      return problematicIndexes;
    } catch (error) {
      console.error('❌ 删除索引过程失败:', error);
      throw error;
    }
  }

  /**
   * 重建所有索引
   */
  async rebuildIndexes() {
    try {
      console.log('🔨 开始重建索引...');
      
      // 删除所有现有索引（除了 _id 索引）
      await Product.collection.dropIndexes();
      console.log('🗑️ 已清空所有旧索引');
      
      // 重新创建索引（根据当前模型定义）
      await Product.createIndexes();
      console.log('✅ 索引重建完成');
      
      // 验证新索引
      const newIndexes = await Product.collection.getIndexes();
      console.log('📋 新索引列表:');
      Object.keys(newIndexes).forEach(indexName => {
        console.log(`  - ${indexName}`);
      });
      
    } catch (error) {
      console.error('❌ 重建索引失败:', error);
      throw error;
    }
  }

  /**
   * 清理所有产品数据
   */
  async clearAllProducts() {
    try {
      console.log('🗑️ 开始清理产品数据...');
      const result = await Product.deleteMany({});
      console.log(`✅ 已清理 ${result.deletedCount} 个产品记录`);
      return result.deletedCount;
    } catch (error) {
      console.error('❌ 清理产品数据失败:', error);
      throw error;
    }
  }

  /**
   * 检查数据库健康状态
   */
  async checkDatabaseHealth() {
    try {
      console.log('🔍 开始数据库健康检查...');
      
      // 检查产品数量
      const productCount = await Product.countDocuments();
      console.log(`📊 当前产品数量: ${productCount}`);
      
      // 检查索引状态
      const indexes = await Product.collection.getIndexes();
      const indexCount = Object.keys(indexes).length;
      console.log(`📊 当前索引数量: ${indexCount}`);
      
      // 检查是否有重复的 skuId
      const duplicateSkuIds = await Product.aggregate([
        { $group: { _id: '$skuId', count: { $sum: 1 } } },
        { $match: { count: { $gt: 1 } } }
      ]);
      
      if (duplicateSkuIds.length > 0) {
        console.log('⚠️ 发现重复的 skuId:');
        duplicateSkuIds.forEach(dup => {
          console.log(`  - ${dup._id}: ${dup.count} 个重复`);
        });
      } else {
        console.log('✅ 未发现重复的 skuId');
      }
      
      return {
        productCount,
        indexCount,
        duplicateSkuIds: duplicateSkuIds.length,
        healthy: duplicateSkuIds.length === 0
      };
      
    } catch (error) {
      console.error('❌ 数据库健康检查失败:', error);
      throw error;
    }
  }

  /**
   * 完整的数据库重置流程
   */
  async fullReset() {
    try {
      console.log('🚀 开始完整的数据库重置流程...\n');
      
      // 1. 检查当前状态
      console.log('=== 步骤 1: 检查当前状态 ===');
      await this.checkDatabaseHealth();
      
      // 2. 清理所有产品数据
      console.log('\n=== 步骤 2: 清理产品数据 ===');
      await this.clearAllProducts();
      
      // 3. 重建索引
      console.log('\n=== 步骤 3: 重建索引 ===');
      await this.rebuildIndexes();
      
      // 4. 最终检查
      console.log('\n=== 步骤 4: 最终检查 ===');
      const finalHealth = await this.checkDatabaseHealth();
      
      console.log('\n🎉 数据库重置完成!');
      console.log('现在可以重新导入数据了。');
      
      return finalHealth;
      
    } catch (error) {
      console.error('❌ 数据库重置过程失败:', error);
      throw error;
    }
  }
}

/**
 * 命令行接口
 */
async function runCleanup() {
  const cleanup = new DatabaseCleanup();
  
  try {
    await cleanup.connect();
    
    const args = process.argv.slice(2);
    
    if (args.includes('--list-indexes')) {
      await cleanup.listIndexes();
    } else if (args.includes('--drop-indexes')) {
      await cleanup.dropProblematicIndexes();
    } else if (args.includes('--rebuild-indexes')) {
      await cleanup.rebuildIndexes();
    } else if (args.includes('--clear-data')) {
      await cleanup.clearAllProducts();
    } else if (args.includes('--check-health')) {
      await cleanup.checkDatabaseHealth();
    } else if (args.includes('--full-reset')) {
      await cleanup.fullReset();
    } else {
      console.log('💡 使用说明:');
      console.log('  --list-indexes     查看所有索引');
      console.log('  --drop-indexes     删除有问题的索引');
      console.log('  --rebuild-indexes  重建所有索引');
      console.log('  --clear-data       清理所有产品数据');
      console.log('  --check-health     检查数据库健康状态');
      console.log('  --full-reset       完整重置（推荐）');
      console.log('\n🚀 推荐使用: npm run db:reset');
    }
    
  } catch (error) {
    console.error('❌ 操作失败:', error);
    process.exit(1);
  } finally {
    await cleanup.disconnect();
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  runCleanup();
}

module.exports = DatabaseCleanup; 