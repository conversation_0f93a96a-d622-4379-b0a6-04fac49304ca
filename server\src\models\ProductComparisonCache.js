const mongoose = require('mongoose');

/**
 * 产品对比缓存模型
 * 实现热度缓存机制、分层存储和增量缓存
 */
const ProductComparisonCacheSchema = new mongoose.Schema(
  {
    // 缓存标识 - 对比产品的组合唯一键
    comparisonKey: {
      type: String,
      required: true,
      unique: true
    },
    
    // 参与对比的产品信息
    productInfo: {
      productIds: [{
        type: mongoose.Schema.Types.ObjectId,
        ref: 'Product',
        required: true
      }],
      productNames: [{
        type: String,
        required: true
      }],
      productType: {
        type: String,
        required: true,
        enum: ['phone', 'laptop', 'tablet', 'headphones', 'smartwatch', 'other']
      }
    },
    
    // 缓存的对比数据
    cachedData: {
      // 对比表格数据
      comparisonTable: [{
        parameter: { type: String, required: true },
        category: { type: String, required: true },
        values: [{
          productId: { type: mongoose.Schema.Types.ObjectId, required: true },
          productName: { type: String, required: true },
          value: { type: String, required: true }
        }]
      }],
      
      // AI分析结果（高成本缓存项）
      aiAnalysis: [{
        parameter: { type: String, required: true },
        analysis: { type: String, required: true }
      }],
      
      // 格式化的产品基本信息
      formattedProducts: [{
        id: { type: mongoose.Schema.Types.ObjectId, required: true },
        skuId: { type: String, required: true },
        name: { type: String, required: true },
        displayName: { type: String }, // 🆕 用于显示的完整名称
        price: { type: Number },
        priceRange: { // 🆕 价格范围信息
          min: { type: Number },
          max: { type: Number }
        },
        configurations: [{ // 🆕 所有配置信息
          name: { type: String, required: true },
          ram: { type: String },
          storage: { type: String },
          price: { type: Number, required: true },
          available: { type: Boolean, default: true }
        }],
        defaultConfiguration: { type: String }, // 🆕 默认配置标识
        defaultConfigDetails: { // 🆕 默认配置的详细信息
          name: { type: String },
          ram: { type: String },
          storage: { type: String },
          price: { type: Number },
          available: { type: Boolean }
        },
        image: { type: String },
        brand: { type: String },
        productType: { type: String, required: true }
      }]
    },
    
    // 热度统计
    hotness: {
      // 访问次数
      hitCount: {
        type: Number,
        default: 1,
        min: 0
      },
      
      // 最后访问时间
      lastAccessTime: {
        type: Date,
        default: Date.now
      },
      
      // 首次创建时间
      firstAccessTime: {
        type: Date,
        default: Date.now
      },
      
      // 热度得分（综合访问次数和时间衰减）
      heatScore: {
        type: Number,
        default: 1,
        min: 0
      }
    },
    
    // 缓存元数据
    cacheMetadata: {
      // 数据版本（用于缓存失效）
      dataVersion: {
        type: String,
        default: '1.0'
      },
      
      // 缓存大小（字节）
      cacheSize: {
        type: Number,
        default: 0
      },
      
      // 缓存状态
      status: {
        type: String,
        enum: ['active', 'expired', 'invalidated'],
        default: 'active'
      },
      
      // AI分析生成时间
      aiGeneratedAt: {
        type: Date,
        default: Date.now
      }
    }
  },
  {
    timestamps: true
  }
);

// 索引优化
// 移除 comparisonKey 单独索引，unique: true 已自动创建唯一索引
ProductComparisonCacheSchema.index({ 'hotness.heatScore': -1 }); // 热度排序索引
ProductComparisonCacheSchema.index({ 'hotness.lastAccessTime': -1 }); // 访问时间索引
ProductComparisonCacheSchema.index({ 'cacheMetadata.status': 1, 'hotness.heatScore': -1 }); // 复合索引
ProductComparisonCacheSchema.index({ 'productInfo.productType': 1 }); // 产品类型索引

// 静态方法：生成对比键
ProductComparisonCacheSchema.statics.generateComparisonKey = function(productNames) {
  // 对产品名称排序以确保相同产品组合产生相同的键
  const sortedNames = [...productNames].sort();
  return `comparison_${sortedNames.join('_')}_${sortedNames.length}`;
};

// 静态方法：计算热度得分
ProductComparisonCacheSchema.statics.calculateHeatScore = function(hitCount, lastAccessTime, firstAccessTime) {
  const now = new Date();
  const timeSinceLastAccess = (now - lastAccessTime) / (1000 * 60 * 60 * 24); // 天数
  const totalDuration = (now - firstAccessTime) / (1000 * 60 * 60 * 24); // 总天数
  
  // 热度得分 = 访问次数 * 时间衰减因子 * 持续性因子
  const timeDecayFactor = Math.exp(-timeSinceLastAccess / 7); // 7天衰减
  const persistenceFactor = Math.min(totalDuration / 30, 1); // 30天内逐渐增强
  
  return hitCount * timeDecayFactor * persistenceFactor;
};

// 实例方法：更新热度
ProductComparisonCacheSchema.methods.updateHotness = function() {
  this.hotness.hitCount += 1;
  this.hotness.lastAccessTime = new Date();
  this.hotness.heatScore = this.constructor.calculateHeatScore(
    this.hotness.hitCount,
    this.hotness.lastAccessTime,
    this.hotness.firstAccessTime
  );
  return this.save();
};

// 实例方法：检查缓存是否过期
ProductComparisonCacheSchema.methods.isExpired = function(maxAge = 24 * 60 * 60 * 1000) {
  const now = new Date();
  const ageInMs = now - this.cacheMetadata.aiGeneratedAt;
  return ageInMs > maxAge || this.cacheMetadata.status !== 'active';
};

// 中间件：自动清理过期缓存
ProductComparisonCacheSchema.pre('find', function() {
  // 只查询活跃状态的缓存
  this.where({ 'cacheMetadata.status': 'active' });
});

module.exports = mongoose.model('ProductComparisonCache', ProductComparisonCacheSchema);