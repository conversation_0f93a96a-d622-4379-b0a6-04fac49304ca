const app = getApp();
const { question } = require('../../../utils/api');
const { formatTime } = require('../../../utils/util');
// 引入通用常量
const { REGION_LIST, OCCUPATION_LIST } = require('../../../utils/constants');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    title: '',
    scene: '',
    keyFactors: '',
    budget: {
      min: null,
      max: null,
      currency: 'CNY'
    },
    tags: [],
    // 添加标签状态数组
    tagStatus: {},
    options: ['', ''],
    // 新增：选项推荐理由数据
    optionRecommendReasons: [], // 与options数组对应的推荐理由数组
    // 新增：推荐理由展开状态
    reasonExpandedStatus: [], // 控制每个理由是否展开显示
    expiryTime: null,
    // 添加预计结束时间预览
    expiryTimePreview: '',
    // 添加持续时间数据
    duration: {
      days: 3,
      hours: 0
    },
    isAnonymous: false,
    requireReason: false,
    visibility: {
      type: 'public',
      filters: {
        gender: [],
        minAge: null,
        maxAge: null,
        regions: [],
        occupations: []
      }
    },
    // 筛选条件选择状态
    filterConditions: {
      gender: false,
      age: false,
      region: false,
      occupation: false
    },
    submitting: false,
    maxOptions: 5,
    minOptions: 2,
    // 地区和职业列表
    regionList: REGION_LIST,
    occupationList: OCCUPATION_LIST,
    // 标签选项
    tagOptions: ['手机', '电脑'],
    // 模板示例相关
    showTemplateModal: false,
    currentTemplate: null,
    // AI推荐相关
    showAiRecommendModal: false,
    questionInfo: {},
    // 模板示例数据
    templates: [
      {
        title: '[3000-4000元] 拍照强、续航好的安卓手机推荐',
        scene: '主要用于日常社交、拍照记录生活和旅行。经常外出一整天，需要电池续航有保障。不玩大型游戏，但会刷视频和使用导航。',
        keyFactors: '拍照（尤其是夜景和人像）\n电池续航\n系统流畅度',
        budget: {
          min: 3000,
          max: 4000,
          currency: 'CNY'
        },
        tags: ['手机', '拍照'],
        options: [
          '小米13：3999元，骁龙8Gen2，徕卡光学，4500mAh',
          'OPPO Reno10 Pro+：3899元，骁龙8+，哈苏影像，4700mAh',
          'vivo S17 Pro：3399元，天玑9200+，蔡司影像，4500mAh'
        ]
      }
    ]
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // 检查登录状态
    if (!app.globalData.isLoggedIn) {
      wx.showToast({
        title: '请先登录',
        icon: 'none'
      });
      setTimeout(() => {
        // 获取当前页面路径并传递给登录页面
        const pages = getCurrentPages();
        const currentPage = pages[pages.length - 1];
        const currentPagePath = `/${currentPage.route}`;
        
        wx.navigateTo({
          url: `/pages/login/login?redirectTo=${encodeURIComponent(currentPagePath)}`
        });
      }, 1500);
      return;
    }

    // 初始化标签状态
    const tagStatus = {};
    this.data.tagOptions.forEach(tag => {
      tagStatus[tag] = false;
    });
    
    // 初始化推荐理由数组，与options数组长度保持一致
    const optionRecommendReasons = new Array(this.data.options.length).fill(null);
    const reasonExpandedStatus = new Array(this.data.options.length).fill(false);
    
    this.setData({ 
      tagStatus,
      optionRecommendReasons,
      reasonExpandedStatus
    });
    
    // 初始化结束时间预览
    this.updateExpiryTimePreview();
  },

  /**
   * 输入标题
   */
  inputTitle: function(e) {
    this.setData({
      title: e.detail.value
    });
  },

  /**
   * 输入使用场景
   */
  inputScene: function(e) {
    this.setData({
      scene: e.detail.value
    });
  },

  /**
   * 输入关键考量因素
   */
  inputKeyFactors: function(e) {
    this.setData({
      keyFactors: e.detail.value
    });
  },
  
  /**
   * 输入预算最小值
   */
  inputBudgetMin: function(e) {
    this.setData({
      'budget.min': e.detail.value ? Number(e.detail.value) : null
    });
  },
  
  /**
   * 输入预算最大值
   */
  inputBudgetMax: function(e) {
    this.setData({
      'budget.max': e.detail.value ? Number(e.detail.value) : null
    });
  },
  
  /**
   * 选择标签
   */
  selectTag: function(e) {
    const { tag } = e.currentTarget.dataset;
    const { tags, tagStatus } = this.data;
    
    // 切换标签选中状态
    if (tagStatus[tag]) {
      // 取消选中
      const newTags = tags.filter(t => t !== tag);
      const newTagStatus = { ...tagStatus };
      newTagStatus[tag] = false;
      
      this.setData({
        tags: newTags,
        tagStatus: newTagStatus
      }, () => {
        // 回调确保视图已更新
        console.log('标签已取消选中:', tag, '当前选中标签:', this.data.tags);
      });
    } else {
      // 选中
      if (tags.length >= 2) {
        wx.showToast({
          title: '最多选择2个标签',
          icon: 'none'
        });
        return;
      }
      
      const newTags = [...tags, tag];
      const newTagStatus = { ...tagStatus };
      newTagStatus[tag] = true;
      
      this.setData({
        tags: newTags,
        tagStatus: newTagStatus
      }, () => {
        // 回调确保视图已更新
        console.log('标签已选中:', tag, '当前选中标签:', this.data.tags);
      });
    }
  },

  /**
   * 产品选项输入处理
   */
  onProductOptionInput: function(e) {
    const { index } = e.currentTarget.dataset;
    const { value } = e.detail;
    const { options, optionRecommendReasons } = this.data;
    
    options[index] = value;
    
    // 如果用户手动修改了内容，清除AI推荐理由
    if (optionRecommendReasons[index] && optionRecommendReasons[index].isAiRecommended) {
      optionRecommendReasons[index] = null;
    }
    
    this.setData({ 
      options,
      optionRecommendReasons
    });
  },

  /**
   * 产品选项选择处理
   */
  onProductOptionSelect: function(e) {
    const { index } = e.currentTarget.dataset;
    const { product, value } = e.detail;
    const { options } = this.data;
    
    // 更新选项值为选中的产品名称
    options[index] = value;
    this.setData({ options });
    
    console.log(`选项${index + 1}选择了产品:`, product.name);
  },

  /**
   * 产品选项清空处理
   */
  onProductOptionClear: function(e) {
    const { index } = e.currentTarget.dataset;
    const { options } = this.data;
    
    options[index] = '';
    this.setData({ options });
    
    console.log(`选项${index + 1}已清空`);
  },

  /**
   * 输入选项 (保留旧方法作为备用)
   */
  inputOption: function(e) {
    const { index } = e.currentTarget.dataset;
    const { options } = this.data;
    options[index] = e.detail.value;
    
    this.setData({ options });
  },

  /**
   * 添加选项
   */
  addOption: function() {
    const { options, maxOptions, optionRecommendReasons, reasonExpandedStatus } = this.data;
    
    if (options.length >= maxOptions) {
      wx.showToast({
        title: `最多添加${maxOptions}个选项`,
        icon: 'none'
      });
      return;
    }
    
    // 添加新的空选项和对应的推荐理由数据
    options.push('');
    optionRecommendReasons.push(null);
    reasonExpandedStatus.push(false);
    
    this.setData({ 
      options,
      optionRecommendReasons,
      reasonExpandedStatus
    });
  },

  /**
   * 删除选项
   */
  deleteOption: function(e) {
    const { index } = e.currentTarget.dataset;
    const { options, minOptions, optionRecommendReasons, reasonExpandedStatus } = this.data;
    
    if (options.length <= minOptions) {
      wx.showToast({
        title: `至少保留${minOptions}个选项`,
        icon: 'none'
      });
      return;
    }
    
    // 同时删除选项和对应的推荐理由数据
    options.splice(index, 1);
    optionRecommendReasons.splice(index, 1);
    reasonExpandedStatus.splice(index, 1);
    
    this.setData({ 
      options,
      optionRecommendReasons,
      reasonExpandedStatus
    });
  },

  /**
   * 输入持续时间（天）
   */
  inputDurationDays: function(e) {
    const days = e.detail.value ? parseInt(e.detail.value) : 0;
    // 限制最大天数为30天
    const limitedDays = days > 30 ? 30 : days;
    
    this.setData({
      'duration.days': limitedDays
    });
    
    if (days > 30) {
      wx.showToast({
        title: '最多设置30天',
        icon: 'none'
      });
    }
    
    // 更新预计结束时间预览
    this.updateExpiryTimePreview();
  },

  /**
   * 输入持续时间（小时）
   */
  inputDurationHours: function(e) {
    const hours = e.detail.value ? parseInt(e.detail.value) : 0;
    // 限制最大小时数为23小时
    const limitedHours = hours > 23 ? 23 : hours;
    
    this.setData({
      'duration.hours': limitedHours
    });
    
    if (hours > 23) {
      wx.showToast({
        title: '小时数应小于24',
        icon: 'none'
      });
    }
    
    // 更新预计结束时间预览
    this.updateExpiryTimePreview();
  },

  /**
   * 根据持续时间计算截止时间
   */
  calculateExpiryTime: function() {
    const { days, hours } = this.data.duration;
    
    // 如果持续时间为0，则不设置截止时间
    if (days === 0 && hours === 0) {
      return null;
    }
    
    // 获取当前时间
    const now = new Date();
    
    // 计算截止时间
    const expiryTime = new Date(now.getTime());
    expiryTime.setDate(expiryTime.getDate() + days);
    expiryTime.setHours(expiryTime.getHours() + hours);
    
    // 格式化为ISO格式字符串，包含日期和时间
    return expiryTime.toISOString();
  },

  /**
   * 计算截止时间并格式化为预览显示
   */
  calculateExpiryTimePreview: function() {
    const { days, hours } = this.data.duration;
    
    // 如果持续时间为0，则不设置截止时间
    if (days === 0 && hours === 0) {
      return '';
    }
    
    // 获取当前时间
    const now = new Date();
    
    // 计算截止时间
    const expiryTime = new Date(now.getTime());
    expiryTime.setDate(expiryTime.getDate() + days);
    expiryTime.setHours(expiryTime.getHours() + hours);
    
    // 格式化为易读格式
    return formatTime(expiryTime, 'MM月DD日 HH:mm');
  },
  
  /**
   * 更新预计结束时间预览
   */
  updateExpiryTimePreview: function() {
    const previewText = this.calculateExpiryTimePreview();
    this.setData({
      expiryTimePreview: previewText
    });
  },

  /**
   * 切换匿名设置
   */
  toggleAnonymous: function() {
    this.setData({
      isAnonymous: !this.data.isAnonymous
    });
  },

  /**
   * 切换是否要求理由
   */
  toggleRequireReason: function(e) {
    const value = e.currentTarget.dataset.value === 'true';
    this.setData({
      requireReason: value
    });
    console.log('设置投票方式为:', value ? '必填理由' : '选填理由');
  },

  /**
   * 切换可见性类型
   */
  changeVisibilityType: function(e) {
    this.setData({
      'visibility.type': e.detail.value
    });
  },

  /**
   * 选择使用哪些筛选条件
   */
  selectFilterConditions: function(e) {
    const selectedConditions = e.detail.value || [];
    const filterConditions = {
      gender: selectedConditions.includes('gender'),
      age: selectedConditions.includes('age'),
      region: selectedConditions.includes('region'),
      occupation: selectedConditions.includes('occupation')
    };
    
    // 如果某个条件取消选择，清空对应的筛选值
    const visibility = this.data.visibility;
    if (!filterConditions.gender) {
      visibility.filters.gender = [];
    }
    if (!filterConditions.age) {
      visibility.filters.minAge = null;
      visibility.filters.maxAge = null;
    }
    if (!filterConditions.region) {
      visibility.filters.regions = [];
    }
    if (!filterConditions.occupation) {
      visibility.filters.occupations = [];
    }
    
    this.setData({
      filterConditions,
      visibility
    });
  },

  /**
   * 性别筛选 
   */
  filterGender: function(e) {
    this.setData({
      'visibility.filters.gender': e.detail.value
    });
  },

  /**
   * 输入最小年龄
   */
  inputMinAge: function(e) {
    const value = e.detail.value ? parseInt(e.detail.value) : null;
    this.setData({
      'visibility.filters.minAge': value
    });
  },

  /**
   * 输入最大年龄
   */
  inputMaxAge: function(e) {
    const value = e.detail.value ? parseInt(e.detail.value) : null;
    this.setData({
      'visibility.filters.maxAge': value
    });
  },

  /**
   * 选择地区
   */
  selectRegion: function(e) {
    const index = parseInt(e.detail.value);
    const region = this.data.regionList[index];
    
    // 检查是否已经选择了该地区
    const regions = [...this.data.visibility.filters.regions];
    if (!regions.includes(region)) {
      regions.push(region);
      this.setData({
        'visibility.filters.regions': regions
      });
    }
  },

  /**
   * 移除选择的地区
   */
  removeRegion: function(e) {
    const { item } = e.currentTarget.dataset;
    const regions = this.data.visibility.filters.regions.filter(region => region !== item);
    this.setData({
      'visibility.filters.regions': regions
    });
  },

  /**
   * 选择职业
   */
  selectOccupation: function(e) {
    const index = parseInt(e.detail.value);
    const occupation = this.data.occupationList[index];
    
    // 检查是否已经选择了该职业
    const occupations = [...this.data.visibility.filters.occupations];
    if (!occupations.includes(occupation)) {
      occupations.push(occupation);
      this.setData({
        'visibility.filters.occupations': occupations
      });
    }
  },

  /**
   * 移除选择的职业
   */
  removeOccupation: function(e) {
    const { item } = e.currentTarget.dataset;
    const occupations = this.data.visibility.filters.occupations.filter(occupation => occupation !== item);
    this.setData({
      'visibility.filters.occupations': occupations
    });
  },

  /**
   * 表单提交
   */
  formSubmit: function() {
    const { 
      title, 
      scene,
      keyFactors,
      budget,
      tags,
      options, 
      duration,
      isAnonymous,
      requireReason,
      visibility
    } = this.data;
    
    // 表单验证
    if (!title.trim()) {
      wx.showToast({
        title: '请输入问题标题',
        icon: 'none'
      });
      return;
    }
    
    // 验证选项
    const formattedOptions = options.filter(option => option.trim());
    if (formattedOptions.length < 2) {
      wx.showToast({
        title: '至少需要2个有效选项',
        icon: 'none'
      });
      return;
    }
    
    // 验证持续时间
    if (duration.days === 0 && duration.hours === 0) {
      wx.showToast({
        title: '请设置问题持续时间',
        icon: 'none'
      });
      return;
    }
    
    this.setData({ submitting: true });
    
    // 构建问题数据
    const questionData = {
      title: title.trim(),
      scene: scene.trim(),
      keyFactors,
      budget,
      tags,
      options: formattedOptions.map(option => ({ content: option.trim() })),
      isAnonymous,
      requireReason,
      visibility
    };
    
    // 计算并添加截止时间
    const expiryTime = this.calculateExpiryTime();
    if (expiryTime) {
      questionData.expiryTime = expiryTime;
    }
    
    console.log('发送问题数据:', JSON.stringify(questionData));
    
    // 发送请求
    question.createQuestion(questionData)
      .then(res => {
        this.setData({ submitting: false });
        wx.showToast({
          title: '问题发布成功',
          icon: 'success'
        });
        setTimeout(() => {
          wx.redirectTo({
            url: `/pages/question/detail/detail?id=${res.data.id}`
          });
        }, 1500);
      })
      .catch(err => {
        console.error('发布问题失败:', err);
        this.setData({ submitting: false });
        wx.showToast({
          title: err.message || '发布失败，请重试',
          icon: 'none'
        });
      });
  },

  /**
   * 打开模板示例弹窗
   */
  showTemplate: function() {
    this.setData({
      showTemplateModal: true,
      currentTemplate: this.data.templates[0]
    });
  },

  /**
   * 关闭模板示例弹窗
   */
  closeTemplateModal: function() {
    this.setData({
      showTemplateModal: false
    });
  },

  /**
   * 使用当前模板
   */
  useTemplate: function() {
    const { currentTemplate } = this.data;
    if (!currentTemplate) return;

    // 更新标签状态
    const newTagStatus = {};
    this.data.tagOptions.forEach(tag => {
      newTagStatus[tag] = currentTemplate.tags.includes(tag);
    });

    // 初始化推荐理由数组，模板选项不包含AI推荐理由
    const templateOptions = [...currentTemplate.options, '其他'];
    const newRecommendReasons = new Array(templateOptions.length).fill(null);
    const newExpandedStatus = new Array(templateOptions.length).fill(false);

    this.setData({
      title: currentTemplate.title,
      scene: currentTemplate.scene,
      keyFactors: currentTemplate.keyFactors,
      budget: {
        min: currentTemplate.budget.min,
        max: currentTemplate.budget.max,
        currency: currentTemplate.budget.currency
      },
      tags: [...currentTemplate.tags],
      tagStatus: newTagStatus,
      options: templateOptions,
      optionRecommendReasons: newRecommendReasons,
      reasonExpandedStatus: newExpandedStatus,
      showTemplateModal: false
    }, () => {
      // 更新预计结束时间预览
      this.updateExpiryTimePreview();
      wx.showToast({
        title: '已应用模板',
        icon: 'success'
      });
    });
  },

  /**
   * 显示AI推荐弹窗
   */
  showAiRecommend: function() {
    const { title, scene, keyFactors, budget } = this.data;
    
    // 构建问题信息
    const questionInfo = {
      title: title.trim(),
      scene: scene.trim(),
      keyFactors: keyFactors.trim(),
      budget
    };
    
    this.setData({
      showAiRecommendModal: true,
      questionInfo
    });
  },

  /**
   * 关闭AI推荐弹窗
   */
  closeAiRecommend: function() {
    this.setData({
      showAiRecommendModal: false
    });
  },

  /**
   * 切换推荐理由展开状态
   */
  toggleReasonExpanded: function(e) {
    const { index } = e.currentTarget.dataset;
    const { reasonExpandedStatus } = this.data;
    
    reasonExpandedStatus[index] = !reasonExpandedStatus[index];
    this.setData({ reasonExpandedStatus });
  },

  /**
   * 处理AI推荐结果
   * 从AI推荐数据中提取产品名称，生成问题选项，并保存推荐理由
   * @param {Object} e 事件对象
   * @param {Boolean} e.detail.success 推荐是否成功
   * @param {Object} e.detail.data 推荐数据
   */
  onAiRecommend: function(e) {
    const { success, data } = e.detail;
    
    // 验证事件数据的有效性
    if (!success || !data) {
      console.error('AI推荐事件数据格式错误:', e.detail);
      wx.showToast({
        title: '推荐数据格式错误',
        icon: 'none'
      });
      return;
    }
    
    console.log('AI推荐成功，原始数据:', data);
    
    try {
      // 从不同的数据结构中提取推荐产品列表
      let recommendedProducts = [];
      
      if (data.recommendedProducts && Array.isArray(data.recommendedProducts)) {
        recommendedProducts = data.recommendedProducts;
      } else if (data.products && Array.isArray(data.products)) {
        recommendedProducts = data.products;
      } else {
        console.error('推荐结果数据结构异常:', data);
        throw new Error('推荐结果数据格式错误');
      }
      
      // 验证推荐产品数量
      if (recommendedProducts.length === 0) {
        console.warn('AI推荐返回的产品列表为空');
        throw new Error('没有找到合适的推荐产品，请调整筛选条件');
      }
      
      // 限制推荐数量，避免选项过多
      const maxRecommendations = Math.min(recommendedProducts.length, this.data.maxOptions);
      console.log(`将使用${maxRecommendations}个推荐产品（总共${recommendedProducts.length}个）`);
      
      // 处理推荐产品数据
      const newOptions = [];
      const newRecommendReasons = [];
      const newExpandedStatus = [];
      
      recommendedProducts.slice(0, maxRecommendations).forEach((item, index) => {
        // 提取产品名称
        let productName = '';
        if (item.skuName && typeof item.skuName === 'string') {
          productName = item.skuName.trim();
        } else if (item.name && typeof item.name === 'string') {
          productName = item.name.trim();
        } else if (item.content && typeof item.content === 'string') {
          productName = item.content.trim();
        }
        
        // 验证产品名称有效性
        if (!productName) {
          console.warn(`第${index + 1}个推荐产品名称为空:`, item);
          productName = `推荐产品${index + 1}`;
        }
        
        // 构建推荐理由对象
        const reasonData = {
          hasRecommendReason: !!(item.recommendReason || item.highlights),
          recommendReason: item.recommendReason || '',
          highlights: item.highlights || [],
          isAiRecommended: true // 标记这是AI推荐的产品
        };
        
        newOptions.push(productName);
        newRecommendReasons.push(reasonData);
        newExpandedStatus.push(false); // 默认不展开
      });
      
      // 确保选项数量符合最小要求
      while (newOptions.length < this.data.minOptions) {
        newOptions.push('');
        newRecommendReasons.push(null);
        newExpandedStatus.push(false);
      }
      
      console.log('生成的选项列表:', newOptions);
      console.log('生成的推荐理由:', newRecommendReasons);
      
      // 更新页面数据
      this.setData({
        options: newOptions,
        optionRecommendReasons: newRecommendReasons,
        reasonExpandedStatus: newExpandedStatus,
        showAiRecommendModal: false
      });
      
      // 显示成功提示
      wx.showToast({
        title: `已添加${maxRecommendations}个AI推荐选项`,
        icon: 'success',
        duration: 2000
      });
      
    } catch (error) {
      console.error('处理AI推荐结果失败:', error);
      wx.showToast({
        title: error.message || '处理推荐结果失败，请重试',
        icon: 'none',
        duration: 3000
      });
    }
  }
}); 