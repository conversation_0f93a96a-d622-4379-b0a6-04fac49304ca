<!--pages/login/login.wxml-->
<view class="page">
  <view class="container p-30">
    <!-- 顶部 Logo -->
    <view class="logo-display mb-40">
      <image class="logo-image" src="/assets/images/logo.png" mode="aspectFit"></image>
      <view class="logo-title">选选</view>
      <view class="logo-subtitle">购物前,先选选</view>
    </view>
    
    <!-- 登录表单 -->
    <view class="login-form mb-40" wx:if="{{showPhoneLogin}}">
      <!-- 手机号输入 -->
      <view class="form-group">
        <view class="form-label">手机号</view>
        <view class="form-input-wrap">
          <input 
            class="form-input" 
            type="number" 
            maxlength="11" 
            placeholder="请输入手机号" 
            bindinput="onPhoneInput" 
            value="{{phone}}" />
        </view>
      </view>
      
      <!-- 验证码输入 -->
      <view class="form-group">
        <view class="form-label">验证码</view>
        <view class="code-input-group">
          <view class="form-input-wrap flex-1">
            <input 
              class="form-input" 
              type="number" 
              maxlength="6" 
              placeholder="请输入验证码" 
              bindinput="onCodeInput" 
              value="{{verifyCode}}" />
          </view>
          <button 
            class="code-btn {{countdownDisabled ? 'disabled' : ''}}" 
            bindtap="sendVerifyCode" 
            disabled="{{countdownDisabled}}">
            {{countdownText}}
          </button>
        </view>
      </view>
      
      <!-- 登录按钮 -->
      <button 
        class="btn btn-primary btn-full mt-30" 
        bindtap="phoneAuth" 
        disabled="{{!isPhoneValid || !isCodeValid}}">
        登录 / 注册
      </button>
      
      <!-- 说明文字 -->
      <view class="text-center mt-20">
        <text class="text-small">新用户将自动注册账号</text>
      </view>
    </view>
    
    <!-- 分割线 -->
    <view class="divider-text mb-30" wx:if="{{showPhoneLogin}}">
      <view class="divider-text-content">或</view>
    </view>
    
    <!-- 微信登录 -->
    <button class="btn btn-wechat btn-full {{showPhoneLogin ? 'mb-40' : 'btn-wechat-only'}}" bindtap="wxLogin">
      <image class="icon" src="/assets/images/wechat.png" mode="aspectFit"></image>
      微信快捷登录
    </button>
    
    <!-- 底部协议 -->
    <view class="text-center">
      <text class="text-small">登录即表示您已同意</text>
      <text class="text-small text-primary">《用户协议》</text>
      <text class="text-small">和</text>
      <text class="text-small text-primary">《隐私政策》</text>
    </view>
  </view>
</view> 