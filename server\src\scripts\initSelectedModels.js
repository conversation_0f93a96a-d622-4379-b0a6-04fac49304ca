const mongoose = require('mongoose');
const dotenv = require('dotenv');
const path = require('path');
const fs = require('fs');

// 正确加载环境变量
const envPath = path.resolve(__dirname, '../../.env');
dotenv.config({ path: envPath });

// 获取命令行参数（要初始化的模型名称）
const modelsToInit = process.argv.slice(2);

// 连接数据库
const connectDB = async () => {
  try {
    console.log('尝试连接到MongoDB...');
    const conn = await mongoose.connect(process.env.MONGODB_URI);
    console.log(`MongoDB连接成功: ${conn.connection.host}`);
    
    // 创建指定的集合
    await setupSelectedCollections();
    
    console.log('指定的集合创建成功!');
    process.exit(0);
  } catch (error) {
    console.error(`MongoDB连接错误: ${error.message}`);
    process.exit(1);
  }
};

// 设置指定的集合
const setupSelectedCollections = async () => {
  console.log('开始创建指定的集合...');
  
  // 如果没有指定模型，则退出
  if (modelsToInit.length === 0) {
    console.log('没有指定要初始化的模型。请提供模型名称作为命令行参数。');
    process.exit(0);
  }
  
  // 处理每个指定的模型
  for (const modelName of modelsToInit) {
    try {
      console.log(`正在初始化模型: ${modelName}`);
      
      // 动态导入模型
      const Model = require(`../models/${modelName}`);
      
      // 只创建集合，不添加测试数据
      await ensureCollectionExists(Model);
      
      console.log(`${modelName}集合创建成功`);
    } catch (error) {
      console.error(`初始化模型${modelName}时出错:`, error);
    }
  }
};

// 确保集合存在
const ensureCollectionExists = async (Model) => {
  // 这个操作会确保集合在数据库中被创建
  // 在Mongoose中，只需要引用模型就会自动创建集合（如果不存在）
  const modelName = Model.modelName;
  const collectionName = Model.collection.name;
  
  console.log(`确保${modelName}集合(${collectionName})存在...`);
  
  // 可以通过执行一个简单的查询来确保集合被创建
  await Model.findOne({}).exec();
  
  console.log(`${modelName}集合已创建`);
};

// 执行连接
connectDB();
