/* pages/user/feedback/list/list.wxss */
.page {
  background-color: #f5f5f5;
  min-height: 100vh;
  width: 100%;
}

.feedback-container {
  padding: 12rpx;
  padding-bottom: 60rpx;
  width: 100%;
  box-sizing: border-box;
}

/* 新增反馈按钮区域 */
.add-feedback-section {
  margin-bottom: 24rpx;
}

.add-feedback-btn {
  background: linear-gradient(135deg, #3B7ADB 0%, #4A90E2 100%);
  color: #fff;
  border-radius: 16rpx;
  height: 96rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  font-weight: 600;
  border: none;
  box-shadow: 0 4rpx 20rpx rgba(59, 122, 219, 0.3);
  transition: all 0.3s ease;
}

.add-feedback-btn:active {
  transform: translateY(2rpx);
  box-shadow: 0 2rpx 10rpx rgba(59, 122, 219, 0.4);
}

.add-btn-icon {
  font-size: 36rpx;
  margin-right: 16rpx;
}

.add-btn-text {
  font-size: 32rpx;
  font-weight: 600;
}

/* 反馈列表 */
.feedback-list {
  margin-bottom: 40rpx;
}

/* 加载更多 */
.load-more, .no-more, .loading-state {
  text-align: center;
  padding: 40rpx 0;
}

.loading-text, .tip-text, .no-more-text {
  font-size: 28rpx;
  color: #999;
}

/* 空状态 */
.empty-state {
  text-align: center;
  padding: 100rpx 40rpx;
}

.empty-image {
  width: 300rpx;
  height: 300rpx;
}

.empty-text {
  font-size: 32rpx;
  color: #666;
  margin-bottom: 16rpx;
  display: block;
  font-weight: 500;
}

.empty-desc {
  font-size: 28rpx;
  color: #999;
  display: block;
} 