/**
 * 选选小程序 - 布局样式文件
 * Layout Styles for XuanXuan Mini Program
 * 依赖：variables.wxss, base.wxss
 */

/* ==================== 网格布局系统 Grid Layout System ==================== */

/* 容器 */
.container-fluid {
  width: 100%;
  padding: 0 20rpx;
  margin: 0 auto;
  box-sizing: border-box;
}

.container-sm {
  max-width: 600rpx;
  padding: 0 30rpx;
  margin: 0 auto;
  box-sizing: border-box;
}

.container-md {
  max-width: 900rpx;
  padding: 0 40rpx;
  margin: 0 auto;
  box-sizing: border-box;
}

.container-lg {
  max-width: 1200rpx;
  padding: 0 50rpx;
  margin: 0 auto;
  box-sizing: border-box;
}

/* 行 */
.row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -15rpx;
}

/* 列 */
.col {
  flex: 1;
  padding: 0 15rpx;
  box-sizing: border-box;
}

.col-1 { flex: 0 0 8.333333%; max-width: 8.333333%; }
.col-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
.col-3 { flex: 0 0 25%; max-width: 25%; }
.col-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
.col-5 { flex: 0 0 41.666667%; max-width: 41.666667%; }
.col-6 { flex: 0 0 50%; max-width: 50%; }
.col-7 { flex: 0 0 58.333333%; max-width: 58.333333%; }
.col-8 { flex: 0 0 66.666667%; max-width: 66.666667%; }
.col-9 { flex: 0 0 75%; max-width: 75%; }
.col-10 { flex: 0 0 83.333333%; max-width: 83.333333%; }
.col-11 { flex: 0 0 91.666667%; max-width: 91.666667%; }
.col-12 { flex: 0 0 100%; max-width: 100%; }

/* ==================== 响应式断点 Responsive Breakpoints ==================== */

/* 小屏幕 (手机) */
@media screen and (max-width: 375px) {
  .container {
    padding: 0 20rpx;
  }
  
  .content {
    padding: 20rpx;
  }
  
  /* 小屏幕隐藏 */
  .hidden-sm {
    display: none !important;
  }
  
  /* 小屏幕显示 */
  .visible-sm {
    display: block !important;
  }
  
  /* 小屏幕列布局调整 */
  .col-sm-12 { flex: 0 0 100%; max-width: 100%; }
  .col-sm-6 { flex: 0 0 50%; max-width: 50%; }
  .col-sm-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
  .col-sm-3 { flex: 0 0 25%; max-width: 25%; }
}

/* 中等屏幕 (平板) */
@media screen and (min-width: 376px) and (max-width: 768px) {
  .container {
    padding: 0 30rpx;
  }
  
  /* 中屏幕隐藏 */
  .hidden-md {
    display: none !important;
  }
  
  /* 中屏幕显示 */
  .visible-md {
    display: block !important;
  }
  
  /* 中屏幕列布局调整 */
  .col-md-12 { flex: 0 0 100%; max-width: 100%; }
  .col-md-6 { flex: 0 0 50%; max-width: 50%; }
  .col-md-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
  .col-md-3 { flex: 0 0 25%; max-width: 25%; }
}

/* 大屏幕 (桌面) */
@media screen and (min-width: 769px) {
  .container {
    max-width: 1200rpx;
    padding: 0 40rpx;
  }
  
  /* 大屏幕隐藏 */
  .hidden-lg {
    display: none !important;
  }
  
  /* 大屏幕显示 */
  .visible-lg {
    display: block !important;
  }
  
  /* 大屏幕列布局调整 */
  .col-lg-12 { flex: 0 0 100%; max-width: 100%; }
  .col-lg-6 { flex: 0 0 50%; max-width: 50%; }
  .col-lg-4 { flex: 0 0 33.333333%; max-width: 33.333333%; }
  .col-lg-3 { flex: 0 0 25%; max-width: 25%; }
  .col-lg-2 { flex: 0 0 16.666667%; max-width: 16.666667%; }
}

/* ==================== 页面布局组件 Page Layout Components ==================== */

/* 页面头部 */
.page-header {
  position: sticky;
  top: 0;
  z-index: 100;
  background-color: #ffffff;
  border-bottom: 1rpx solid #f0f0f0;
  padding: 20rpx 0;
}

/* 页面主体 */
.page-body {
  flex: 1;
  min-height: 0;
  overflow-y: auto;
}

/* 页面底部 */
.page-footer {
  background-color: #ffffff;
  border-top: 1rpx solid #f0f0f0;
  padding: 20rpx 0;
}

/* ==================== 导航布局 Navigation Layout ==================== */

/* 顶部导航 */
.navbar {
  display: flex;
  align-items: center;
  justify-content: space-between;
  height: 88rpx;
  padding: 0 30rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #f0f0f0;
}

.navbar-brand {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
}

.navbar-menu {
  display: flex;
  align-items: center;
  gap: 30rpx;
}

/* 标签栏导航 */
.tabbar {
  display: flex;
  height: 100rpx;
  background-color: #ffffff;
  border-top: 1rpx solid #f0f0f0;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.tabbar-item {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 10rpx;
  font-size: 20rpx;
  color: #999999;
  transition: color 0.3s ease;
}

.tabbar-item.active {
  color: #3B7ADB;
}

.tabbar-icon {
  width: 40rpx;
  height: 40rpx;
  margin-bottom: 4rpx;
}

/* ==================== 侧边栏布局 Sidebar Layout ==================== */

.layout-with-sidebar {
  display: flex;
  min-height: 100vh;
}

.sidebar {
  width: 300rpx;
  background-color: #ffffff;
  border-right: 1rpx solid #f0f0f0;
  overflow-y: auto;
}

.main-content {
  flex: 1;
  min-width: 0;
  overflow-y: auto;
}

/* ==================== 卡片网格布局 Card Grid Layout ==================== */

.card-grid {
  display: flex;
  flex-wrap: wrap;
  padding: 20rpx;
  margin: -10rpx;
}

.card-grid .card-item {
  flex: 1 1 300rpx;
  margin: 10rpx;
  min-width: 300rpx;
}

.card-grid-2 {
  display: flex;
  flex-wrap: wrap;
  padding: 20rpx;
  margin: -10rpx;
}

.card-grid-2 .card-item {
  flex: 1 1 calc(50% - 20rpx);
  margin: 10rpx;
  max-width: calc(50% - 20rpx);
}

.card-grid-3 {
  display: flex;
  flex-wrap: wrap;
  padding: 20rpx;
  margin: -10rpx;
}

.card-grid-3 .card-item {
  flex: 1 1 calc(33.333% - 20rpx);
  margin: 10rpx;
  max-width: calc(33.333% - 20rpx);
}

/* 瀑布流布局 - 使用flex替代 */
.masonry {
  display: flex;
  flex-wrap: wrap;
  padding: 20rpx;
}

.masonry-item {
  width: calc(50% - 10rpx);
  margin-right: 10rpx;
  margin-bottom: 20rpx;
}

.masonry-item:nth-child(2n) {
  margin-right: 0;
}

/* ==================== 固定定位布局 Fixed Position Layout ==================== */

/* 浮动操作按钮 */
.fab {
  position: fixed;
  bottom: 120rpx;
  right: 30rpx;
  width: 112rpx;
  height: 112rpx;
  border-radius: 50%;
  background-color: #3B7ADB;
  color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 48rpx;
  box-shadow: 0 4rpx 20rpx rgba(59, 122, 219, 0.3);
  z-index: 999;
}

/* 回到顶部按钮 */
.back-to-top {
  position: fixed;
  bottom: 200rpx;
  right: 30rpx;
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  background-color: rgba(0, 0, 0, 0.5);
  color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 32rpx;
  z-index: 998;
  opacity: 0;
  transform: translateY(20rpx);
  transition: all 0.3s ease;
}

.back-to-top.show {
  opacity: 1;
  transform: translateY(0);
}

/* ==================== 空间分布布局 Space Distribution Layout ==================== */

/* 平均分布 */
.space-between {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.space-around {
  display: flex;
  justify-content: space-around;
  align-items: center;
}

.space-evenly {
  display: flex;
  justify-content: space-evenly;
  align-items: center;
}

/* ==================== 垂直居中布局 Vertical Center Layout ==================== */

.v-center {
  display: flex;
  align-items: center;
  min-height: 100vh;
}

.v-center-content {
  width: 100%;
}

/* ==================== 粘性布局 Sticky Layout ==================== */

.sticky-top {
  position: sticky;
  top: 0;
  z-index: 100;
}

.sticky-bottom {
  position: sticky;
  bottom: 0;
  z-index: 100;
}

/* ==================== 堆叠布局 Stack Layout ==================== */

.stack {
  display: flex;
  flex-direction: column;
}

.stack .stack-item:not(:first-child) {
  margin-top: 20rpx;
}

.stack-sm .stack-item:not(:first-child) {
  margin-top: 10rpx;
}

.stack-lg .stack-item:not(:first-child) {
  margin-top: 40rpx;
}

/* 水平堆叠 */
.hstack {
  display: flex;
  align-items: center;
}

.hstack .hstack-item:not(:first-child) {
  margin-left: 20rpx;
}

.hstack-sm .hstack-item:not(:first-child) {
  margin-left: 10rpx;
}

.hstack-lg .hstack-item:not(:first-child) {
  margin-left: 40rpx;
}
