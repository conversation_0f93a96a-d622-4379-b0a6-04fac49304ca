#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中关村在线手机参数对比页面爬虫
专门爬取参数对比表格中所有型号手机的详细参数
URL示例: https://detail.zol.com.cn/series/57/544/param_10842944_0_1.html
包含反爬虫机制，按照apple_phones_detailed_specs_1.json格式保存数据
"""

import requests
from bs4 import BeautifulSoup
import json
import time
import re
import chardet
from urllib.parse import urljoin, urlparse
import random
from fake_useragent import UserAgent
import pandas as pd

class ZOLPhoneParamsCrawler:
    def __init__(self):
        # 使用随机User-Agent
        try:
            ua = UserAgent()
            user_agent = ua.random
        except:
            # 如果fake_useragent失败，使用预设的User-Agent列表
            user_agents = [
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0',
                'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            ]
            user_agent = random.choice(user_agents)
        
        self.headers = {
            'User-Agent': user_agent,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-US;q=0.7',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Cache-Control': 'no-cache',
            'DNT': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1',
            'Referer': 'https://detail.zol.com.cn/',
        }
        
        self.session = requests.Session()
        self.session.headers.update(self.headers)
        
        # 反爬虫设置
        self.min_delay = 1.5  # 最小延时（秒）
        self.max_delay = 4.0  # 最大延时（秒）
        self.request_count = 0
        self.max_requests_per_session = 15  # 每个session最大请求数
        
        # 需要过滤的保修信息相关参数
        self.warranty_keywords = [
            '保修政策', '质保时间', '质保备注', '客服电话', '电话备注', 
            '详细内容', '保修', '质保', '维修', '售后', '客服'
        ]
        
    def random_delay(self):
        """随机延时"""
        delay = random.uniform(self.min_delay, self.max_delay)
        print(f"等待 {delay:.1f} 秒...")
        time.sleep(delay)
    
    def rotate_user_agent(self):
        """轮换User-Agent"""
        user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        ]
        new_ua = random.choice(user_agents)
        self.session.headers.update({'User-Agent': new_ua})
        print(f"轮换User-Agent: {new_ua[:50]}...")
    
    def create_new_session(self):
        """创建新的session"""
        print("创建新的session...")
        self.session.close()
        self.session = requests.Session()
        self.rotate_user_agent()
        self.session.headers.update(self.headers)
        self.request_count = 0
        time.sleep(random.uniform(3, 6))  # 更长的延时
        
    def safe_request(self, url, **kwargs):
        """安全的请求方法，包含反爬虫机制"""
        self.request_count += 1
        
        # 每隔一定请求数就轮换User-Agent
        if self.request_count % 3 == 0:
            self.rotate_user_agent()
        
        # 每隔一定请求数就创建新session
        if self.request_count >= self.max_requests_per_session:
            self.create_new_session()
        
        try:
            # 添加随机延时
            self.random_delay()
            
            # 发送请求
            response = self.session.get(url, timeout=20, **kwargs)
            
            # 检查是否被反爬虫拦截
            if response.status_code == 403:
                print("检测到403错误，可能被反爬虫拦截，等待更长时间...")
                time.sleep(random.uniform(10, 20))
                self.create_new_session()
                response = self.session.get(url, timeout=20, **kwargs)
            elif response.status_code == 429:
                print("检测到429错误（请求过于频繁），等待...")
                time.sleep(random.uniform(15, 30))
                self.create_new_session()
                response = self.session.get(url, timeout=20, **kwargs)
            
            return response
            
        except requests.exceptions.RequestException as e:
            print(f"请求失败: {e}")
            return None
        
    def detect_and_decode(self, content):
        """检测并正确解码内容"""
        try:
            # 尝试检测编码
            detected = chardet.detect(content)
            encoding = detected.get('encoding', 'utf-8')
            
            # 尝试多种编码方式
            encodings_to_try = [encoding, 'utf-8', 'gbk', 'gb2312', 'gb18030']
            
            for enc in encodings_to_try:
                try:
                    if enc:
                        decoded = content.decode(enc)
                        return decoded
                except (UnicodeDecodeError, LookupError):
                    continue
            
            # 如果都失败了，使用错误忽略模式
            return content.decode('utf-8', errors='ignore')
            
        except Exception as e:
            print(f"编码检测失败: {e}")
            return content.decode('utf-8', errors='ignore')
    
    def is_warranty_param(self, param_name):
        """判断是否为保修信息相关参数"""
        for keyword in self.warranty_keywords:
            if keyword in param_name:
                return True
        return False
    
    def format_phone_data(self, phone_name, params_dict, source_url):
        """按照参考格式整理手机数据"""
        formatted_data = {
            "产品名称": phone_name,
            "详情页URL": "",  # 从源页面提取或设置为空
            "参数页URL": "",  # 从源页面提取或设置为空
        }
        
        # 过滤掉保修信息相关参数，并添加到formatted_data中
        filtered_params = {}
        for param_name, param_value in params_dict.items():
            if not self.is_warranty_param(param_name):
                filtered_params[param_name] = param_value
                formatted_data[param_name] = param_value
        
        # 添加元数据
        formatted_data["爬取时间"] = time.strftime('%Y-%m-%d %H:%M:%S')
        formatted_data["参数总数"] = len(filtered_params)
        
        return formatted_data
    
    def parse_param_table(self, soup):
        """解析参数对比表格"""
        print("正在解析参数对比表格...")
        
        # 查找参数表格的主要容器
        table_containers = soup.find_all(['div', 'table'], 
            class_=re.compile(r'param|config|compare|detail'))
        
        # 如果没有找到特定容器，查找所有table元素
        if not table_containers:
            table_containers = soup.find_all('table')
        
        phones_data = []
        
        # 首先尝试解析表格结构
        for container in table_containers:
            try:
                # 查找表格行
                rows = container.find_all('tr')
                if len(rows) < 2:  # 至少需要表头和一行数据
                    continue
                
                # 解析表头（型号信息）
                header_row = rows[0]
                headers = []
                header_cells = header_row.find_all(['th', 'td'])
                
                for cell in header_cells:
                    cell_text = cell.get_text(strip=True)
                    headers.append(cell_text)
                
                # 检查是否包含型号信息
                if len(headers) < 2:
                    continue
                
                print(f"找到表头: {headers}")
                
                # 初始化每个型号的数据字典
                phone_models = []
                for i, header in enumerate(headers[1:], 1):  # 跳过第一列（参数名称）
                    if header:  # 确保不是空字符串
                        phone_model = {
                            '型号': header,
                            '索引位置': i,
                            '参数': {}
                        }
                        phone_models.append(phone_model)
                
                print(f"识别到 {len(phone_models)} 个型号")
                
                # 解析参数行
                param_count = 0
                for row in rows[1:]:  # 跳过表头
                    cells = row.find_all(['td', 'th'])
                    if len(cells) < 2:
                        continue
                    
                    # 第一列是参数名称
                    param_name = cells[0].get_text(strip=True)
                    if not param_name or param_name in ['型号', '图片', '价格/商家']:
                        continue
                    
                    # 跳过保修信息相关参数
                    if self.is_warranty_param(param_name):
                        print(f"跳过保修信息参数: {param_name}")
                        continue
                    
                    # 获取每个型号的参数值
                    for phone_model in phone_models:
                        cell_index = phone_model['索引位置']
                        if cell_index < len(cells):
                            param_value = cells[cell_index].get_text(strip=True)
                            if param_value:
                                phone_model['参数'][param_name] = param_value
                                param_count += 1
                
                print(f"成功解析 {param_count} 个参数项（已过滤保修信息）")
                
                # 如果找到了有效数据，转换为目标格式
                if phone_models and param_count > 10:
                    for phone_model in phone_models:
                        formatted_data = self.format_phone_data(
                            phone_model['型号'], 
                            phone_model['参数'],
                            ""
                        )
                        phones_data.append(formatted_data)
                    break
                    
            except Exception as e:
                print(f"解析表格容器失败: {e}")
                continue
        
        # 如果表格解析失败，尝试其他方式
        if not phones_data or len(phones_data) == 0:
            print("表格解析失败，尝试其他解析方式...")
            phones_data = self.parse_alternative_structure(soup)
        
        return phones_data
    
    def parse_alternative_structure(self, soup):
        """备用解析方式 - 针对非标准表格结构"""
        print("使用备用解析方式...")
        
        phones_data = []
        
        # 查找所有可能包含型号的元素
        model_elements = soup.find_all(text=re.compile(r'iPhone|华为|小米|OPPO|vivo|三星|苹果'))
        model_names = []
        
        for element in model_elements:
            text = element.strip()
            if text and len(text) < 100:  # 避免长文本
                # 检查是否是型号名称
                if any(keyword in text for keyword in ['iPhone', '华为', '小米', 'OPPO', 'vivo', '三星']):
                    if text not in model_names:
                        model_names.append(text)
        
        print(f"找到可能的型号: {model_names}")
        
        # 如果找到型号，尝试提取参数
        if model_names:
            # 尝试查找参数信息
            param_patterns = [
                r'屏幕尺寸[：:]?\s*([^，,\n]+)',
                r'CPU[：:]?\s*([^，,\n]+)',
                r'内存[：:]?\s*([^，,\n]+)',
                r'存储[：:]?\s*([^，,\n]+)',
                r'摄像头[：:]?\s*([^，,\n]+)',
                r'电池[：:]?\s*([^，,\n]+)',
                r'重量[：:]?\s*([^，,\n]+)',
            ]
            
            page_text = soup.get_text()
            
            for model in model_names[:5]:  # 限制最多5个型号
                phone_params = {}
                
                for pattern in param_patterns:
                    matches = re.findall(pattern, page_text, re.IGNORECASE)
                    if matches:
                        param_name = pattern.split('[')[0]
                        if not self.is_warranty_param(param_name) and matches:
                            phone_params[param_name] = matches[0].strip()
                
                if phone_params:
                    formatted_data = self.format_phone_data(model, phone_params, "")
                    phones_data.append(formatted_data)
        
        return phones_data
    
    def crawl_phone_params(self, url):
        """爬取手机参数对比页面"""
        print(f"开始爬取手机参数页面: {url}")
        print("已启用反爬虫机制：随机延时、User-Agent轮换、Session轮换")
        print("已启用保修信息过滤：自动跳过保修相关参数")
        
        # 发送请求
        response = self.safe_request(url)
        if not response:
            print("无法访问目标页面")
            return []
        
        if response.status_code != 200:
            print(f"请求失败，状态码: {response.status_code}")
            return []
        
        # 解码内容
        html_content = self.detect_and_decode(response.content)
        soup = BeautifulSoup(html_content, 'html.parser')
        
        # 解析参数表格
        phones_data = self.parse_param_table(soup)
        
        return phones_data
    
    def save_to_json(self, data, filename):
        """保存数据到JSON文件"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            print(f"数据已保存到JSON文件: {filename}")
            return True
        except Exception as e:
            print(f"保存JSON文件失败: {e}")
            return False
    
    def save_to_excel(self, data, filename):
        """保存数据到Excel文件"""
        try:
            if not data:
                print("没有数据可保存")
                return False
            
            # 准备Excel数据
            excel_data = []
            
            # 获取所有参数名称
            all_params = set()
            for phone in data:
                # 排除元数据字段
                for key in phone.keys():
                    if key not in ['产品名称', '详情页URL', '参数页URL', '爬取时间', '参数总数']:
                        all_params.add(key)
            
            all_params = sorted(list(all_params))
            
            # 构建数据行
            for phone in data:
                row = {'产品名称': phone.get('产品名称', '')}
                for param in all_params:
                    row[param] = phone.get(param, '')
                row['爬取时间'] = phone.get('爬取时间', '')
                row['参数总数'] = phone.get('参数总数', 0)
                excel_data.append(row)
            
            # 创建DataFrame并保存
            df = pd.DataFrame(excel_data)
            df.to_excel(filename, index=False, engine='openpyxl')
            print(f"数据已保存到Excel文件: {filename}")
            return True
            
        except Exception as e:
            print(f"保存Excel文件失败: {e}")
            print("请确保已安装 pandas 和 openpyxl: pip install pandas openpyxl")
            return False
    
    def print_summary(self, phones_data):
        """打印爬取结果摘要"""
        if not phones_data:
            print("没有获取到任何手机参数")
            return
        
        print(f"\n=== 手机参数爬取摘要 ===")
        print(f"成功爬取的手机型号数量: {len(phones_data)}")
        
        print(f"\n手机型号列表:")
        for i, phone in enumerate(phones_data):
            name = phone.get('产品名称', f'手机{i+1}')
            param_count = phone.get('参数总数', 0)
            print(f"  {i+1}. {name} ({param_count}个参数，已过滤保修信息)")
        
        # 显示第一个手机的部分参数作为示例
        if phones_data:
            first_phone = phones_data[0]
            print(f"\n=== 示例参数 (第一个手机: {first_phone.get('产品名称', 'Unknown')}) ===")
            shown_count = 0
            for key, value in first_phone.items():
                if key not in ['产品名称', '详情页URL', '参数页URL', '爬取时间', '参数总数'] and shown_count < 10:
                    print(f"  {key}: {value}")
                    shown_count += 1
            if first_phone.get('参数总数', 0) > 10:
                print(f"  ... 还有 {first_phone.get('参数总数', 0) - 10} 个参数")

def main():
    """主函数"""
    crawler = ZOLPhoneParamsCrawler()
    
    print("中关村在线手机参数对比页面爬虫 v2.0")
    print("专门爬取参数对比表格中所有型号的详细参数")
    print("✅ 按照 apple_phones_detailed_specs_1.json 格式保存数据")
    print("✅ 自动过滤保修信息相关参数")
    print("=" * 60)
    
    # 目标URL - 您提供的苹果iPhone 16系列参数对比页面
    target_url = "https://detail.zol.com.cn/series/57/544/param_10842944_0_1.html"
    
    print(f"目标页面: {target_url}")
    
    # 开始爬取
    phones_data = crawler.crawl_phone_params(target_url)
    
    if phones_data:
        # 显示摘要
        crawler.print_summary(phones_data)
        
        # 保存数据到JSON - 按照参考格式
        json_filename = 'zol_iphone_params_comparison.json'
        crawler.save_to_json(phones_data, json_filename)
        
        # 尝试保存到Excel
        excel_filename = 'zol_iphone_params_comparison.xlsx'
        crawler.save_to_excel(phones_data, excel_filename)
        
        print(f"\n✅ 爬取完成！")
        print(f"共获取 {len(phones_data)} 个手机型号的参数数据")
        print(f"JSON文件: {json_filename} (按照参考格式保存)")
        print(f"Excel文件: {excel_filename}")
        print(f"📝 说明: 已自动过滤掉保修政策、质保时间、客服电话等保修信息相关参数")
        
    else:
        print("\n❌ 未能获取到任何手机参数")
        print("可能的原因:")
        print("1. 网络连接问题")
        print("2. 网页结构发生变化")
        print("3. 被网站反爬虫机制阻止")
        print("4. 目标页面结构与预期不符")

if __name__ == "__main__":
    main()