const express = require('express');
const router = express.Router();
const notificationController = require('../controllers/notificationController');
const { protect } = require('../middlewares/auth');

/**
 * 通知路由
 * 定义通知相关的API路由
 */

// 所有通知路由都需要认证
router.use(protect);

// 获取用户通知列表
router.get('/', notificationController.getUserNotifications);

// 获取未读通知数量
router.get('/unread-count', notificationController.getUnreadCount);

// 标记所有通知为已读
router.patch('/mark-all-read', notificationController.markAllAsRead);

// 标记单个通知为已读
router.patch('/:notificationId/mark-read', notificationController.markAsRead);

module.exports = router; 