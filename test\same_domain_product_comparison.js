#!/usr/bin/env node
/**
 * 同领域产品对比工具 - 基于AI智能分析
 * 专注于同类型产品的详细参数对比和分析
 * 完全依靠DeepSeek AI的知识能力进行产品分析和对比
 * 作者: AI助手
 * 创建时间: 2025-07-03
 */

const axios = require('axios');
const path = require('path');
const fs = require('fs');

// DeepSeek API配置
const DEEPSEEK_API_KEY = process.env.DEEPSEEK_API_KEY || '***********************************';
const DEEPSEEK_API_BASE = process.env.DEEPSEEK_API_BASE || 'https://api.deepseek.com';
const DEEPSEEK_MODEL = process.env.DEEPSEEK_MODEL || 'deepseek-chat';

// 默认配置
const DEFAULT_CONFIG = {
  temperature: 0.2, // 降低temperature以获得更准确的技术参数
  maxTokens: 4000,
  timeout: 150000
};

/**
 * 调用DeepSeek API
 * @param {String} userPrompt 用户提示
 * @param {String} systemPrompt 系统提示
 * @param {Object} config 配置选项
 * @returns {Promise<String>} API响应内容
 */
async function callDeepSeekAPI(userPrompt, systemPrompt = null, config = {}) {
  try {
    const aiConfig = { ...DEFAULT_CONFIG, ...config };
    
    const baseEndpoint = DEEPSEEK_API_BASE.endsWith('/') 
      ? DEEPSEEK_API_BASE.slice(0, -1) 
      : DEEPSEEK_API_BASE;
    
    const url = `${baseEndpoint}/v1/chat/completions`;
    
    console.log(`🤖 正在调用DeepSeek AI进行产品参数分析...`);
    
    const messages = [];
    if (systemPrompt) {
      messages.push({ role: "system", content: systemPrompt });
    }
    messages.push({ role: "user", content: userPrompt });
    
    const response = await axios({
      url: url,
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${DEEPSEEK_API_KEY}`,
        'Content-Type': 'application/json',
      },
      data: {
        model: DEEPSEEK_MODEL,
        messages: messages,
        temperature: aiConfig.temperature,
        max_tokens: aiConfig.maxTokens,
        stream: false
      },
      timeout: aiConfig.timeout,
    });
    
    if (response.status === 200) {
      console.log(`✅ DeepSeek AI分析完成`);
      return response.data.choices[0].message.content;
    } else {
      throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
    }
  } catch (error) {
    console.error('❌ DeepSeek API调用失败:', error.message);
    throw new Error(`DeepSeek AI分析失败: ${error.message}`);
  }
}

/**
 * 获取产品详细参数规格
 * @param {Array<String>} productNames 产品名称列表
 * @returns {Promise<Object>} 产品详细参数信息
 */
async function getProductDetailedSpecs(productNames) {
  const systemPrompt = `你是一个专业的产品技术参数专家，具有丰富的产品知识。

任务：为给定的产品列表提供详细、准确的技术参数规格。

要求：
1. 首先识别产品类型（确保是同一类型的产品）
2. 为每个产品提供完整的技术参数，包括但不限于：
   - 基本信息（品牌、型号、发布时间、官方价格）
   - 核心技术参数（根据产品类型提供详细规格）
   - 设计参数（尺寸、重量、颜色等）
   - 性能参数（处理器、内存、存储等）
   - 其他重要参数

3. 请使用JSON格式返回结果，结构如下：
{
  "productCategory": "产品类别",
  "products": [
    {
      "name": "产品名称",
      "basicInfo": {
        "brand": "品牌",
        "model": "具体型号",
        "releaseDate": "发布时间",
        "officialPrice": "官方价格",
        "availability": "上市状态"
      },
      "detailedSpecs": {
        // 根据产品类型的详细技术参数
        // 例如手机：处理器、屏幕、摄像头、电池、存储、网络等
        // 例如笔记本：CPU、GPU、内存、存储、屏幕、接口、电池等
      }
    }
  ]
}

注意：
- 请基于最新的产品信息提供准确参数
- 参数要详细具体，包含数值和单位
- 如果某些参数不确定，请标注"待确认"
- 价格提供官方指导价或主流渠道价格
- 确保所有产品属于同一类别`;

  const userPrompt = `请提供以下产品的详细技术参数规格：

产品列表：
${productNames.map((name, index) => `${index + 1}. ${name}`).join('\n')}

请返回JSON格式的详细参数信息。`;

  const response = await callDeepSeekAPI(userPrompt, systemPrompt);
  
  try {
    // 尝试解析JSON响应
    const jsonMatch = response.match(/```json\s*([\s\S]*?)\s*```/) || response.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      const jsonStr = jsonMatch[1] || jsonMatch[0];
      return JSON.parse(jsonStr);
    } else {
      throw new Error('无法解析产品参数JSON数据');
    }
  } catch (parseError) {
    console.warn('JSON解析失败，尝试从原始响应中提取信息');
    return {
      productCategory: "未知",
      products: productNames.map(name => ({ name, error: "参数解析失败" })),
      rawResponse: response
    };
  }
}

/**
 * 对每个参数进行AI分析
 * @param {Object} productSpecs 产品详细参数
 * @returns {Promise<Object>} 参数分析结果
 */
async function analyzeParametersByAI(productSpecs) {
  const systemPrompt = `你是一个资深的产品评测和技术分析专家。

任务：对产品的各项技术参数进行深入分析和对比。

要求：
1. 为每个主要技术参数提供专业分析，包括：
   - 参数说明：该参数的作用和重要性
   - 对比分析：各产品在该参数上的表现差异
   - 性能评价：从技术角度评价各产品的优劣
   - 实际影响：该参数对用户体验的具体影响

2. 分析要客观、专业，基于技术事实
3. 使用通俗易懂的语言解释专业术语
4. 突出各产品的优势和不足

返回JSON格式：
{
  "categoryAnalysis": "产品类别整体分析",
  "parameterAnalysis": {
    "参数名称": {
      "description": "参数说明",
      "comparison": "对比分析",
      "performance": "性能评价",
      "userImpact": "用户影响",
      "productRanking": ["排名从高到低的产品名称"]
    }
  },
  "overallRecommendation": "综合推荐建议"
}`;

  const userPrompt = `请基于以下产品参数进行详细的AI分析：

${JSON.stringify(productSpecs, null, 2)}

请为每个重要参数提供深入的分析和对比。`;

  const response = await callDeepSeekAPI(userPrompt, systemPrompt, {
    temperature: 0.3,
    maxTokens: 4000
  });

  try {
    const jsonMatch = response.match(/```json\s*([\s\S]*?)\s*```/) || response.match(/\{[\s\S]*\}/);
    if (jsonMatch) {
      const jsonStr = jsonMatch[1] || jsonMatch[0];
      return JSON.parse(jsonStr);
    } else {
      throw new Error('无法解析参数分析JSON数据');
    }
  } catch (parseError) {
    console.warn('参数分析JSON解析失败');
    return {
      categoryAnalysis: "分析数据解析失败",
      parameterAnalysis: {},
      overallRecommendation: response.substring(0, 500) + "...",
      rawResponse: response
    };
  }
}

/**
 * 主要的产品对比函数
 * @param {Array<String>} productNames 产品名称列表
 * @param {Object} options 对比选项
 * @returns {Promise<Object>} 产品对比结果
 */
async function compareProductsSameDomain(productNames, options = {}) {
  try {
    console.log(`\n🔍 开始同领域产品参数对比分析...`);
    console.log(`待对比产品: ${productNames.join(', ')}`);
    
    // 验证输入
    if (!Array.isArray(productNames) || productNames.length < 2) {
      throw new Error('至少需要提供2个产品名称进行对比');
    }
    
    if (productNames.length > 5) {
      throw new Error('最多支持5个产品同时对比');
    }

    // 清理产品名称
    const cleanedNames = productNames.map(name => name.trim()).filter(name => name.length > 0);
    
    // 第一步：获取产品详细参数
    console.log(`\n📊 第一步：获取产品详细技术参数...`);
    const productSpecs = await getProductDetailedSpecs(cleanedNames);
    
    console.log(`✅ 产品类别: ${productSpecs.productCategory}`);
    console.log(`✅ 获取到 ${productSpecs.products?.length || 0} 个产品的详细参数`);

    // 第二步：AI参数分析
    console.log(`\n🧠 第二步：AI参数深度分析...`);
    const parameterAnalysis = await analyzeParametersByAI(productSpecs);
    
    console.log(`✅ 完成参数分析，分析了 ${Object.keys(parameterAnalysis.parameterAnalysis || {}).length} 个主要参数`);

    // 构建返回结果
    const result = {
      success: true,
      requestedProducts: cleanedNames,
      productCategory: productSpecs.productCategory,
      
      // 产品详细参数
      productSpecs: productSpecs.products || [],
      
      // AI参数分析
      parameterAnalysis: parameterAnalysis.parameterAnalysis || {},
      categoryAnalysis: parameterAnalysis.categoryAnalysis || "",
      overallRecommendation: parameterAnalysis.overallRecommendation || "",
      
      // 元数据
      analysisTimestamp: new Date().toISOString(),
      productCount: cleanedNames.length,
      aiModel: DEEPSEEK_MODEL
    };
    
    console.log(`\n✅ 同领域产品对比分析完成！`);
    console.log(`📱 产品类别: ${result.productCategory}`);
    console.log(`🔢 对比产品数量: ${result.productCount}`);
    console.log(`📊 分析参数数量: ${Object.keys(result.parameterAnalysis).length}`);
    
    return result;
    
  } catch (error) {
    console.error(`❌ 产品对比失败: ${error.message}`);
    return {
      success: false,
      error: error.message,
      requestedProducts: productNames,
      productCategory: null,
      productSpecs: [],
      parameterAnalysis: {},
      categoryAnalysis: "",
      overallRecommendation: "",
      analysisTimestamp: new Date().toISOString(),
      productCount: 0,
      aiModel: DEEPSEEK_MODEL
    };
  }
}

/**
 * 格式化输出结果
 * @param {Object} result 对比结果
 * @returns {String} 格式化的文本输出
 */
function formatComparisonOutput(result) {
  if (!result.success) {
    return `❌ 对比失败: ${result.error}`;
  }

  let output = [];
  
  // 标题
  output.push('='.repeat(80));
  output.push(`📊 ${result.productCategory} - 产品参数对比分析报告`);
  output.push('='.repeat(80));
  output.push(`🕒 生成时间: ${result.analysisTimestamp}`);
  output.push(`🤖 AI模型: ${result.aiModel}`);
  output.push(`📱 对比产品: ${result.requestedProducts.join(' | ')}`);
  output.push('');

  // 产品详细参数
  output.push('📋 一、产品详细参数');
  output.push('-'.repeat(50));
  
  result.productSpecs.forEach((product, index) => {
    output.push(`\n${index + 1}. ${product.name}`);
    
    // 基本信息
    if (product.basicInfo) {
      output.push(`   品牌: ${product.basicInfo.brand || '未知'}`);
      output.push(`   型号: ${product.basicInfo.model || '未知'}`);
      output.push(`   发布时间: ${product.basicInfo.releaseDate || '未知'}`);
      output.push(`   官方价格: ${product.basicInfo.officialPrice || '未知'}`);
    }
    
    // 详细规格
    if (product.detailedSpecs) {
      output.push(`   详细规格:`);
      Object.entries(product.detailedSpecs).forEach(([key, value]) => {
        if (typeof value === 'object') {
          output.push(`     ${key}:`);
          Object.entries(value).forEach(([subKey, subValue]) => {
            output.push(`       - ${subKey}: ${subValue}`);
          });
        } else {
          output.push(`     - ${key}: ${value}`);
        }
      });
    }
  });

  // AI参数分析
  output.push('\n\n🧠 二、AI参数深度分析');
  output.push('-'.repeat(50));
  
  if (result.categoryAnalysis) {
    output.push(`\n📈 产品类别分析:`);
    output.push(result.categoryAnalysis);
  }

  Object.entries(result.parameterAnalysis).forEach(([param, analysis]) => {
    output.push(`\n🔍 ${param} 参数分析:`);
    output.push(`   📝 参数说明: ${analysis.description || '暂无说明'}`);
    output.push(`   📊 对比分析: ${analysis.comparison || '暂无对比'}`);
    output.push(`   ⭐ 性能评价: ${analysis.performance || '暂无评价'}`);
    output.push(`   👤 用户影响: ${analysis.userImpact || '暂无说明'}`);
    
    if (analysis.productRanking && analysis.productRanking.length > 0) {
      output.push(`   🏆 排名推荐: ${analysis.productRanking.join(' > ')}`);
    }
  });

  // 综合推荐
  if (result.overallRecommendation) {
    output.push('\n\n💡 三、综合推荐建议');
    output.push('-'.repeat(50));
    output.push(result.overallRecommendation);
  }

  output.push('\n' + '='.repeat(80));
  
  return output.join('\n');
}

/**
 * 保存对比结果到文件
 * @param {Object} result 对比结果
 * @param {String} filename 文件名（可选）
 * @returns {Promise<String>} 保存的文件路径
 */
async function saveComparisonResult(result, filename = null) {
  try {
    if (!filename) {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      const category = result.productCategory ? result.productCategory.replace(/[^a-zA-Z0-9\u4e00-\u9fa5]/g, '_') : 'unknown';
      filename = `product_specs_comparison_${category}_${timestamp}.json`;
    }
    
    const filePath = path.join(__dirname, filename);
    
    // 保存完整JSON结果
    await fs.promises.writeFile(filePath, JSON.stringify(result, null, 2), 'utf8');
    
    // 保存格式化的文本版本
    if (result.success) {
      const txtFilename = filename.replace('.json', '.txt');
      const txtFilePath = path.join(__dirname, txtFilename);
      
      const formattedOutput = formatComparisonOutput(result);
      await fs.promises.writeFile(txtFilePath, formattedOutput, 'utf8');
      
      console.log(`📄 分析报告已保存: ${txtFilename}`);
    }
    
    console.log(`💾 对比结果已保存: ${filename}`);
    return filePath;
    
  } catch (error) {
    console.error(`保存文件失败: ${error.message}`);
    throw error;
  }
}

/**
 * 演示函数
 */
async function demo() {
  console.log('='.repeat(80));
  console.log('📊 同领域产品参数对比工具演示');
  console.log('='.repeat(80));
  
  const demos = [
    {
      title: '智能手机参数对比',
      products: ['iPhone 15 Pro', 'Samsung Galaxy S24 Ultra', 'Google Pixel 8 Pro']
    },
    {
      title: '笔记本电脑参数对比', 
      products: ['MacBook Pro M3 14英寸', 'ThinkPad X1 Carbon Gen 11', 'Dell XPS 13 Plus']
    }
  ];
  
  for (let i = 0; i < demos.length; i++) {
    const demo = demos[i];
    console.log(`\n📋 演示${i + 1}: ${demo.title}`);
    console.log(`对比产品: ${demo.products.join(' vs ')}`);
    
    try {
      const result = await compareProductsSameDomain(demo.products);
      if (result.success) {
        console.log(`\n✅ 分析成功`);
        console.log(`📊 产品类别: ${result.productCategory}`);
        console.log(`📊 分析参数: ${Object.keys(result.parameterAnalysis).length} 个`);
        
        // 保存结果
        await saveComparisonResult(result, `demo${i + 1}_${demo.title.replace(/[^a-zA-Z0-9]/g, '_')}.json`);
        
        // 显示格式化输出预览
        const formattedOutput = formatComparisonOutput(result);
        console.log('\n📄 报告预览:');
        console.log(formattedOutput.substring(0, 800) + '\n...(省略)');
        
      } else {
        console.log(`❌ 分析失败: ${result.error}`);
      }
    } catch (error) {
      console.error(`演示${i + 1}执行失败:`, error.message);
    }
    
    // 演示间隔
    if (i < demos.length - 1) {
      console.log('\n' + '-'.repeat(40));
      await new Promise(resolve => setTimeout(resolve, 3000));
    }
  }
  
  console.log('\n✅ 演示完成！');
}

// 如果直接运行此文件，执行演示
if (require.main === module) {
  // 检查环境变量
  if (!DEEPSEEK_API_KEY || DEEPSEEK_API_KEY === 'your_deepseek_api_key') {
    console.error('❌ 请设置DEEPSEEK_API_KEY环境变量');
    console.log('示例: export DEEPSEEK_API_KEY=your_actual_api_key');
    process.exit(1);
  }
  
  console.log('📊 同领域产品参数对比工具');
  console.log(`🤖 AI模型: ${DEEPSEEK_MODEL}`);
  console.log('专注于同类型产品的详细参数对比分析');
  
  // 运行演示
  demo().catch(error => {
    console.error('演示执行失败:', error.message);
    process.exit(1);
  });
}

module.exports = {
  compareProductsSameDomain,
  getProductDetailedSpecs,
  analyzeParametersByAI,
  formatComparisonOutput,
  saveComparisonResult
};