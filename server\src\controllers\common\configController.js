/**
 * 配置控制器 - 获取产品配置数据
 */

const configService = require('../../services/common/configService');
const { successResponse, errorResponse } = require('../../utils/apiResponse');

class ConfigController {
  /**
   * 获取产品类型和品牌配置
   * @param {Object} req - Express请求对象
   * @param {Object} res - Express响应对象
   */
  async getProductTypesBrands(req, res) {
    try {
      console.log('接收到获取产品配置请求');

      // 获取配置数据
      const result = await configService.getProductTypesBrands();

      // 检查服务层返回结果
      if (result && result.success) {
        // 返回成功响应
        return successResponse(res, 200, result.data, '获取成功');
      } else {
        // 处理服务层返回的错误
        const errorMessage = result && result.message ? result.message : '获取产品配置失败';
        console.error('服务层返回错误:', errorMessage);
        
        return errorResponse(res, 500, errorMessage, result && result.error ? result.error : null);
      }

    } catch (error) {
      console.error('控制器层异常:', error);
      
      return errorResponse(res, 500, '获取产品配置失败', error.message || '服务器内部错误');
    }
  }
}

module.exports = new ConfigController();
