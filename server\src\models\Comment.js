const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const CommentSchema = new Schema(
  {
    // 基本信息
    questionId: {
      type: Schema.Types.ObjectId,
      ref: 'Question',
      required: true,
      index: true
    },
    answerId: {
      type: Schema.Types.ObjectId,
      ref: 'Answer',
      required: true,
      index: true
    },
    parentId: {
      type: Schema.Types.ObjectId,
      ref: 'Comment',
      default: null,
      index: true
    },
    topCommentId: {
      type: Schema.Types.ObjectId,
      ref: 'Comment',
      default: null,
      index: true
    },
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
      index: true
    },
    targetUserId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      default: null
    },
    content: {
      type: String,
      required: [true, '评论内容不能为空'],
      maxlength: [200, '评论内容最多200个字符'],
      trim: true
    },
    isAnonymous: {
      type: Boolean,
      default: false
    },
    // 添加软删除相关字段
    isDeleted: {
      type: Boolean,
      default: false,
      index: true
    },
    deleteReason: {
      type: String,
      enum: ['user_delete', 'admin_delete', 'violation'],
      default: null
    },
    deletedAt: {
      type: Date,
      default: null
    },
    // 父评论是否已删除
    parentDeleted: {
      type: Boolean,
      default: false
    }
  },
  {
    timestamps: true
  }
);

// 发表评论时更新相关计数器
CommentSchema.post('save', async function() {
  // if (this.isNew) {
  if (true) {
    // 更新回答的评论计数
    const Answer = mongoose.model('Answer');
    await Answer.findByIdAndUpdate(
      this.answerId,
      { $inc: { commentCount: 1 } }
    );

    // 如果是顶级评论（不是回复），同时更新问题的评论计数
    if (!this.parentId) {
      const Question = mongoose.model('Question');
      await Question.findByIdAndUpdate(
        this.questionId,
        { $inc: { commentCount: 1 } }
      );
    }
  }
});

// 删除评论时更新相关计数器
CommentSchema.post('remove', async function() {
  // 更新回答的评论计数
  const Answer = mongoose.model('Answer');
  await Answer.findByIdAndUpdate(
    this.answerId,
    { $inc: { commentCount: -1 } }
  );

  // 如果是顶级评论（不是回复），同时更新问题的评论计数
  if (!this.parentId) {
    const Question = mongoose.model('Question');
    await Question.findByIdAndUpdate(
      this.questionId,
      { $inc: { commentCount: -1 } }
    );
  }
});

// 评论规则验证
// CommentSchema.pre('save', async function(next) {
//   // if (this.isNew)
//   if (true) {
//     try {
//       const Answer = mongoose.model('Answer');
//       const answer = await Answer.findById(this.answerId);
//       const Question = mongoose.model('Question');
//       const question = await Question.findById(this.questionId);
      
//       // 回答者自己
//       if (this.userId.toString() === answer.userId.toString()) {
//         // 检查是回复问题发布者
//         if (this.targetUserId && this.targetUserId.toString() !== question.userId.toString()) {
//           throw new Error('回答者只能回复问题发布者的评论');
//         }
//         return next();
//       }
      
//       // 问题发布者
//       if (this.userId.toString() === question.userId.toString()) {
//         return next();
//       }
      
//       // 其他用户不允许评论
//       throw new Error('只有问题发布者和回答者可以参与评论');
//     } catch (error) {
//       return next(error);
//     }
//   }
//   next();
// });

module.exports = mongoose.model('Comment', CommentSchema); 