<!--pages/user/feedback/feedback.wxml-->
<view class="page">
  <view class="container p-30">
    <!-- 反馈表单 -->
    <view class="feedback-form">
      <!-- 反馈类型选择 -->
      <view class="card mb-20">
        <view class="card-body">
          <view class="form-group">
            <view class="form-label">反馈类型</view>
            <view class="type-options">
              <block wx:for="{{typeOptions}}" wx:key="value">
                <view 
                  class="type-option {{selectedTypeIndex === index ? 'selected' : ''}}"
                  bindtap="onTypeSelect"
                  data-index="{{index}}"
                >
                  <text class="option-text">{{item.label}}</text>
                </view>
              </block>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 反馈内容 -->
      <view class="card mb-20">
        <view class="card-body">
          <view class="form-group">
            <view class="form-label required">反馈内容</view>
            <view class="content-input">
              <textarea
                placeholder="请描述您遇到的问题或建议（500字以内）"
                value="{{content}}"
                bindinput="onContentInput"
                maxlength="500"
                show-confirm-bar="{{false}}"
                auto-height
                class="form-textarea"
              ></textarea>
              <view class="char-count">{{content.length}}/500</view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 图片上传 -->
      <view class="card mb-40">
        <view class="card-body">
          <view class="form-group">
            <view class="form-label">
              相关图片
              <text class="subtitle">（选填，最多6张）</text>
            </view>
            <view class="image-upload">
              <!-- 已上传的图片 -->
              <view class="image-list">
                <block wx:for="{{images}}" wx:key="index">
                  <view class="image-item">
                    <image 
                      src="{{item.file_url}}" 
                      mode="aspectFill"
                      bindtap="previewImage"
                      bindload="onImageLoad"
                      binderror="onImageError"
                      data-index="{{index}}"
                      lazy-load="{{true}}"
                      show-menu-by-longpress="{{true}}"
                    ></image>
                    <view 
                      class="delete-btn" 
                      bindtap="deleteImage"
                      data-index="{{index}}"
                    >
                      <text class="iconfont icon-close">×</text>
                    </view>
                  </view>
                </block>
                
                <!-- 添加图片按钮 -->
                <view 
                  class="add-image-btn" 
                  bindtap="chooseImage"
                  wx:if="{{images.length < maxImages}}"
                >
                  <text class="add-icon">+</text>
                  <text class="add-text">添加图片</text>
                </view>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 底部操作区 -->
    <view class="bottom-actions">
      <view class="action-tips mb-20">
        <text class="tips-text text-secondary font-size-small">感谢您的反馈，我们会尽快处理</text>
      </view>
      
      <view class="button-group flex">
        <button 
          class="btn btn-secondary mr-20" 
          bindtap="viewMyFeedback"
        >
          我的反馈
        </button>
        <button 
          class="btn btn-primary flex-1" 
          bindtap="submitFeedback"
          loading="{{loading}}"
          disabled="{{loading || !canSubmit}}"
        >
          提交反馈
        </button>
      </view>
    </view>
  </view>
</view> 