import WxCanvas from './wx-canvas';
import * as echarts from './wx-echarts';
const darkTheme = require('./dark');

let ctx;

Component({
	properties: {
		canvasId: {
			type: String,
			value: 'ec-canvas'
		},

		data: {
			type: Object,
			value: {}
		},

		ec: {
			type: Object
		}
	},

	data: {
		size: {
			height: 240
		}
	},

	ready: function () {
		if (!this.data.ec) {
			console.warn('组件需绑定 ec 变量，例：<ec-canvas id="mychart-dom-bar" ' +
				'canvas-id="mychart-bar" ec="{{ ec }}"></ec-canvas>');
			return;
		}

		if (!this.data.ec.lazyLoad) {
			this.init();
		}
	},
	lifetimes: {
		attached: function () {
			const _ts = this;

			let dataAttr = this.data.data.attrs,
				obj = JSON.parse(decodeURIComponent(dataAttr.value));
			obj.option.color = ['#60acfc', '#32d3eb', '#5bc49f', '#feb64d', '#ff7c7c', '#9287e7'];
			if (obj.height) {
				_ts.setData({
					size: {
						height: obj.height
					}
				})
			}
			_ts.data.ec = {};
			_ts.data.ec.onInit = function (canvas, width, height) {
				echarts.registerTheme('dark', darkTheme);
				const theme = global._theme === 'dark' ? 'dark' : null,
					chart = echarts.init(canvas, theme, {
						width: width,
						height: height
					});
				canvas.setChart(chart);

				chart.setOption(obj.option);
				return chart;
			};
		},
		moved: function () {},
		detached: () => {

		},
	},
	methods: {
		init: function (callback) {
			const version = wx.version.version.split('.').map(n => parseInt(n, 10));
			const isValid = version[0] > 1 || (version[0] === 1 && version[1] > 9) ||
				(version[0] === 1 && version[1] === 9 && version[2] >= 91);
			if (!isValid) {
				console.error('微信基础库版本过低，需大于等于 1.9.91。' +
					'参见：https://github.com/ecomfe/echarts-for-weixin' +
					'#%E5%BE%AE%E4%BF%A1%E7%89%88%E6%9C%AC%E8%A6%81%E6%B1%82');
				return;
			}

			ctx = wx.createCanvasContext(this.data.canvasId, this);

			const canvas = new WxCanvas(ctx, this.data.canvasId);

			echarts.setCanvasCreator(() => {
				return canvas;
			});

			var query = wx.createSelectorQuery().in(this);
			query.select('.ec-canvas').boundingClientRect(res => {
				if (typeof callback === 'function') {
					this.chart = callback(canvas, res.width, res.height);
				} else if (this.data.ec && typeof this.data.ec.onInit === 'function') {
					this.chart = this.data.ec.onInit(canvas, res.width, res.height);
				} else {
					this.triggerEvent('init', {
						canvas: canvas,
						width: res.width,
						height: res.height
					});
				}
			}).exec();
		},

		canvasToTempFilePath(opt) {
			if (!opt.canvasId) {
				opt.canvasId = this.data.canvasId;
			}

			ctx.draw(true, () => {
				wx.canvasToTempFilePath(opt, this);
			});
		},

		touchStart(e) {
			if (this.chart && e.touches.length > 0) {
				var touch = e.touches[0];
				var handler = this.chart.getZr().handler;
				handler.dispatch('mousedown', {
					zrX: touch.x,
					zrY: touch.y
				});
				handler.dispatch('mousemove', {
					zrX: touch.x,
					zrY: touch.y
				});
				handler.processGesture(wrapTouch(e), 'start');
			}
		},

		touchMove(e) {
			if (this.chart && e.touches.length > 0) {
				var touch = e.touches[0];
				var handler = this.chart.getZr().handler;
				handler.dispatch('mousemove', {
					zrX: touch.x,
					zrY: touch.y
				});
				handler.processGesture(wrapTouch(e), 'change');
			}
		},

		touchEnd(e) {
			if (this.chart) {
				const touch = e.changedTouches ? e.changedTouches[0] : {};
				var handler = this.chart.getZr().handler;
				handler.dispatch('mouseup', {
					zrX: touch.x,
					zrY: touch.y
				});
				handler.dispatch('click', {
					zrX: touch.x,
					zrY: touch.y
				});
				handler.processGesture(wrapTouch(e), 'end');
			}
		}
	}
});

function wrapTouch(event) {
	for (let i = 0; i < event.touches.length; ++i) {
		const touch = event.touches[i];
		touch.offsetX = touch.x;
		touch.offsetY = touch.y;
	}
	return event;
}