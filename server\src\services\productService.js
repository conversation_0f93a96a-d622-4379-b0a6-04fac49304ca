const Product = require('../models/Product');
const ProductComparisonCache = require('../models/ProductComparisonCache');

const { generateProductComparison } = require('../utils/aiHelper');

/**
 * 根据产品名称列表获取产品参数对比数据（带缓存优化）
 * @param {Array<String>} productNames 产品名称列表
 * @returns {Promise<Object>} 对比结果
 */
const compareProductsByNames = async (productNames) => {
  try {
    // 验证输入参数
    if (!Array.isArray(productNames) || productNames.length < 2) {
      throw new Error('至少需要提供2个产品进行对比');
    }

    if (productNames.length > 5) {
      throw new Error('最多支持5个产品同时对比');
    }

    // 首先查找实际存在的产品（修改：提前进行产品查找）
    console.log('开始查找产品数据');
    const productResult = await findProductsByNames(productNames);
    
    if (!productResult.success) {
      return productResult;
    }

    const { products, notFoundProducts, productType } = productResult.data;

    // 基于实际存在的产品生成缓存键（修改：使用实际找到的产品名称）
    const actualProductNames = products.map(p => p.skuName).sort(); // 排序确保缓存键一致性
    const comparisonKey = ProductComparisonCache.generateComparisonKey(actualProductNames);

    // Layer 1: 尝试从缓存中获取数据
    console.log('检查缓存:', comparisonKey);
    let cachedComparison = await ProductComparisonCache.findOne({ comparisonKey });

    if (cachedComparison && !cachedComparison.isExpired()) {
      console.log('缓存命中，更新热度');
      
      // 更新热度统计
      await cachedComparison.updateHotness();
      
      return {
        success: true,
        data: {
          productType: cachedComparison.productInfo.productType,
          products: cachedComparison.cachedData.formattedProducts,
          comparison: cachedComparison.cachedData.comparisonTable,
          aiAnalysis: cachedComparison.cachedData.aiAnalysis,
          notFoundProducts, // 返回未找到的产品列表
          fromCache: true,
          cacheStats: {
            hitCount: cachedComparison.hotness.hitCount,
            heatScore: cachedComparison.hotness.heatScore
          }
        }
      };
    }

    // Layer 2: 缓存未命中或已过期，重新计算
    console.log('缓存未命中，开始实时计算');

    // 生成对比表格数据
    const comparisonData = await generateComparisonTable(products, productType);

    // 生成AI分析（增量缓存策略 - 只缓存AI结果）
    const aiAnalysis = await generateAIAnalysisWithCache(products, comparisonData, comparisonKey);

    // 格式化产品数据
    const formattedProducts = products.map(formatProductForComparison);

    // 保存到缓存（修改：使用实际产品名称作为缓存数据）
    await saveToCacheAsync(comparisonKey, {
      productInfo: {
        productIds: products.map(p => p._id),
        productNames: actualProductNames, // 使用实际产品名称
        productType
      },
      cachedData: {
        comparisonTable: comparisonData,
        aiAnalysis,
        formattedProducts
      }
    });

    return {
      success: true,
      data: {
        productType,
        products: formattedProducts,
        comparison: comparisonData,
        aiAnalysis,
        notFoundProducts,
        fromCache: false
      }
    };

  } catch (error) {
    console.error('产品对比出错:', error);
    throw error;
  }
};

/**
 * 查找产品并验证（抽取的公共逻辑）
 * @param {Array<String>} productNames 产品名称列表
 * @returns {Promise<Object>} 查找结果
 */
const findProductsByNames = async (productNames) => {
  const products = [];
  const notFoundProducts = [];

  for (const productName of productNames) {
    const trimmedName = productName.trim();
    if (!trimmedName) continue;

    let product = null;

    try {
      console.log(`正在为产品对比查找: "${trimmedName}"`);
      
      // 🆕 Step 1: 优先进行精准匹配（直接使用skuName查找）
      product = await Product.findOne({
        skuName: trimmedName,
        supportsComparison: true
      }).lean();

      if (product) {
        console.log(`✅ 精准匹配成功: "${trimmedName}"`);
        products.push(product);
        continue;
      }

      // 🆕 Step 2: 精准匹配失败，使用智能搜索作为备选方案
      console.log(`精准匹配未找到，启用智能搜索: "${trimmedName}"`);
      
      const searchResult = await searchProductNames(trimmedName, 5, '');
      
      if (searchResult.success && searchResult.data.productNames.length > 0) {
        // 获取最匹配的产品名称
        const bestMatchName = searchResult.data.productNames[0];
        console.log(`智能匹配结果: "${trimmedName}" -> "${bestMatchName}"`);
        
        // 根据匹配到的产品名称获取完整的产品信息
        product = await Product.findOne({
          skuName: bestMatchName,
          supportsComparison: true
        }).lean();
        
        if (product) {
          products.push(product);
          console.log(`✅ 智能搜索成功找到产品: ${product.skuName}`);
        } else {
          console.log(`❌ 搜索结果中的产品不支持对比: ${bestMatchName}`);
          notFoundProducts.push(trimmedName);
        }
      } else {
        console.log(`❌ 智能搜索未找到匹配的产品: "${trimmedName}"`);
        notFoundProducts.push(trimmedName);
      }
    } catch (searchError) {
      console.error(`产品搜索出错 "${trimmedName}":`, searchError);
      notFoundProducts.push(trimmedName);
    }
  }

  if (products.length < 2) {
    return {
      success: false,
      message: '找到的可对比产品数量不足',
      data: {
        foundProducts: products.map(p => p.skuName),
        notFoundProducts
      }
    };
  }

  // 检查产品类型是否一致
  const productTypes = [...new Set(products.map(p => p.productType))];
  if (productTypes.length > 1) {
    return {
      success: false,
      message: '只能对比相同类型的产品',
      data: {
        foundProductTypes: productTypes
      }
    };
  }

  return {
    success: true,
    data: {
      products,
      notFoundProducts,
      productType: productTypes[0]
    }
  };
};

/**
 * 带缓存优化的AI分析生成
 * @param {Array} products 产品列表
 * @param {Array} comparisonData 对比数据
 * @param {String} comparisonKey 对比键（基于实际存在的产品）
 * @returns {Promise<Array>} AI分析结果
 */
const generateAIAnalysisWithCache = async (products, comparisonData, comparisonKey) => {
  try {
    // 记录实际参与AI分析的产品信息
    const actualProductNames = products.map(p => p.skuName);
    console.log(`开始AI分析缓存处理，实际产品: [${actualProductNames.join(', ')}]`);

    // 检查是否有部分缓存的AI分析结果
    const existingCache = await ProductComparisonCache.findOne({ comparisonKey });
    
    if (existingCache && existingCache.cachedData.aiAnalysis && existingCache.cachedData.aiAnalysis.length > 0) {
      // 检查AI缓存是否仍然有效（1小时内）
      const aiCacheAge = Date.now() - existingCache.cacheMetadata.aiGeneratedAt.getTime();
      const oneHour = 60 * 60 * 1000;
      
      if (aiCacheAge < oneHour) {
        console.log(`使用缓存的AI分析结果，缓存年龄: ${Math.round(aiCacheAge / 1000 / 60)}分钟`);
        return existingCache.cachedData.aiAnalysis;
      } else {
        console.log(`AI分析缓存已过期，缓存年龄: ${Math.round(aiCacheAge / 1000 / 60)}分钟，重新生成`);
      }
    } else {
      console.log('未找到AI分析缓存，将重新生成');
    }

    // 重新生成AI分析
    console.log(`开始为${actualProductNames.length}个产品生成新的AI分析`);
    return await generateAIAnalysis(products, comparisonData);

  } catch (error) {
    console.error('AI分析缓存处理失败:', error);
    // 记录错误详情但不阻塞主流程
    console.error('错误详情:', {
      productCount: products ? products.length : 0,
      comparisonKey,
      errorMessage: error.message
    });
    return await generateAIAnalysis(products, comparisonData);
  }
};

/**
 * 异步保存到缓存（非阻塞）
 * @param {String} comparisonKey 对比键（基于实际存在的产品）
 * @param {Object} cacheData 缓存数据
 */
const saveToCacheAsync = async (comparisonKey, cacheData) => {
  try {
    // 验证输入参数
    if (!comparisonKey || !cacheData || !cacheData.productInfo || !cacheData.cachedData) {
      console.error('缓存保存失败：缺少必要参数');
      return;
    }

    // 计算缓存大小
    const cacheSize = JSON.stringify(cacheData).length;
    const actualProductNames = cacheData.productInfo.productNames;

    console.log(`开始保存缓存，产品: [${actualProductNames.join(', ')}]，大小: ${Math.round(cacheSize / 1024)}KB`);

    // 检查是否已存在缓存
    const existingCache = await ProductComparisonCache.findOne({ comparisonKey });

    if (existingCache) {
      // 更新现有缓存
      console.log(`更新现有缓存，原缓存命中次数: ${existingCache.hotness.hitCount}`);
      
      existingCache.cachedData = cacheData.cachedData;
      existingCache.productInfo = cacheData.productInfo;
      existingCache.cacheMetadata.cacheSize = cacheSize;
      existingCache.cacheMetadata.aiGeneratedAt = new Date();
      existingCache.cacheMetadata.status = 'active';
      
      await existingCache.updateHotness();
      console.log(`缓存更新成功: ${comparisonKey}，新热度得分: ${existingCache.hotness.heatScore.toFixed(2)}`);
    } else {
      // 创建新缓存
      console.log('创建新缓存记录');
      
      const newCache = new ProductComparisonCache({
        comparisonKey,
        ...cacheData,
        cacheMetadata: {
          cacheSize,
          status: 'active',
          aiGeneratedAt: new Date()
        }
      });

      await newCache.save();
      console.log(`新缓存创建成功: ${comparisonKey}，产品数量: ${actualProductNames.length}`);
    }

    // 异步清理低热度缓存（不阻塞主流程）
    setImmediate(() => cleanupLowHeatCaches());

  } catch (error) {
    console.error('保存缓存失败:', error);
    // 记录详细的错误信息用于调试
    console.error('缓存保存错误详情:', {
      comparisonKey,
      productCount: cacheData?.productInfo?.productNames?.length || 0,
      errorMessage: error.message,
      errorStack: error.stack
    });
    // 缓存失败不影响主流程
  }
};

/**
 * 清理低热度缓存
 */
const cleanupLowHeatCaches = async () => {
  try {
    const thirtyDaysAgo = new Date(Date.now() - 30 * 24 * 60 * 60 * 1000);
    
    // 清理超过30天未访问的低热度缓存
    const result = await ProductComparisonCache.deleteMany({
      'hotness.lastAccessTime': { $lt: thirtyDaysAgo },
      'hotness.heatScore': { $lt: 2 }
    });

    if (result.deletedCount > 0) {
      console.log(`清理了 ${result.deletedCount} 个低热度缓存`);
    }
  } catch (error) {
    console.error('清理缓存失败:', error);
  }
};

/**
 * 获取缓存统计信息
 * @returns {Promise<Object>} 缓存统计
 */
const getCacheStats = async () => {
  try {
    const [totalCaches, activeCaches, topHotCaches] = await Promise.all([
      ProductComparisonCache.countDocuments(),
      ProductComparisonCache.countDocuments({ 'cacheMetadata.status': 'active' }),
      ProductComparisonCache.find({ 'cacheMetadata.status': 'active' })
        .sort({ 'hotness.heatScore': -1 })
        .limit(10)
        .select('comparisonKey productInfo.productNames hotness')
        .lean()
    ]);

    return {
      total: totalCaches,
      active: activeCaches,
      topHot: topHotCaches
    };
  } catch (error) {
    console.error('获取缓存统计失败:', error);
    return { total: 0, active: 0, topHot: [] };
  }
};

/**
 * 生成产品对比表格数据
 * @param {Array} products 产品列表
 * @param {String} productType 产品类型
 * @returns {Promise<Array>} 对比表格数据
 */
const generateComparisonTable = async (products, productType) => {
  const comparisonRows = [];

  // 根据产品类型获取对比参数
  const compareFields = getCompareFieldsByType(productType);

  // 生成基本信息对比
  const basicFields = [
    { key: 'skuName', label: '产品名称', category: 'basic' },
    { key: 'price', label: '价格', category: 'basic', formatter: (value) => value ? `¥${value}` : '-' },
    { key: 'brandName', label: '品牌', category: 'basic' },
    { key: 'defaultConfig.ram', label: '运行内存', category: 'basic', formatter: (value) => value ? `${value}` : '-' },
    { key: 'defaultConfig.storage', label: '存储容量', category: 'basic', formatter: (value) => value ? `${value}` : '-' }
  ];

  // 合并基本信息和产品规格
  const allFields = [...basicFields, ...compareFields];

  // 生成每一行的对比数据
  for (const field of allFields) {
    const row = {
      parameter: field.label,
      category: field.category,
      values: []
    };

    for (const product of products) {
      let value = getFieldValue(product, field.key);
      
      // 应用格式化器
      if (field.formatter && value) {
        value = field.formatter(value);
      }

      row.values.push({
        productId: product._id,
        productName: product.skuName,
        value: value || '-'
      });
    }

    comparisonRows.push(row);
  }

  return comparisonRows;
};

/**
 * 生成AI对比分析（优化版本 - 批量分析）
 * @param {Array} products 产品列表
 * @param {Array} comparisonData 对比数据
 * @returns {Promise<Array>} AI分析结果
 */
const generateAIAnalysis = async (products, comparisonData) => {
  try {
    // 过滤出需要AI分析的参数
    const analysisRows = comparisonData.filter(row => 
      row.category !== 'basic' && 
      row.values && 
      row.values.filter(v => v.value && v.value !== '-').length >= 2
    );

    if (analysisRows.length === 0) {
      return comparisonData.map(row => ({
        parameter: row.parameter,
        analysis: row.category === 'basic' ? '-' : '暂无数据可供分析'
      }));
    }

    // 构建批量分析提示词
    const batchPrompt = buildBatchAnalysisPrompt(products, analysisRows);
    
    if (!batchPrompt) {
      return comparisonData.map(row => ({
        parameter: row.parameter,
        analysis: row.category === 'basic' ? '-' : '分析暂时不可用'
      }));
    }

    try {
      // 一次性请求AI分析所有参数
      console.log('开始批量AI分析，参数数量:', analysisRows.length);
      const batchAnalysis = await generateProductComparison(batchPrompt);
      
      // 解析AI返回的批量分析结果
      const analysisResults = parseBatchAnalysisResponse(batchAnalysis, comparisonData);
      
      console.log('批量AI分析完成');
      return analysisResults;
    } catch (aiError) {
      console.error('批量AI分析失败:', aiError);
      return comparisonData.map(row => ({
        parameter: row.parameter,
        analysis: row.category === 'basic' ? '-' : '分析暂时不可用'
      }));
    }

  } catch (error) {
    console.error('生成AI分析失败:', error);
    return comparisonData.map(row => ({
      parameter: row.parameter,
      analysis: row.category === 'basic' ? '-' : '分析暂时不可用'
    }));
  }
};

/**
 * 构建批量AI分析提示词
 * @param {Array} products 产品列表
 * @param {Array} analysisRows 需要分析的参数行
 * @returns {String} 批量分析提示词
 */
const buildBatchAnalysisPrompt = (products, analysisRows) => {
  if (!products || products.length === 0 || !analysisRows || analysisRows.length === 0) {
    return null;
  }

  const productNames = products.map(p => p.skuName);
  
  let prompt = `请对比以下${productNames.length}款产品的各项参数，每个参数请用30字以内简洁说明差异和优劣：\n\n`;
  prompt += `对比产品：${productNames.join(' vs ')}\n\n`;
  
  analysisRows.forEach((row, index) => {
    const validValues = row.values.filter(v => v.value && v.value !== '-');
    if (validValues.length >= 2) {
      const comparisons = validValues.map(v => `${v.productName}: ${v.value}`).join('，');
      prompt += `${index + 1}. ${row.parameter}：${comparisons}\n`;
    }
  });
  
  prompt += `\n请严格按照以下格式返回分析结果，每个参数占一行：\n`;
  prompt += `参数名称|分析内容\n`;
  prompt += `例如：\n`;
  prompt += `屏幕尺寸|iPhone屏幕更紧凑便携，华为显示面积更大适合观影\n`;
  prompt += `处理器|华为芯片性能更强，iPhone功耗控制更好\n\n`;
  prompt += `重要说明：请确保每行都是"参数名称|分析内容"的格式，不要添加其他内容。`;
  
  return prompt;
};

/**
 * 解析批量AI分析响应
 * @param {String} batchResponse AI批量分析响应
 * @param {Array} comparisonData 原始对比数据
 * @returns {Array} 解析后的分析结果
 */
const parseBatchAnalysisResponse = (batchResponse, comparisonData) => {
  const analysisResults = [];
  
  // 创建参数名到分析结果的映射
  const analysisMap = new Map();
  
  try {
    // 按行分割AI响应，过滤空行
    const lines = batchResponse.split('\n').filter(line => line.trim());
    
    for (const line of lines) {
      // 查找包含|分隔符的行
      const pipeIndex = line.indexOf('|');
      if (pipeIndex > 0) {
        const parameter = line.substring(0, pipeIndex).trim();
        const analysis = line.substring(pipeIndex + 1).trim();
        
        // 移除可能的序号前缀（如"1. "）
        const cleanParameter = parameter.replace(/^\d+\.\s*/, '');
        
        if (cleanParameter && analysis) {
          analysisMap.set(cleanParameter, analysis);
        }
      }
    }
    
    console.log('解析到的AI分析结果数量:', analysisMap.size);
  } catch (parseError) {
    console.error('解析AI分析响应失败:', parseError);
  }
  
  // 为每个参数匹配分析结果
  for (const row of comparisonData) {
    let analysis;
    
    if (row.category === 'basic') {
      analysis = '-';
    } else {
      // 尝试精确匹配参数名
      analysis = analysisMap.get(row.parameter);
      
      // 如果精确匹配失败，尝试模糊匹配
      if (!analysis) {
        for (const [key, value] of analysisMap) {
          if (key.includes(row.parameter) || row.parameter.includes(key)) {
            analysis = value;
            break;
          }
        }
      }
      
      // 如果还是没有找到，设置默认值
      if (!analysis) {
        analysis = '分析暂时不可用';
      }
    }
    
    analysisResults.push({
      parameter: row.parameter,
      analysis
    });
  }
  
  return analysisResults;
};

/**
 * 根据产品类型获取对比字段
 * @param {String} productType 产品类型
 * @returns {Array} 对比字段配置
 */
const getCompareFieldsByType = (productType) => {
  const fieldConfigs = {
    phone: [
      { key: 'productSpecs.phone.screenSize', label: '屏幕尺寸', category: 'display' },
      { key: 'productSpecs.phone.screenResolution', label: '屏幕分辨率', category: 'display' },
      { key: 'productSpecs.phone.processor', label: '处理器', category: 'performance' },
      { key: 'productSpecs.phone.battery', label: '电池容量', category: 'battery' },
      { key: 'productSpecs.phone.camera.rear', label: '后置摄像头', category: 'camera' },
      { key: 'productSpecs.phone.camera.front', label: '前置摄像头', category: 'camera' },
      { key: 'productSpecs.phone.operatingSystem', label: '操作系统', category: 'system' },
      { key: 'productSpecs.phone.network', label: '网络制式', category: 'connectivity' }
    ],
    laptop: [
      { key: 'productSpecs.laptop.screenSize', label: '屏幕尺寸', category: 'display' },
      { key: 'productSpecs.laptop.screenResolution', label: '屏幕分辨率', category: 'display' },
      { key: 'productSpecs.laptop.processor', label: '处理器', category: 'performance' },
      { key: 'productSpecs.laptop.graphics', label: '显卡', category: 'performance' },
      { key: 'productSpecs.laptop.operatingSystem', label: '操作系统', category: 'system' },
      { key: 'productSpecs.laptop.ports', label: '接口配置', category: 'connectivity' },
      { key: 'productSpecs.laptop.battery', label: '电池续航', category: 'battery' }
    ],
    tablet: [
      { key: 'productSpecs.tablet.screenSize', label: '屏幕尺寸', category: 'display' },
      { key: 'productSpecs.tablet.screenResolution', label: '屏幕分辨率', category: 'display' },
      { key: 'productSpecs.tablet.processor', label: '处理器', category: 'performance' },
      { key: 'productSpecs.tablet.battery', label: '电池容量', category: 'battery' },
      { key: 'productSpecs.tablet.operatingSystem', label: '操作系统', category: 'system' }
    ]
  };

  return fieldConfigs[productType] || [];
};

/**
 * 获取产品字段值（支持嵌套字段和新的配置字段）
 * @param {Object} product 产品对象
 * @param {String} fieldKey 字段键（支持点分隔的嵌套键）
 * @returns {*} 字段值
 */
const getFieldValue = (product, fieldKey) => {
  if (!fieldKey) return null;

  // 🆕 处理配置相关字段
  if (fieldKey.startsWith('defaultConfig.')) {
    const configField = fieldKey.replace('defaultConfig.', '');
    
    // 获取默认配置
    const defaultConfig = product.configurations?.find(
      config => config.name === product.defaultConfiguration
    ) || product.configurations?.[0];
    
    if (!defaultConfig) {
      // 向后兼容：如果没有配置数据，尝试从旧的productSpecs中获取
      const productType = product.productType;
      if (configField === 'ram') {
        return product.productSpecs?.[productType]?.ram || null;
      }
      if (configField === 'storage') {
        return product.productSpecs?.[productType]?.storage || null;
      }
      return null;
    }
    
    return defaultConfig[configField] || null;
  }
  
  // 🆕 处理价格字段的特殊逻辑
  if (fieldKey === 'price') {
    // 如果有多个配置，显示价格范围
    if (product.configurations && product.configurations.length > 1) {
      const prices = product.configurations.map(config => config.price);
      const minPrice = Math.min(...prices);
      const maxPrice = Math.max(...prices);
      return minPrice === maxPrice ? `¥${minPrice}` : `¥${minPrice} - ¥${maxPrice}`;
    } 
    // 如果只有一个配置，显示该配置的价格
    else if (product.configurations && product.configurations.length === 1) {
      return product.configurations[0].price;
    }
    // 向后兼容：使用原有的price字段
    else {
      return product.price;
    }
  }

  // 原有的嵌套字段处理逻辑
  const keys = fieldKey.split('.');
  let value = product;

  for (const key of keys) {
    if (value && typeof value === 'object' && key in value) {
      value = value[key];
    } else {
      return null;
    }
  }

  return value;
};

/**
 * 格式化产品数据用于对比展示
 * @param {Object} product 产品对象
 * @returns {Object} 格式化后的产品数据
 */
const formatProductForComparison = (product) => {
  // 获取默认配置
  const defaultConfig = product.configurations?.find(
    config => config.name === product.defaultConfiguration
  ) || product.configurations?.[0];

  // 构建显示名称
  let displayName = product.skuName;
  if (defaultConfig && product.configurations?.length > 1) {
    // 如果有多个配置，在产品名称后显示默认配置
    displayName = `${product.skuName} (${defaultConfig.name})`;
  }

  // 确定显示价格
  let displayPrice = product.price; // 向后兼容
  let priceRange = null;
  
  if (product.configurations && product.configurations.length > 0) {
    if (product.configurations.length === 1) {
      // 单一配置
      displayPrice = product.configurations[0].price;
    } else {
      // 多配置，显示默认配置价格，同时提供价格范围
      displayPrice = defaultConfig?.price || product.configurations[0].price;
      const prices = product.configurations.map(config => config.price);
      priceRange = {
        min: Math.min(...prices),
        max: Math.max(...prices)
      };
    }
  }

  return {
    id: product._id,
    skuId: product.skuId,
    name: product.skuName,
    displayName: displayName, // 🆕 用于显示的完整名称
    price: displayPrice,
    priceRange: priceRange, // 🆕 价格范围信息
    configurations: product.configurations || [], // 🆕 所有配置信息
    defaultConfiguration: product.defaultConfiguration, // 🆕 默认配置标识
    image: product.imageUrl,
    brand: product.brandName,
    productType: product.productType,
    // 🆕 默认配置的详细信息
    defaultConfigDetails: defaultConfig ? {
      name: defaultConfig.name,
      ram: defaultConfig.ram,
      storage: defaultConfig.storage,
      price: defaultConfig.price,
      available: defaultConfig.available
    } : null
  };
};

/**
 * 字符串标准化处理（用于搜索匹配）
 * 统一处理空格、标点符号、大小写等匹配问题
 * @param {String} str 待标准化的字符串
 * @returns {String} 标准化后的字符串
 */
const normalizeStringForSearch = (str) => {
  if (!str || typeof str !== 'string') return '';
  
  return str
    .toLowerCase()                          // 转换为小写
    .replace(/\s+/g, '')                   // 移除所有空格（包括多个连续空格）
    .replace(/[^\w\u4e00-\u9fa5]/g, '')    // 移除标点符号，只保留字母、数字、中文字符
    .trim();                               // 移除首尾空白
};

/**
 * 智能字符串匹配检查
 * 使用searchMatch字段进行匹配，同时支持标准化匹配
 * @param {String} searchMatchValue 产品的searchMatch字段值
 * @param {String} keyword 搜索关键词
 * @returns {Object} 匹配结果 { matched: boolean, matchType: string, score: number }
 */
const checkStringMatch = (searchMatchValue, keyword) => {
  const originalSearchMatch = (searchMatchValue || '').toLowerCase();
  const originalKeyword = keyword.toLowerCase();
  
  const normalizedSearchMatch = normalizeStringForSearch(searchMatchValue || '');
  const normalizedKeyword = normalizeStringForSearch(keyword);
  
  // 检查各种匹配情况
  const matches = {
    // 原始字符串匹配（优先级最高）
    originalExact: originalSearchMatch === originalKeyword,
    originalPrefix: originalSearchMatch.startsWith(originalKeyword),
    originalContains: originalSearchMatch.includes(originalKeyword),
    
    // 标准化字符串匹配（解决空格等问题）
    normalizedExact: normalizedSearchMatch === normalizedKeyword,
    normalizedPrefix: normalizedSearchMatch.startsWith(normalizedKeyword),
    normalizedContains: normalizedSearchMatch.includes(normalizedKeyword)
  };
  
  // 根据匹配类型返回结果
  if (matches.originalExact || matches.normalizedExact) {
    return { matched: true, matchType: 'exact', score: 100 };
  } else if (matches.originalPrefix || matches.normalizedPrefix) {
    return { matched: true, matchType: 'prefix', score: 80 };
  } else if (matches.originalContains || matches.normalizedContains) {
    return { matched: true, matchType: 'contains', score: 60 };
  }
  
  return { matched: false, matchType: 'none', score: 0 };
};

/**
 * 🆕 智能分词匹配检查
 * 支持中英文分词匹配，解决"华为mate70"匹配"华为huaweimate70"的问题
 * @param {String} searchMatchValue 产品的searchMatch字段值
 * @param {String} keyword 搜索关键词
 * @returns {Object} 匹配结果 { matched: boolean, matchType: string, score: number }
 */
const checkIntelligentMatch = (searchMatchValue, keyword) => {
  if (!searchMatchValue || !keyword) return { matched: false, matchType: 'none', score: 0 };
  
  const searchMatch = searchMatchValue.toLowerCase();
  const searchKeyword = keyword.toLowerCase();
  
  // 1. 先尝试原有的完整匹配
  const directMatch = checkStringMatch(searchMatchValue, keyword);
  if (directMatch.matched) {
    return directMatch;
  }
  
  // 2. 🆕 分词匹配：将关键词拆分为中文和英文数字部分
  const chineseChars = searchKeyword.match(/[\u4e00-\u9fa5]+/g) || [];
  const englishChars = searchKeyword.match(/[a-zA-Z0-9]+/g) || [];
  
  // 如果没有分词，返回不匹配
  if (chineseChars.length === 0 && englishChars.length === 0) {
    return { matched: false, matchType: 'none', score: 0 };
  }
  
  let matchScore = 0;
  let matchedParts = 0;
  const totalParts = chineseChars.length + englishChars.length;
  
  console.log(`分词匹配分析 - 关键词: "${keyword}"`);
  console.log(`中文部分: [${chineseChars.join(', ')}]`);
  console.log(`英文数字部分: [${englishChars.join(', ')}]`);
  console.log(`目标searchMatch: "${searchMatch}"`);
  
  // 检查中文部分匹配
  chineseChars.forEach((chinese, index) => {
    if (searchMatch.includes(chinese)) {
      console.log(`✅ 中文部分 "${chinese}" 匹配成功`);
      // 中文匹配给予更高权重，因为中文通常是品牌或关键信息
      matchScore += 35;
      matchedParts++;
      
      // 如果中文部分在开头，额外加分
      if (searchMatch.startsWith(chinese)) {
        matchScore += 5;
      }
    } else {
      console.log(`❌ 中文部分 "${chinese}" 未匹配`);
    }
  });
  
  // 检查英文数字部分匹配
  englishChars.forEach((english, index) => {
    if (searchMatch.includes(english)) {
      console.log(`✅ 英文数字部分 "${english}" 匹配成功`);
      // 英文数字匹配给予标准权重
      matchScore += 30;
      matchedParts++;
      
      // 如果英文部分在末尾，额外加分（通常是型号）
      if (searchMatch.endsWith(english)) {
        matchScore += 5;
      }
    } else {
      console.log(`❌ 英文数字部分 "${english}" 未匹配`);
    }
  });
  
  console.log(`匹配结果: ${matchedParts}/${totalParts} 部分匹配，得分: ${matchScore}`);
  
  // 3. 判断匹配结果
  const matchThreshold = 0.8; // 80%的部分需要匹配
  const requiredMatches = Math.ceil(totalParts * matchThreshold);
  
  if (matchedParts >= requiredMatches && matchedParts > 0) {
    // 根据匹配完整度确定匹配类型
    let matchType = 'intelligent_partial';
    let finalScore = matchScore;
    
    if (matchedParts === totalParts) {
      // 所有部分都匹配
      matchType = 'intelligent_complete';
      finalScore = Math.min(matchScore + 10, 85); // 最高85分，低于精确匹配但高于包含匹配
    } else {
      // 部分匹配
      finalScore = Math.min(matchScore, 70); // 最高70分
    }
    
    console.log(`🎉 智能匹配成功: 类型=${matchType}, 最终得分=${finalScore}`);
    
    return {
      matched: true,
      matchType: matchType,
      score: finalScore
    };
  }
  
  console.log(`❌ 智能匹配失败: 匹配部分不足`);
  return { matched: false, matchType: 'none', score: 0 };
};

/**
 * 🆕 组合匹配检查（整合所有匹配策略）
 * @param {String} searchMatchValue 产品的searchMatch字段值
 * @param {String} keyword 搜索关键词
 * @returns {Object} 最佳匹配结果
 */
const checkCombinedMatch = (searchMatchValue, keyword) => {
  // 1. 先尝试智能匹配（包含了原有匹配逻辑）
  const intelligentResult = checkIntelligentMatch(searchMatchValue, keyword);
  
  // 2. 如果智能匹配成功，直接返回
  if (intelligentResult.matched) {
    return intelligentResult;
  }
  
  // 3. 如果智能匹配失败，尝试原有的字符串匹配作为备选
  return checkStringMatch(searchMatchValue, keyword);
};

/**
 * 构建智能搜索查询（使用searchMatch字段优化）
 * @param {Object} baseQuery 基础查询条件
 * @param {String} keyword 搜索关键词
 * @returns {Array} 搜索查询配置数组
 */
const buildIntelligentSearchQueries = (baseQuery, keyword) => {
  const queries = [];
  
  const escapedKeyword = escapeRegex(keyword);
  const normalizedKeyword = normalizeStringForSearch(keyword);
  const escapedNormalizedKeyword = escapeRegex(normalizedKeyword);
  
  // 🆕 优先使用searchMatch字段进行匹配
  queries.push(
    // searchMatch精确匹配
    {
      query: { ...baseQuery, searchMatch: { $regex: `^${escapedNormalizedKeyword}$`, $options: 'i' } },
      weight: 100,
      matchType: 'searchMatch_exact',
      keyword: keyword
    },
    // searchMatch前缀匹配
    {
      query: { ...baseQuery, searchMatch: { $regex: `^${escapedNormalizedKeyword}`, $options: 'i' } },
      weight: 90,
      matchType: 'searchMatch_prefix',
      keyword: keyword
    },
    // searchMatch包含匹配
    {
      query: { ...baseQuery, searchMatch: { $regex: escapedNormalizedKeyword, $options: 'i' } },
      weight: 80,
      matchType: 'searchMatch_contains',
      keyword: keyword
    }
  );
  
  // 🆕 添加分词匹配查询
  const chineseMatch = keyword.match(/[\u4e00-\u9fa5]+/g);
  const englishMatch = keyword.match(/[a-zA-Z0-9]+/g);
  
  if (chineseMatch && englishMatch) {
    // 构建分词查询：同时包含中文和英文部分
    const chineseRegex = chineseMatch.map(escapeRegex).join('.*');
    const englishRegex = englishMatch.map(escapeRegex).join('.*');
    
    queries.push({
      query: {
        ...baseQuery,
        $and: [
          { searchMatch: { $regex: chineseRegex, $options: 'i' } },
          { searchMatch: { $regex: englishRegex, $options: 'i' } }
        ]
      },
      weight: 85,
      matchType: 'searchMatch_split',
      keyword: keyword
    });
  }
  
  // 🆕 如果原始关键词与标准化关键词不同，也尝试用原始关键词匹配
  if (keyword.toLowerCase() !== normalizedKeyword && keyword.trim().length > 0) {
    queries.push(
      {
        query: { ...baseQuery, searchMatch: { $regex: escapedKeyword, $options: 'i' } },
        weight: 70,
        matchType: 'searchMatch_original',
        keyword: keyword
      }
    );
  }
  
  // 保留品牌名称匹配作为补充
  queries.push({
    query: { ...baseQuery, brandName: { $regex: escapedKeyword, $options: 'i' } },
    weight: 40,
    matchType: 'brand',
    keyword: keyword
  });
  
  return queries;
};

/**
 * 搜索匹配的产品名称（用于自动完成功能）
 * 增强版：支持智能字符串匹配，解决空格、标点符号等匹配问题
 * @param {String} keyword 搜索关键词
 * @param {Number} limit 返回数量限制
 * @param {String} category 产品类别筛选
 * @returns {Promise<Object>} 搜索结果
 */
const searchProductNames = async (keyword, limit = 10, category = '') => {
  const startTime = Date.now();

  try {
    // 验证输入参数
    if (!keyword || !keyword.trim()) {
      return {
        success: false,
        message: '搜索关键词不能为空'
      };
    }
    console.log("关键字是", keyword)
    const trimmedKeyword = keyword.trim();
    
    // 限制搜索关键词长度
    if (trimmedKeyword.length > 50) {
      return {
        success: false,
        message: '搜索关键词过长，请控制在50字符以内'
      };
    }

    // 限制返回数量
    const searchLimit = Math.min(Math.max(limit, 1), 20);

    // 构建基础查询条件
    const baseQuery = {
      supportsComparison: true // 只搜索支持对比的产品
    };

    // 添加类别筛选
    if (category && category.trim()) {
      baseQuery.productType = category.trim();
    }

    // 构建智能搜索查询
    const searchQueries = buildIntelligentSearchQueries(baseQuery, trimmedKeyword);

    // 执行搜索并收集产品名称
    const allProductNames = [];
    const seenNames = new Set();

    for (const searchQuery of searchQueries) {
      try {
        // 使用基础查询（移除聚合查询逻辑）
        const results = await Product.find(searchQuery.query)
          .select('skuName brandName')
          .limit(searchLimit * 2)
          .lean();

        // 提取产品名称并去重
        for (const product of results) {
          const productName = product.skuName;
          
          // 避免重复产品名称
          if (!seenNames.has(productName)) {
            seenNames.add(productName);
            
            // 使用增强的相关性得分计算
            const relevanceScore = calculateEnhancedRelevanceScore(
              productName, 
              trimmedKeyword, 
              searchQuery.weight, 
              searchQuery.matchType,
              searchQuery.keyword
            );
            
            allProductNames.push({
              name: productName,
              matchType: searchQuery.matchType,
              relevanceScore
            });
          }
        }
      } catch (queryError) {
        console.error(`产品名称搜索查询执行失败 (${searchQuery.matchType}):`, queryError);
        // 继续执行其他查询
      }
    }

    // 如果常规查询结果不足，使用客户端智能匹配作为补充
    if (allProductNames.length < searchLimit) {
      try {
        const supplementResults = await getSupplementaryMatches(
          baseQuery, 
          trimmedKeyword, 
          searchLimit * 3,
          seenNames
        );
        allProductNames.push(...supplementResults);
      } catch (supplementError) {
        console.error('补充匹配查询失败:', supplementError);
      }
    }

    // 按相关性得分排序并限制结果数量
    const sortedNames = allProductNames
      .sort((a, b) => b.relevanceScore - a.relevanceScore)
      .slice(0, searchLimit)
      .map(item => item.name);

    const searchTime = Date.now() - startTime;

    return {
      success: true,
      data: {
        productNames: sortedNames,
        total: sortedNames.length,
        searchTime,
        keyword: trimmedKeyword,
        searchStrategy: 'intelligent_matching' // 标识使用了智能匹配
      }
    };

  } catch (error) {
    console.error('产品名称搜索失败:', error);
    
    const searchTime = Date.now() - startTime;
    
    return {
      success: false,
      message: '搜索服务暂时不可用，请稍后重试',
      data: {
        productNames: [],
        total: 0,
        searchTime,
        keyword: keyword
      }
    };
  }
};

/**
 * 获取补充匹配结果（客户端智能匹配 - 使用searchMatch字段优化版）
 * 当数据库查询结果不足时，使用客户端匹配作为补充
 * @param {Object} baseQuery 基础查询条件
 * @param {String} keyword 搜索关键词
 * @param {Number} limit 查询限制
 * @param {Set} seenNames 已经找到的产品名称集合
 * @returns {Promise<Array>} 补充匹配结果
 */
const getSupplementaryMatches = async (baseQuery, keyword, limit, seenNames) => {
  // 获取更多产品进行客户端匹配，现在选择searchMatch字段
  const allProducts = await Product.find(baseQuery)
    .select('skuName brandName searchMatch')
    .limit(limit)
    .lean();

  const supplementResults = [];

  console.log(`开始补充匹配，产品数量: ${allProducts.length}, 关键词: "${keyword}"`);

  for (const product of allProducts) {
    const productName = product.skuName;
    
    // 跳过已经找到的产品
    if (seenNames.has(productName)) {
      continue;
    }

    // 🆕 使用组合匹配策略进行智能匹配检查
    const matchResult = checkCombinedMatch(product.searchMatch, keyword);
    
    if (matchResult.matched) {
      console.log(`💡 补充匹配成功: ${productName} (${matchResult.matchType}, 得分: ${matchResult.score})`);
      
      supplementResults.push({
        name: productName,
        matchType: `client_${matchResult.matchType}`,
        relevanceScore: matchResult.score,
        matchedKeyword: keyword
      });
      
      seenNames.add(productName);
    }
  }

  console.log(`补充匹配完成，新增结果: ${supplementResults.length}`);
  return supplementResults;
};

/**
 * 计算产品名称相关性得分（使用searchMatch字段优化版）
 * @param {String} productName 产品名称
 * @param {String} keyword 搜索关键词
 * @param {Number} baseWeight 基础权重
 * @param {String} matchType 匹配类型
 * @param {String} matchedKeyword 实际匹配的关键词
 * @returns {Number} 相关性得分
 */
const calculateEnhancedRelevanceScore = (productName, keyword, baseWeight, matchType, matchedKeyword = keyword) => {
  let score = baseWeight;
  
  // 🆕 对于不同的searchMatch匹配类型给予相应奖励
  if (matchType && matchType.includes('searchMatch')) {
    score += 10; // searchMatch匹配奖励
    
    // 🆕 对智能匹配给予额外奖励
    if (matchType.includes('intelligent')) {
      score += 5; // 智能分词匹配奖励
    }
    
    // 对分词匹配给予奖励
    if (matchType.includes('split')) {
      score += 8; // 分词匹配奖励
    }
  }
  
  // 标准化关键词进行匹配分析
  const normalizedKeyword = normalizeStringForSearch(matchedKeyword);
  const normalizedName = normalizeStringForSearch(productName);
  
  // 关键词在名称中的位置权重（越靠前越好）
  const keywordIndex = normalizedName.indexOf(normalizedKeyword);
  if (keywordIndex !== -1) {
    score += Math.max(0, 20 - keywordIndex * 0.5);
  }
  
  // 产品名称长度权重（较短的名称通常更相关）
  const nameLength = productName.length;
  score += Math.max(0, 100 - nameLength) * 0.1;
  
  // 关键词覆盖度（关键词占产品名称的比例）
  if (normalizedName.length > 0) {
    const coverage = normalizedKeyword.length / normalizedName.length;
    score += coverage * 10;
  }
  
  // 对标准化匹配类型给予额外奖励
  if (matchType && matchType.includes('normalized')) {
    score += 5; // 智能匹配奖励
  }
  
  return score;
};

/**
 * 转义正则表达式特殊字符
 * @param {String} str 要转义的字符串
 * @returns {String} 转义后的字符串
 */
const escapeRegex = (str) => {
  return str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
};

module.exports = {
  compareProductsByNames,
  searchProductNames,
  getCacheStats
}; 