const { result: resultApi } = require('../../utils/api');
const { PRODUCT_LINKS } = require('../../utils/product-links');
const { formatUTCToBeijing } = require('../../utils/util');
// 引入towxml库
const towxml = require('../../lib/towxml/index');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    questionId: null,
    result: null,
    loading: true,
    error: null,
    canvasWidth: 0,
    canvasHeight: 0,
    // 添加商品链接相关数据
    productLinks: null,
    showProductLinks: false,
    // 添加Markdown内容展示
    parsedSummary: null,
    // 添加结果处理状态
    processing: false,
    pollInterval: null
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    if (!options.id) {
      this.setData({
        loading: false,
        error: '缺少问题ID参数'
      });
      return;
    }

    this.setData({
      questionId: options.id
    });

    // 使用新的API替代已弃用的wx.getSystemInfoSync
    const windowInfo = wx.getWindowInfo();
    this.setData({
      canvasWidth: windowInfo.windowWidth,
      canvasHeight: windowInfo.windowHeight * 0.8
    });

    this.fetchResultData();
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function() {
    // 清除轮询定时器
    if (this.data.pollInterval) {
      clearInterval(this.data.pollInterval);
    }
  },

  /**
   * 获取问题结果数据
   */
  fetchResultData: function () {
    this.setData({ loading: true, error: null });

    // 使用异步模式请求AI分析结果
    resultApi.getQuestionResult(this.data.questionId, 'ai', true)
      .then(res => {
        console.log('API响应数据:', res); // 添加调试日志
        if (res.success) {
          // 检查是否返回处理中状态
          if (res.code === 202 && res.data.status === 'processing') {
            console.log('AI分析结果正在生成中，开始轮询...');
            // 设置处理中状态
            this.setData({
              loading: false,
              processing: true
            });
            // 开始轮询检查结果状态
            this.startPollingResultStatus();
          } else {
            console.log('获取到结果数据:', res.data); // 添加调试日志
            // 处理结果数据
            this.processResultData(res.data);
            this.setData({
              result: res.data,
              loading: false,
              processing: false
            });
            // 查找获胜选项的商品链接
            this.findProductLinks();
          }
        } else {
          this.setData({
            loading: false,
            error: res.message || '获取结果失败'
          });
        }
      })
      .catch(err => {
        console.error('API请求异常:', err); // 添加调试日志
        this.setData({
          loading: false,
          error: err.message || '网络异常，请稍后再试'
        });
      });
  },

  /**
   * 开始轮询检查结果状态
   */
  startPollingResultStatus: function() {
    // 清除可能存在的旧定时器
    if (this.data.pollInterval) {
      clearInterval(this.data.pollInterval);
    }
    
    // 创建新的轮询定时器
    const pollInterval = setInterval(() => {
      this.checkResultStatus();
    }, 3000); // 每3秒检查一次状态
    
    // 保存定时器ID
    this.setData({
      pollInterval: pollInterval
    });
  },

  /**
   * 检查结果状态
   */
  checkResultStatus: function() {
    const questionId = this.data.questionId;
    
    // 调用API检查状态
    resultApi.checkResultStatus(questionId, 'ai')
      .then(res => {
        if (res.success) {
          const statusData = res.data;
          console.log('状态检查结果:', statusData);
          
          // 如果结果已生成完成
          if (statusData.status === 'completed') {
            console.log('AI分析结果已生成完成，获取结果数据');
            // 清除轮询定时器
            clearInterval(this.data.pollInterval);
            this.setData({
              pollInterval: null
            });
            
            // 重新获取结果数据
            this.fetchCompletedResult();
          } 
          // 如果结果尚未开始处理
          else if (statusData.status === 'pending') {
            console.log('AI分析结果尚未开始处理，尝试启动处理');
            // 主动触发一次异步生成
            resultApi.getQuestionResult(this.data.questionId, 'ai', true)
              .then(triggerRes => {
                console.log('触发异步生成结果:', triggerRes);
              })
              .catch(err => {
                console.error('触发异步生成异常:', err.message);
              });
          }
          else {
            console.log('AI分析结果仍在生成中...');
          }
        } else {
          console.error('检查结果状态失败:', res.message);
        }
      })
      .catch(err => {
        console.error('检查结果状态异常:', err.message);
      });
  },

  /**
   * 获取已完成的结果数据
   */
  fetchCompletedResult: function() {
    this.setData({ loading: true, processing: false });
    
    resultApi.getQuestionResult(this.data.questionId, 'ai', false)
      .then(res => {
        console.log('已完成结果API响应:', res); // 添加调试日志
        if (res.success) {
          console.log('已完成结果数据:', res.data); // 添加调试日志
          // 处理结果数据
          this.processResultData(res.data);
          this.setData({
            result: res.data,
            loading: false
          });
          // 查找获胜选项的商品链接
          this.findProductLinks();
        } else {
          this.setData({
            loading: false,
            error: res.message || '获取结果失败'
          });
        }
      })
      .catch(err => {
        console.error('获取已完成结果异常:', err); // 添加调试日志
        this.setData({
          loading: false,
          error: err.message || '网络异常，请稍后再试'
        });
      });
  },

  /**
   * 处理结果数据，解析Markdown内容
   */
  processResultData: function(data) {
    // 格式化日期时间
    if (data) {
      data.createdAt = formatUTCToBeijing(data.createdAt);
      data.updatedAt = formatUTCToBeijing(data.updatedAt);
      data.expiryTime = formatUTCToBeijing(data.expiryTime);
    }
    
    // 解析Markdown内容 - 修复数据结构匹配问题
    let summaryContent = null;
    
    // 检查不同可能的数据结构位置
    if (data && data.summary) {
      // 直接在data.summary中（AI分析结果的标准位置）
      summaryContent = data.summary;
    } else if (data && data.aiSummary) {
      // 在data.aiSummary中（备用位置）
      summaryContent = data.aiSummary;
    } else if (data && data.baseData && data.baseData.summary) {
      // 在data.baseData.summary中（可能的缓存数据结构）
      summaryContent = data.baseData.summary;
    }
    
    // 如果找到了summary内容，则解析为towxml格式
    if (summaryContent && typeof summaryContent === 'string') {
      try {
        const parsedSummary = towxml(summaryContent, 'markdown');
        this.setData({
          parsedSummary
        });
        console.log('Markdown内容解析成功');
      } catch (error) {
        console.error('Markdown解析失败:', error);
        // 如果解析失败，设置一个错误消息
        this.setData({
          parsedSummary: towxml('# 投票分析\n\n暂无分析内容或内容解析失败', 'markdown')
        });
      }
    } else {
      console.warn('未找到有效的投票分析内容');
      // 设置默认的分析内容
      this.setData({
        parsedSummary: towxml('# 投票分析\n\n正在生成分析结果，请稍候...', 'markdown')
      });
    }
  },

  /**
   * 查找获胜选项的商品链接
   */
  findProductLinks: function() {
    const { result } = this.data;
    if (!result || !result.baseData || !result.baseData.votingTrend || result.baseData.votingTrend.length === 0) {
      return;
    }

    // 获取票数最高的选项
    const winningOption = result.baseData.winningOption;
    if (!winningOption) return;
    
    const optionContent = winningOption.content;
    
    // 在产品链接库中查找匹配项
    let productLinks = null;
    
    // 遍历所有产品，查找完全匹配或部分包含的产品
    for (const productName in PRODUCT_LINKS) {
      if (optionContent === productName || optionContent.includes(productName) || productName.includes(optionContent)) {
        productLinks = {
          productName: productName,
          links: PRODUCT_LINKS[productName]
        };
        break;
      }
    }
    
    this.setData({
      productLinks: productLinks,
      showProductLinks: !!productLinks
    });
  },

  /**
   * 复制商品链接
   */
  copyProductLink: function(e) {
    const platform = e.currentTarget.dataset.platform;
    const link = e.currentTarget.dataset.link;
    
    wx.setClipboardData({
      data: link,
      success: () => {
        wx.showToast({
          title: '链接已复制',
          icon: 'success'
        });
      },
      fail: () => {
        wx.showToast({
          title: '复制失败',
          icon: 'none'
        });
      }
    });
  },

  /**
   * 分享为图片
   */
  shareAsImage: function () {
    const that = this;
    wx.showLoading({
      title: '生成图片中...',
    });

    // 创建选择器
    const query = wx.createSelectorQuery();
    
    // 选择需要生成图片的容器
    query.select('#result-container').boundingClientRect();
    query.exec(function(res) {
      const container = res[0];
      
      // 创建canvas上下文
      wx.createSelectorQuery()
        .select('#share-canvas')
        .fields({ node: true, size: true })
        .exec((res) => {
          const canvas = res[0].node;
          const ctx = canvas.getContext('2d');
          
          // 设置canvas尺寸
          canvas.width = that.data.canvasWidth * 2; // 高清图片
          canvas.height = that.data.canvasHeight * 2;
          ctx.scale(2, 2);
          
          // 绘制白色背景
          ctx.fillStyle = '#ffffff';
          ctx.fillRect(0, 0, that.data.canvasWidth, that.data.canvasHeight);
          
          wx.canvasToTempFilePath({
            canvas: canvas,
            success: function(res) {
              wx.hideLoading();
              // 保存图片到相册
              wx.saveImageToPhotosAlbum({
                filePath: res.tempFilePath,
                success: function() {
                  wx.showToast({
                    title: '已保存到相册',
                    icon: 'success'
                  });
                },
                fail: function(err) {
                  console.error('保存图片失败', err);
                  wx.showToast({
                    title: '保存失败',
                    icon: 'none'
                  });
                }
              });
            },
            fail: function(err) {
              wx.hideLoading();
              console.error('生成图片失败', err);
              wx.showToast({
                title: '生成图片失败',
                icon: 'none'
              });
            }
          });
        });
    });
  },

  /**
   * 分享给好友
   */
  onShareAppMessage: function () {
    const { result } = this.data;
    return {
      title: `「${result.title}」的投票结果`,
      path: `/pages/result/result?id=${this.data.questionId}`,
      imageUrl: '/assets/images/share-default.png'
    };
  }
}); 