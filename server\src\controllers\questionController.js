const questionService = require('../services/questionService');
const validator = require('../utils/communityvalidator');
const { success, error } = require('../utils/response');

/**
 * @desc    创建新问题
 * @route   POST /api/v1/community/questions
 * @access  Private
 */
const createQuestion = async (req, res) => {
  try {
    // 验证请求数据
    const { error: validationError, value } = validator.createQuestionSchema.validate(req.body);
    
    if (validationError) {
      const errors = validationError.details.map(detail => ({
        field: detail.path[0],
        message: detail.message
      }));
      
      return error(res, 400, '请求参数错误', errors[0].message);
    }

    // 创建问题
    const question = await questionService.createQuestion(
      value,
      req.user.id
    );

    return success(res, 201, '问题创建成功', {
      id: question._id,
      title: question.title,
      createdAt: question.createdAt
    });
  } catch (err) {
    // 处理内容审核错误
    if (err.message && err.message.includes('内容审核失败')) {
      return error(res, 400, err.message);
    }
    
    console.error('创建问题控制器错误:', err);
    return error(res, 500, err.message);
  }
};

/**
 * @desc    获取问题列表
 * @route   GET /api/v1/community/questions
 * @access  Private
 */
const getQuestions = async (req, res) => {
  try {
    console.log("获取问题列表请求参数",req.query);
    // 验证请求数据
    const { error: validationError, value } = validator.getQuestionsSchema.validate(req.query);
    
    if (validationError) {
      const errors = validationError.details.map(detail => ({
        field: detail.path[0],
        message: detail.message
      }));
      
      return error(res, 400, '请求参数错误', errors[0].message);
    }

    // 构建过滤条件
    const filters = {};
    if (value.status) {
      filters.status = value.status;
    }
    if (value.userId) {
      filters.userId = value.userId;
    }
    if (value.tags) {
      filters.tags = Array.isArray(value.tags) ? value.tags : [value.tags];
    }

    // 获取问题列表
    const result = await questionService.getQuestions(
      filters,
      value.page,
      value.limit,
      value.sortBy
    );

    return success(res, 200, '获取问题列表成功', result);
  } catch (err) {
    return error(res, 500, err.message);
  }
};

/**
 * @desc    根据ID获取问题详情
 * @route   GET /api/v1/community/questions/:id
 * @access  Public (支持未登录用户)
 */
const getQuestionById = async (req, res) => {
  try {
    const questionId = req.params.id;
    // 支持未登录用户访问，如果未登录则userId为null
    const userId = req.user ? req.user.id : null;

    const question = await questionService.getQuestionById(questionId, userId);
    return success(res, 200, '获取问题详情成功', question);
  } catch (err) {
    // 处理特定错误
    if (err.message === '问题不存在' || err.message === '无效的问题ID') {
      return error(res, 404, err.message);
    }
    return error(res, 500, err.message);
  }
};

/**
 * @desc    更新问题
 * @route   PUT /api/v1/community/questions/:id
 * @access  Private
 */
const updateQuestion = async (req, res) => {
  try {
    // 验证请求数据
    const { error: validationError, value } = validator.updateQuestionSchema.validate(req.body);
    
    if (validationError) {
      const errors = validationError.details.map(detail => ({
        field: detail.path[0],
        message: detail.message
      }));
      
      return error(res, 400, '请求参数错误', errors[0].message);
    }

    const questionId = req.params.id;
    const userId = req.user.id;

    // 更新问题
    await questionService.updateQuestion(questionId, value, userId);
    return success(res, 200, '问题更新成功');
  } catch (err) {
    // 处理特定错误
    if (err.message === '问题不存在' || err.message === '无效的问题ID') {
      return error(res, 404, err.message);
    }
    if (err.message === '没有权限修改此问题') {
      return error(res, 403, err.message);
    }
    if (err.message === '已有人回答的问题不能修改') {
      return error(res, 400, err.message);
    }
    return error(res, 500, err.message);
  }
};

/**
 * @desc    删除问题
 * @route   DELETE /api/v1/community/questions/:id
 * @access  Private
 */
const deleteQuestion = async (req, res) => {
  try {
    const questionId = req.params.id;
    const userId = req.user.id;

    // 删除问题
    await questionService.deleteQuestion(questionId, userId);
    return success(res, 200, '问题删除成功');
  } catch (err) {
    // 处理特定错误
    if (err.message === '问题不存在' || err.message === '无效的问题ID') {
      return error(res, 404, err.message);
    }
    if (err.message === '没有权限删除此问题') {
      return error(res, 403, err.message);
    }
    console.error('删除问题错误:', err);
    return error(res, 500, '删除问题失败: ' + err.message);
  }
};

/**
 * @desc    关闭问题
 * @route   PUT /api/v1/community/questions/:id/close
 * @access  Private
 */
const closeQuestion = async (req, res) => {
  try {
    const questionId = req.params.id;
    const userId = req.user.id;

    // 关闭问题
    await questionService.closeQuestion(questionId, userId);
    return success(res, 200, '问题已成功关闭');
  } catch (err) {
    // 处理特定错误
    if (err.message === '问题不存在' || err.message === '无效的问题ID') {
      return error(res, 404, err.message);
    }
    if (err.message === '没有权限关闭此问题') {
      return error(res, 403, err.message);
    }
    if (err.message === '问题已经关闭') {
      return error(res, 400, err.message);
    }
    console.error('关闭问题错误:', err);
    return error(res, 500, '关闭问题失败: ' + err.message);
  }
};

/**
 * @desc    关闭所有过期问题
 * @route   POST /api/v1/community/questions/close-expired
 * @access  Admin
 */
const closeExpiredQuestions = async (req, res) => {
  try {
    // 这里可以添加管理员权限验证
    // if (!req.user.isAdmin) {
    //   return error(res, 403, '没有权限执行此操作');
    // }

    const result = await questionService.closeAllExpiredQuestions();
    return success(res, 200, result.message, { closedCount: result.closedCount });
  } catch (err) {
    console.error('关闭过期问题错误:', err);
    return error(res, 500, '关闭过期问题失败: ' + err.message);
  }
};

/**
 * @desc    搜索问题
 * @route   GET /api/v1/community/questions/search
 * @access  Private
 */
const searchQuestions = async (req, res) => {
  try {
    console.log("搜索问题请求参数", req.query);
    
    // 参数验证
    const { error: validationError, value } = validator.searchQuestionsSchema.validate(req.query);
    
    if (validationError) {
      const errors = validationError.details.map(detail => ({
        field: detail.path[0],
        message: detail.message
      }));
      
      return error(res, 400, '请求参数错误', errors[0].message);
    }

    // 构建过滤条件
    const filters = {};
    if (value.tags) {
      filters.tags = Array.isArray(value.tags) ? value.tags : [value.tags];
    }
    if (value.status) {
      filters.status = value.status;
    }

    // 执行搜索
    const result = await questionService.searchQuestions(
      value.keyword,
      filters,
      value.page || 1,
      value.limit || 10
    );

    return success(res, 200, '搜索问题成功', result);
  } catch (err) {
    console.error('搜索问题错误:', err);
    return error(res, 500, '搜索问题失败: ' + err.message);
  }
};

/**
 * @desc    根据问题ID对比问题下的产品选项
 * @route   POST /api/v1/community/questions/:id/compare
 * @access  Private
 */
const compareQuestionProducts = async (req, res) => {
  try {
    const questionId = req.params.id;
    
    // 验证请求参数
    const { error: validationError } = validator.compareQuestionProductsSchema.validate({ questionId });
    
    if (validationError) {
      const errors = validationError.details.map(detail => ({
        field: detail.path[0],
        message: detail.message
      }));
      
      return error(res, 400, '请求参数错误', errors[0].message);
    }

    // 调用问题服务层进行问题产品对比
    const result = await questionService.compareQuestionProducts(questionId);

    if (!result.success) {
      return error(res, 400, result.message, result.data);
    }

    // 返回成功响应
    const responseData = {
      productType: result.data.productType,
      products: result.data.products,
      comparisonTable: result.data.comparison,
      aiAnalysis: result.data.aiAnalysis,
      meta: {
        questionId,
        foundProductsCount: result.data.products.length,
        notFoundProducts: result.data.notFoundProducts || [],
        fromCache: result.data.fromCache || false
      }
    };

    return success(res, 200, '产品对比成功', responseData);

  } catch (err) {
    console.error('问题产品对比控制器错误:', err);
    
    // 处理特定错误
    if (err.message.includes('问题不存在')) {
      return error(res, 404, '指定的问题不存在或已被删除');
    }
    
    if (err.message.includes('问题选项不足') || err.message.includes('选项不足')) {
      return error(res, 400, '该问题的选项数量不足，无法进行对比');
    }
    
    if (err.message.includes('找到的可对比产品数量不足')) {
      return error(res, 400, '该问题下找到的可对比产品数量不足');
    }
    
    if (err.message.includes('只能对比相同类型的产品')) {
      return error(res, 400, '该问题下的产品类型不一致，无法进行对比');
    }
    
    if (err.message.includes('AI')) {
      return error(res, 503, 'AI分析服务暂时不可用，请稍后重试');
    }

    return error(res, 500, '服务器内部错误，请稍后重试');
  }
};

/**
 * @desc    AI推荐产品选项
 * @route   POST /api/v1/community/questions/ai-recommend
 * @access  Private (需要用户认证)
 */
const aiRecommendProducts = async (req, res) => {
  try {
    console.log('AI推荐产品选项请求参数:', req.body);
    console.log('当前用户ID:', req.user?.id);
    
    // 获取异步处理参数
    const { async } = req.query;
    
    // 验证请求数据
    const { error: validationError, value } = validator.aiRecommendProductsSchema.validate(req.body);
    
    if (validationError) {
      const errors = validationError.details.map(detail => ({
        field: detail.path[0],
        message: detail.message
      }));
      
      return error(res, 400, '请求参数错误', errors[0].message);
    }

    // 获取当前登录用户ID
    const userId = req.user?.id;

    // 如果是异步模式，启动后台处理并立即返回处理中状态
    if (async === 'true' || async === true) {
      // 生成唯一的推荐任务ID（基于用户ID）
      const taskId = `ai_recommend_${userId}_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
      
      // 启动后台异步处理，传递用户ID
      questionService.aiRecommendProductsAsync(
        value.questionInfo,
        value.filterOptions,
        userId, // 传递当前用户ID
        taskId
      ).catch(err => {
        console.error('异步AI推荐处理失败:', err);
      });
      
      // 立即返回处理中状态
      return success(res, 202, 'AI推荐正在生成中', { 
        status: 'processing',
        taskId: taskId,
        message: '正在为您智能推荐产品，请稍候...'
      });
    }

    // 同步模式：调用问题服务层进行AI产品推荐（传递用户ID）
    const result = await questionService.aiRecommendProducts(
      value.questionInfo,
      value.filterOptions,
      userId // 传递当前用户ID
    );

    if (!result.success) {
      return error(res, 400, result.message, result.data);
    }

    // 返回成功响应
    const responseData = {
      recommendedProducts: result.data.products,
      aiAnalysis: result.data.aiAnalysis,
      filterInfo: result.data.filterInfo,
      meta: {
        totalCandidates: result.data.totalCandidates,
        recommendedCount: result.data.products.length,
        processingTime: result.data.processingTime
      }
    };

    console.log('AI推荐产品选项控制器返回结果:', responseData);

    return success(res, 200, 'AI产品推荐成功', responseData);

  } catch (err) {
    console.error('AI推荐产品选项控制器错误:', err);
    
    // 处理特定错误
    if (err.message.includes('产品类型')) {
      return error(res, 400, '请先选择产品类型（手机或电脑）');
    }
    
    if (err.message.includes('筛选条件')) {
      return error(res, 400, '筛选条件不足，请选择至少一个品牌');
    }
    
    if (err.message.includes('找不到符合条件的产品')) {
      return error(res, 400, '未找到符合条件的产品，请调整筛选条件');
    }
    
    if (err.message.includes('AI')) {
      return error(res, 503, 'AI推荐服务暂时不可用，请稍后重试');
    }

    return error(res, 500, '服务器内部错误，请稍后重试');
  }
};

/**
 * @desc    检查AI推荐任务状态
 * @route   GET /api/v1/community/questions/ai-recommend/status/:taskId
 * @access  Private (需要用户认证)
 */
const checkAIRecommendStatus = async (req, res) => {
  try {
    const { taskId } = req.params;
    
    console.log('检查AI推荐任务状态, 任务ID:', taskId);
    console.log('当前用户ID:', req.user?.id);
    
    // 检查任务状态
    const status = await questionService.getAIRecommendTaskStatus(taskId);
    
    return success(res, 200, '获取任务状态成功', status);
    
  } catch (err) {
    console.error('检查AI推荐任务状态失败:', err);
    return error(res, 500, '获取任务状态失败');
  }
};

module.exports = {
  createQuestion,
  getQuestions,
  getQuestionById,
  updateQuestion,
  deleteQuestion,
  closeQuestion,
  closeExpiredQuestions,
  searchQuestions,
  compareQuestionProducts,
  aiRecommendProducts,
  checkAIRecommendStatus
};