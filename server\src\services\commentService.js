const Comment = require('../models/Comment');
const Answer = require('../models/Answer');
const Question = require('../models/Question');
const mongoose = require('mongoose');
const notificationService = require('./notificationService');
const socketService = require('./socketService');
const { auditCommentContent } = require('../utils/contentAuditUtils');

/**
 * 创建评论
 * @param {String} answerId 回答ID
 * @param {String} content 评论内容
 * @param {Boolean} isAnonymous 是否匿名
 * @param {String} parentId 父评论ID (可选)
 * @param {String} userId 用户ID
 * @returns {Promise<Object>} 创建的评论
 */
const createComment = async (answerId, content, isAnonymous, parentId, userId) => {
  // 验证ObjectId有效性
  if (!mongoose.Types.ObjectId.isValid(answerId)) {
    throw new Error('无效的回答ID');
  }
  
  if (parentId && !mongoose.Types.ObjectId.isValid(parentId)) {
    throw new Error('无效的父评论ID');
  }

  // 内容审核 - 对评论内容进行审核
  if (content && content.trim()) {
    try {
      const auditResult = auditCommentContent(content);
      
      // 如果审核未通过，抛出错误
      if (!auditResult.passed) {
        const errorMessage = auditResult.violations.length > 0 
          ? auditResult.violations[0].message 
          : '内容审核未通过';
        
        throw new Error(`内容审核失败: ${errorMessage}`);
      }
    } catch (error) {
      console.error('评论内容审核过程中发生错误:', error);
      throw error;
    }
  } else {
    throw new Error('评论内容不能为空');
  }

  // 获取回答信息
  const answer = await Answer.findById(answerId).populate('userId');
  if (!answer) {
    throw new Error('回答不存在');
  }

  // 获取问题信息
  const question = await Question.findById(answer.questionId);
  if (!question) {
    throw new Error('问题不存在');
  }

  // 设置相关信息
  const commentData = {
    questionId: answer.questionId,
    answerId,
    userId,
    content,
    isAnonymous
  };

  // 如果是回复评论，设置父评论ID和目标用户ID
  let targetUserId = null;
  let notificationType = 'answer_commented';
  let itemType = 'answer';
  let itemId = answerId;
  let topCommentId = null;
  
  if (parentId) {
    const parentComment = await Comment.findById(parentId).populate('userId');
    if (!parentComment) {
      throw new Error('父评论不存在');
    }

    console.log('父评论:', parentComment);
    
    commentData.parentId = parentId;
    commentData.targetUserId = parentComment.userId._id;
    targetUserId = parentComment.userId._id;
    notificationType = 'comment_replied';
    itemType = 'comment';
    itemId = parentId;

    // 设置顶级评论ID
    if (parentComment.parentId === null) {
      // 如果父评论是顶级评论，则设置顶级评论ID为父评论ID
      topCommentId = parentId;
    } else {
      // 如果父评论不是顶级评论，则继承父评论的顶级评论ID
      topCommentId = parentComment.topCommentId;
    }
    
    commentData.topCommentId = topCommentId;

    console.log('目标用户ID:', commentData.targetUserId);
  } else {
    // 如果是直接评论回答，目标用户是回答者
    targetUserId = answer.userId._id;
  }

  // 创建评论
  const comment = new Comment(commentData);
  await comment.save();
  
  // 如果是顶级评论，设置其topCommentId为自己
  if (!parentId) {
    await Comment.findByIdAndUpdate(comment._id, {
      topCommentId: comment._id
    });
    // 更新comment对象，以便返回正确的数据
    comment.topCommentId = comment._id;
  }
  
  // 发送通知（如果评论者不是目标用户）
  if (targetUserId && targetUserId.toString() !== userId.toString()) {
    try {
      // 获取评论者信息
      const User = mongoose.model('User');
      const commenter = await User.findById(userId).select('nickname');
      
      // 构建通知内容
      let username = commenter ? commenter.nickname : "匿名用户";
      if (isAnonymous) {
        username = "匿名用户";
      }
      
      // 获取问题标题并截取
      const shortTitle = question.title.length > 15 ? `${question.title.substring(0, 15)}...` : question.title;
      
      // 截取评论内容
      const shortContent = content.length > 20 ? `${content.substring(0, 20)}...` : content;
      
      // 根据类型生成通知内容
      let notificationContent = '';
      if (notificationType === 'answer_commented') {
        notificationContent = `${username} 在问题"${shortTitle}"评论了您的回答："${shortContent}"`;
      } else {
        notificationContent = `${username} 在问题"${shortTitle}"回复了您的评论："${shortContent}"`;
      }

      // 创建通知
      const notification = await notificationService.createNotification(
        targetUserId,
        userId,
        notificationType,
        notificationContent,
        itemId,
        itemType,
        {
          commentId: comment._id  // 添加评论ID到metadata
        }
      );
      
      // 发送实时通知
      socketService.sendNotification(targetUserId, notification);
    } catch (error) {
      console.error('发送评论通知失败:', error);
    }
  }
  
  return comment;
};

/**
 * 获取回答的评论列表
 * @param {String} answerId 回答ID
 * @param {Number} page 页码
 * @param {Number} limit 每页数量
 * @returns {Promise<Object>} 评论列表和分页信息
 */
const getComments = async (answerId, page = 1, limit = 20) => {
  // 验证ObjectId有效性
  if (!mongoose.Types.ObjectId.isValid(answerId)) {
    throw new Error('无效的回答ID');
  }

  // 获取顶级评论总数 (包括已删除的评论)
  const total = await Comment.countDocuments({
    answerId,
    parentId: null
  });

  // 获取顶级评论 (包括已删除的评论)
  const topLevelComments = await Comment.find({
    answerId,
    parentId: null
  })
    .sort({ createdAt: -1 })
    .skip((page - 1) * limit)
    .limit(limit)
    .populate({
      path: 'userId',
      select: '_id nickname avatar',
      options: { lean: true }
    })
    .lean();
  
  // 获取每个顶级评论的所有未删除回复数量
  const commentIds = topLevelComments.map(comment => comment._id);
  const replyCounts = await Promise.all(
    commentIds.map(async commentId => {
      return await Comment.countDocuments({
        answerId,
        topCommentId: commentId,
        _id: { $ne: commentId }
      });
    })
  );

  // 将回复数量添加到相应的顶级评论中
  const commentsWithReplyCounts = topLevelComments.map((comment, index) => {
    const isDeleted = comment.isDeleted;
    return {
      id: comment._id,
      content: isDeleted ? '该评论已删除' : comment.content,
      isAnonymous: comment.isAnonymous,
      createdAt: comment.createdAt,
      isDeleted: isDeleted,
      user: (comment.isAnonymous || isDeleted) ? 
        { id: null, nickname: '用户', avatar: '/assets/images/default-avatar.png' } : 
        {
          id: comment.userId._id,
          nickname: comment.userId.nickname,
          avatar: comment.userId.avatar
        },
      replyCount: replyCounts[index] || 0,
      replies: []
    };
  });

  // 计算总页数
  const pages = Math.ceil(total / limit);

  return {
    comments: commentsWithReplyCounts,
    pagination: {
      total,
      page,
      limit,
      pages
    }
  };
};

/**
 * 删除评论
 * @param {String} commentId 评论ID
 * @param {String} userId 用户ID
 * @returns {Promise<Boolean>} 删除结果
 */
const deleteComment = async (commentId, userId) => {
  try {
    // 验证ObjectId有效性
    if (!mongoose.Types.ObjectId.isValid(commentId)) {
      throw new Error('无效的评论ID');
    }

    // 查找评论
    const comment = await Comment.findById(commentId);
    if (!comment) {
      throw new Error('评论不存在');
    }

    // 验证用户权限
    if (comment.userId.toString() !== userId) {
      throw new Error('没有权限删除此评论');
    }

    // 如果评论已经被删除
    if (comment.isDeleted) {
      throw new Error('评论已被删除');
    }

    // 软删除评论
    await Comment.findByIdAndUpdate(
      commentId,
      {
        isDeleted: true,
        deleteReason: 'user_delete',
        deletedAt: new Date()
      }
    );

    // 更新所有相关子评论的状态
    const updatePromises = [
      // 更新直接回复此评论的子评论
      Comment.updateMany(
        { parentId: commentId },
        { 
          $set: { 
            parentDeleted: true 
          }
        }
      )
    ];

    // 如果是顶级评论，还需要更新所有子评论的显示状态
    if (!comment.parentId) {
      updatePromises.push(
        Comment.updateMany(
          { topCommentId: commentId },
          { 
            $set: { 
              parentDeleted: true 
            }
          }
        )
      );
    }

    // 并行执行所有更新操作
    await Promise.all(updatePromises);

    // 更新回答的评论计数
    await Answer.findByIdAndUpdate(
      comment.answerId,
      { $inc: { commentCount: -1 } }
    );

    // 如果是顶级评论，同时更新问题的评论计数
    if (!comment.parentId) {
      await Question.findByIdAndUpdate(
        comment.questionId,
        { $inc: { commentCount: -1 } }
      );
    }

    return true;
  } catch (error) {
    console.error('删除评论失败:', error);
    throw error;
  }
};

/**
 * 获取评论的回复列表
 * @param {String} commentId 评论ID
 * @param {Number} page 页码
 * @param {Number} limit 每页数量
 * @param {Boolean} isTopLevel 是否是获取顶级评论的所有回复
 * @returns {Promise<Object>} 回复列表和分页信息
 */
const getReplies = async (commentId, page = 1, limit = 10, isTopLevel = false) => {
  // 验证ObjectId有效性
  if (!mongoose.Types.ObjectId.isValid(commentId)) {
    throw new Error('无效的评论ID');
  }

  // 查找评论
  const parentComment = await Comment.findById(commentId);
  if (!parentComment) {
    throw new Error('评论不存在');
  }

  // 查询条件
  let query;
  if (isTopLevel) {
    query = {
      topCommentId: commentId,
      _id: { $ne: commentId }
    };
  } else {
    query = {
      parentId: commentId
    };
  }

  // 获取回复总数
  const total = await Comment.countDocuments(query);

  // 获取回复列表
  const replies = await Comment.find(query)
    .sort({ createdAt: 1 })
    .skip((page - 1) * limit)
    .limit(limit)
    .populate({
      path: 'userId',
      select: '_id nickname avatar',
      options: { lean: true }
    })
    .populate({
      path: 'targetUserId',
      select: '_id nickname',
      options: { lean: true }
    })
    .lean();

  // 格式化回复
  const formattedReplies = replies.map(reply => ({
    id: reply._id,
    content: reply.isDeleted ? '该评论已删除' : reply.content,
    isAnonymous: reply.isAnonymous,
    createdAt: reply.createdAt,
    isDeleted: reply.isDeleted,
    parentDeleted: reply.parentDeleted,
    user: (reply.isAnonymous || reply.isDeleted) ? 
      { id: null, nickname: '用户', avatar: '/assets/images/default-avatar.png' } : 
      {
        id: reply.userId._id,
        nickname: reply.userId.nickname,
        avatar: reply.userId.avatar
      },
    targetUser: (reply.targetUserId && !reply.isDeleted) ? {
      id: reply.targetUserId._id,
      nickname: reply.targetUserId.nickname
    } : null
  }));

  // 计算总页数
  const pages = Math.ceil(total / limit);

  return {
    replies: formattedReplies,
    pagination: {
      total,
      page,
      limit,
      pages
    }
  };
};

/**
 * 获取评论信息
 * @param {String} commentId 评论ID
 * @returns {Promise<Object>} 评论信息
 */
const getCommentInfo = async (commentId) => {
  // 验证ObjectId有效性
  if (!mongoose.Types.ObjectId.isValid(commentId)) {
    throw new Error('无效的评论ID');
  }

  // 查找评论
  const comment = await Comment.findById(commentId);
  if (!comment) {
    throw new Error('评论不存在');
  }

  // 返回简化的评论信息，主要包含问题ID和回答ID
  return {
    id: comment._id,
    questionId: comment.questionId,
    answerId: comment.answerId,
    parentId: comment.parentId,
    topCommentId: comment.topCommentId
  };
};

module.exports = {
  createComment,
  getComments,
  deleteComment,
  getReplies,
  getCommentInfo
}; 