const mongoose = require('mongoose');

const UserRelationSchema = new mongoose.Schema(
  {
    follower: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    following: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true
    },
    status: {
      type: String,
      enum: ['active', 'blocked'],
      default: 'active'
    },
    interactionFrequency: {
      type: Number,
      default: 0
    },
    intimacyScore: {
      type: Number,
      default: 0,
      min: 0,
      max: 100
    }
  },
  {
    timestamps: true
  }
);

// 确保follower和following组合的唯一性
UserRelationSchema.index({ follower: 1, following: 1 }, { unique: true });

module.exports = mongoose.model('UserRelation', UserRelationSchema); 