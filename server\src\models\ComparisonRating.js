const mongoose = require('mongoose');

/**
 * 用户对AI产品对比结果评分模型
 * 用于存储用户对AI分析结果的评分数据
 */
const ComparisonRatingSchema = new mongoose.Schema(
  {
    // 关联的产品对比缓存ID
    comparisonCacheId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'ProductComparisonV4Cache',
      required: true,
      index: true
    },
    
    // 评分用户ID
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
      index: true
    },
    
    // 用户评分 (0-5星)
    rating: {
      type: Number,
      required: true,
      min: [0, '评分不能低于0星'],
      max: [5, '评分不能超过5星'],
      validate: {
        validator: function(v) {
          // 允许0.5的增量，即0, 0.5, 1, 1.5, 2, 2.5, 3, 3.5, 4, 4.5, 5
          return v % 0.5 === 0;
        },
        message: '评分必须是0.5的倍数'
      }
    },
    
    // 对比产品名称列表（冗余存储，便于查询和统计）
    productNames: [{
      type: String,
      required: true
    }],
    
    // 产品类别（冗余存储，便于分类统计）
    productCategory: {
      type: String,
      required: true
    }
  },
  {
    timestamps: true
  }
);

// 复合索引：确保同一用户对同一对比结果只能评分一次
ComparisonRatingSchema.index(
  { comparisonCacheId: 1, userId: 1 }, 
  { unique: true }
);

// 索引优化
ComparisonRatingSchema.index({ productCategory: 1 }); // 按产品类别查询
ComparisonRatingSchema.index({ rating: 1 }); // 按评分查询
ComparisonRatingSchema.index({ createdAt: -1 }); // 按时间排序

// 静态方法：获取指定对比结果的平均评分
ComparisonRatingSchema.statics.getAverageRating = async function(comparisonCacheId) {
  // 确保comparisonCacheId是ObjectId类型
  const objectId = comparisonCacheId instanceof mongoose.Types.ObjectId ? 
    comparisonCacheId : new mongoose.Types.ObjectId(comparisonCacheId);
  
  const result = await this.aggregate([
    { $match: { comparisonCacheId: objectId } },
    {
      $group: {
        _id: null,
        averageRating: { $avg: '$rating' },
        totalRatings: { $sum: 1 },
        ratingDistribution: {
          $push: '$rating'
        }
      }
    }
  ]);
  
  if (result.length === 0) {
    return {
      averageRating: 0,
      totalRatings: 0,
      ratingDistribution: {}
    };
  }
  
  const data = result[0];
  
  // 计算评分分布
  const distribution = {};
  for (let i = 0; i <= 5; i += 0.5) {
    distribution[i] = 0;
  }
  
  data.ratingDistribution.forEach(rating => {
    distribution[rating] = (distribution[rating] || 0) + 1;
  });
  
  return {
    averageRating: Math.round(data.averageRating * 10) / 10, // 保留1位小数
    totalRatings: data.totalRatings,
    ratingDistribution: distribution
  };
};

// 静态方法：检查用户是否已对指定对比结果评分
ComparisonRatingSchema.statics.hasUserRated = async function(comparisonCacheId, userId) {
  // 确保ID是ObjectId类型
  const cacheObjectId = comparisonCacheId instanceof mongoose.Types.ObjectId ? 
    comparisonCacheId : new mongoose.Types.ObjectId(comparisonCacheId);
  const userObjectId = userId instanceof mongoose.Types.ObjectId ? 
    userId : new mongoose.Types.ObjectId(userId);
  
  const rating = await this.findOne({
    comparisonCacheId: cacheObjectId,
    userId: userObjectId
  });
  
  return rating !== null;
};

// 静态方法：获取用户对指定对比结果的评分
ComparisonRatingSchema.statics.getUserRating = async function(comparisonCacheId, userId) {
  // 确保ID是ObjectId类型
  const cacheObjectId = comparisonCacheId instanceof mongoose.Types.ObjectId ? 
    comparisonCacheId : new mongoose.Types.ObjectId(comparisonCacheId);
  const userObjectId = userId instanceof mongoose.Types.ObjectId ? 
    userId : new mongoose.Types.ObjectId(userId);
  
  return await this.findOne({
    comparisonCacheId: cacheObjectId,
    userId: userObjectId
  }).select('rating createdAt');
};

// 实例方法：更新评分
ComparisonRatingSchema.methods.updateRating = async function(newRating) {
  this.rating = newRating;
  this.updatedAt = new Date();
  return await this.save();
};

module.exports = mongoose.model('ComparisonRating', ComparisonRatingSchema);