/**
 * 智能搜索产品测试脚本
 * 测试关键字：苹果16
 * 对应后端路由：GET /api/v1/products/search-products
 * 使用方法：node product-search-test.js
 */

const fs = require('fs');
const path = require('path');

// 配置后端服务器地址
const BASE_URL = 'http://localhost:5000/api/v1';

/**
 * 保存数据到JSON文件
 */
function saveToJsonFile(data, filename = 'product-search-result.json') {
  const filePath = path.join(__dirname, filename);
  const jsonData = JSON.stringify(data, null, 2);

  try {
    fs.writeFileSync(filePath, jsonData, 'utf8');
    console.log(`✅ 数据已保存到: ${filePath}`);
  } catch (error) {
    console.error('❌ 保存文件失败:', error);
  }
}

/**
 * 发送HTTP GET请求 (Node.js环境)
 */
function sendGetRequest(url) {
  return new Promise((resolve, reject) => {
    const https = require('https');
    const http = require('http');
    const urlObj = new URL(url);

    const options = {
      hostname: urlObj.hostname,
      port: urlObj.port,
      path: urlObj.pathname + urlObj.search,
      method: 'GET',
      headers: {
        'Content-Type': 'application/json'
      }
    };

    const protocol = urlObj.protocol === 'https:' ? https : http;

    const req = protocol.request(options, (res) => {
      let responseData = '';

      res.on('data', (chunk) => {
        responseData += chunk;
      });

      res.on('end', () => {
        try {
          const jsonData = JSON.parse(responseData);
          resolve(jsonData);
        } catch (error) {
          reject(new Error('响应数据解析失败: ' + error.message));
        }
      });
    });

    req.on('error', (error) => {
      reject(error);
    });

    req.end();
  });
}

/**
 * 执行智能搜索产品测试
 */
async function testProductSearch() {
  console.log('🧪 开始测试智能搜索产品接口...');
  console.log('🔍 测试关键字: 苹果16');

  // 构建查询参数
  const params = new URLSearchParams({
    keyword: '苹果16',
    limit: 10
  });

  const apiUrl = `${BASE_URL}/products/search-products?${params.toString()}`;

  try {
    console.log('📡 正在发送请求...');
    console.log('🌐 请求URL:', apiUrl);
    
    const response = await sendGetRequest(apiUrl);

    console.log('✅ 请求成功!');
    console.log('📊 响应数据:', response);

    // 保存后端返回的原始数据
    saveToJsonFile(response);

    // 显示搜索结果摘要
    if (response.success && response.data && response.data.products) {
      console.log(`\n📱 找到 ${response.data.products.length} 个产品:`);
      response.data.products.forEach((product, index) => {
        console.log(`${index + 1}. ${product.name} (相关性: ${product.relevanceScore})`);
      });
      console.log(`\n⏱️ 搜索耗时: ${response.data.searchTime}ms`);
      console.log(`🔍 搜索策略: ${response.data.searchStrategy}`);
    }

    console.log('🎉 测试完成，数据已保存!');

  } catch (error) {
    console.error('❌ 测试失败:', error);
    saveToJsonFile({ error: error.message }, 'product-search-error.json');
  }
}

/**
 * 运行测试
 */
function runTest() {
  testProductSearch();
}

// 如果直接运行此脚本文件，自动执行测试
if (require.main === module) {
  console.log('🚀 启动智能搜索产品测试...');
  runTest();
}

// 导出函数
module.exports = {
  testProductSearch,
  runTest
};