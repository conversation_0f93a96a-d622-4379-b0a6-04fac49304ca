const comparisonRatingService = require('../../services/user/comparisonRatingService');
const { success, error } = require('../../utils/response');

/**
 * @desc    用户对AI产品对比结果进行评分
 * @route   POST /api/v1/users/comparison-ratings
 * @access  Private
 */
const rateComparison = async (req, res) => {
  try {
    const userId = req.user.id;
    const { comparisonCacheId, rating } = req.body;

    // 调用服务层进行评分
    const result = await comparisonRatingService.rateComparison(
      userId,
      comparisonCacheId,
      rating
    );

    // 根据服务层返回结果响应
    if (result.success) {
      return success(res, 201, result.data.message, result.data);
    } else {
      // 根据错误类型返回不同的HTTP状态码
      let statusCode = 400;
      
      if (result.error.includes('不存在')) {
        statusCode = 404;
      } else if (result.error.includes('已被禁用')) {
        statusCode = 403;
      } else if (result.error.includes('已经评分过了')) {
        statusCode = 409; // Conflict
      }
      
      return error(res, statusCode, result.error);
    }
  } catch (err) {
    console.error('评分控制器错误:', err);
    return error(res, 500, '服务器内部错误，请稍后重试');
  }
};

module.exports = {
  rateComparison
};
