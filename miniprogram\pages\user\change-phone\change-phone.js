const app = getApp();
const { user, auth } = require('../../../utils/api');
const util = require('../../../utils/util');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 表单数据
    formData: {
      newPhone: '',
      verifyCode: ''
    },
    // 错误信息
    errors: {},
    // 倒计时状态
    countdown: 0,
    countdownTimer: null,
    // 提交状态
    isSubmitting: false,
    // 发送验证码状态
    isSendingCode: false,
    // 当前用户手机号
    currentPhone: ''
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.initializeData();
  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload: function () {
    // 清除定时器
    if (this.data.countdownTimer) {
      clearInterval(this.data.countdownTimer);
    }
  },

  /**
   * 初始化数据
   */
  initializeData: function() {
    const userInfo = app.globalData.userInfo;
    if (userInfo && userInfo.phone) {
      this.setData({
        currentPhone: userInfo.phone
      });
    }
  },

  /**
   * 输入框内容变化处理
   */
  onInputChange: function(e) {
    const { field } = e.currentTarget.dataset;
    const { value } = e.detail;
    
    this.setData({
      [`formData.${field}`]: value,
      [`errors.${field}`]: '' // 清除该字段的错误信息
    });
  },

  /**
   * 验证手机号格式
   */
  validatePhone: function(phone) {
    const phoneRegex = /^1[3-9]\d{9}$/;
    return phoneRegex.test(phone);
  },

  /**
   * 验证验证码格式
   */
  validateVerifyCode: function(code) {
    return /^\d{6}$/.test(code);
  },

  /**
   * 表单验证
   */
  validateForm: function(validateCodeRequired = false) {
    const { formData, currentPhone } = this.data;
    const errors = {};

    // 验证新手机号
    if (!formData.newPhone.trim()) {
      errors.newPhone = '请输入新手机号';
    } else if (!this.validatePhone(formData.newPhone.trim())) {
      errors.newPhone = '请输入有效的手机号';
    } else if (formData.newPhone.trim() === currentPhone) {
      errors.newPhone = '新手机号不能与当前手机号相同';
    }

    // 如果需要验证验证码
    if (validateCodeRequired) {
      if (!formData.verifyCode.trim()) {
        errors.verifyCode = '请输入验证码';
      } else if (!this.validateVerifyCode(formData.verifyCode.trim())) {
        errors.verifyCode = '验证码必须是6位数字';
      }
    }

    this.setData({ errors });
    return Object.keys(errors).length === 0;
  },

  /**
   * 发送验证码
   */
  sendVerifyCode: function() {
    // 验证手机号
    if (!this.validateForm(false)) {
      return;
    }

    // 检查是否在倒计时中
    if (this.data.countdown > 0) {
      return;
    }

    const { newPhone } = this.data.formData;

    this.setData({ isSendingCode: true });

    user.sendChangePhoneCode(newPhone.trim())
      .then(res => {
        this.setData({ isSendingCode: false });
        
        util.showToast('验证码已发送', 'success');
        
        // 开始倒计时
        this.startCountdown();
      })
      .catch(err => {
        this.setData({ isSendingCode: false });
        
        console.error('发送验证码失败:', err);
        util.showToast(err.message || '发送验证码失败，请稍后再试');
      });
  },

  /**
   * 开始倒计时
   */
  startCountdown: function() {
    let countdown = 60;
    this.setData({ countdown });

    const timer = setInterval(() => {
      countdown--;
      this.setData({ countdown });

      if (countdown <= 0) {
        clearInterval(timer);
        this.setData({ 
          countdown: 0,
          countdownTimer: null 
        });
      }
    }, 1000);

    this.setData({ countdownTimer: timer });
  },

  /**
   * 提交修改
   */
  submitChange: function() {
    // 表单验证
    if (!this.validateForm(true)) {
      return;
    }

    // 防止重复提交
    if (this.data.isSubmitting) {
      return;
    }

    const { newPhone, verifyCode } = this.data.formData;

    this.setData({ isSubmitting: true });
    util.showLoading('修改中...');

    user.changePhone(newPhone.trim(), verifyCode.trim())
      .then(res => {
        util.hideLoading();
        this.setData({ isSubmitting: false });
        
        util.showToast('手机号修改成功', 'success');
        
        // 更新全局用户信息
        if (app.globalData.userInfo) {
          app.globalData.userInfo.phone = newPhone.trim();
          wx.setStorageSync('userInfo', app.globalData.userInfo);
        }
        
        // 延迟返回上一页
        setTimeout(() => {
          wx.navigateBack();
        }, 1500);
      })
      .catch(err => {
        util.hideLoading();
        this.setData({ isSubmitting: false });
        
        console.error('修改手机号失败:', err);
        util.showToast(err.message || '修改失败，请稍后再试');
      });
  }
}); 