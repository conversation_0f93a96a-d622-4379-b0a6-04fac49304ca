# 选选项目 PM2 管理脚本 (PowerShell版本)
# 使用方法: ./pm2-scripts.ps1 [command]

param(
    [Parameter(Mandatory=$true)]
    [string]$Command
)

switch ($Command.ToLower()) {
    "start" {
        Write-Host "🚀 启动选选服务器..." -ForegroundColor Green
        pm2 start ecosystem.config.js --env production
    }
    "stop" {
        Write-Host "⏹️  停止选选服务器..." -ForegroundColor Yellow
        pm2 stop xuanxuan-server
    }
    "restart" {
        Write-Host "🔄 重启选选服务器..." -ForegroundColor Blue
        pm2 restart xuanxuan-server
    }
    "reload" {
        Write-Host "🔄 重新加载选选服务器（零停机）..." -ForegroundColor Blue
        pm2 reload xuanxuan-server
    }
    "delete" {
        Write-Host "🗑️  删除选选服务器进程..." -ForegroundColor Red
        pm2 delete xuanxuan-server
    }
    "status" {
        Write-Host "📊 查看选选服务器状态..." -ForegroundColor Cyan
        pm2 status xuanxuan-server
    }
    "logs" {
        Write-Host "📋 查看选选服务器日志..." -ForegroundColor Cyan
        pm2 logs xuanxuan-server
    }
    "monit" {
        Write-Host "📊 打开PM2监控面板..." -ForegroundColor Cyan
        pm2 monit
    }
    "setup" {
        Write-Host "🔧 初始化环境..." -ForegroundColor Green
        
        # 创建必要的目录
        if (!(Test-Path "logs")) { New-Item -ItemType Directory -Path "logs" }
        if (!(Test-Path "pids")) { New-Item -ItemType Directory -Path "pids" }
        Write-Host "✅ 目录创建完成" -ForegroundColor Green
        
        # 安装依赖
        Write-Host "📦 安装项目依赖..." -ForegroundColor Yellow
        npm install --production
        Write-Host "✅ 依赖安装完成" -ForegroundColor Green
        
        # 启动应用
        pm2 start ecosystem.config.js --env production
        Write-Host "✅ 应用启动完成" -ForegroundColor Green
    }
    default {
        Write-Host "使用方法: ./pm2-scripts.ps1 [command]" -ForegroundColor White
        Write-Host ""
        Write-Host "可用命令:" -ForegroundColor White
        Write-Host "  start   - 启动应用" -ForegroundColor Gray
        Write-Host "  stop    - 停止应用" -ForegroundColor Gray
        Write-Host "  restart - 重启应用" -ForegroundColor Gray
        Write-Host "  reload  - 重新加载应用（零停机）" -ForegroundColor Gray
        Write-Host "  delete  - 删除应用进程" -ForegroundColor Gray
        Write-Host "  status  - 查看应用状态" -ForegroundColor Gray
        Write-Host "  logs    - 查看应用日志" -ForegroundColor Gray
        Write-Host "  monit   - 打开监控面板" -ForegroundColor Gray
        Write-Host "  setup   - 初始化环境并启动" -ForegroundColor Gray
    }
} 