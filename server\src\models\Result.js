const mongoose = require('mongoose');

const ResultSchema = new mongoose.Schema(
  {
    questionId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Question',
      required: true
    },
    summaryType: {
      type: String,
      enum: ['rule', 'ai'],
      default: 'rule'
    },
    data: {
      type: Object,
      required: true
    },
    lastUpdated: {
      type: Date,
      default: Date.now
    }
  },
  {
    timestamps: true
  }
);

// 创建复合唯一索引
ResultSchema.index({ questionId: 1, summaryType: 1 }, { unique: true });

module.exports = mongoose.model('Result', ResultSchema); 