<!--pages/user/change-phone/change-phone.wxml-->
<view class="page">
  <view class="container p-30">
    <!-- 当前手机号显示 -->
    <view class="header-section mb-40">
      <view class="font-size-small text-secondary">
        当前手机号：{{currentPhone}}
      </view>
    </view>

    <!-- 修改手机号表单 -->
    <view class="form-section">
      <view class="card p-30">
        <!-- 新手机号输入 -->
        <view class="form-group mb-30">
          <view class="form-label font-size-small text-dark mb-10">新手机号</view>
          <view class="form-input-wrap">
            <input 
              class="form-input {{errors.newPhone ? 'error' : ''}}"
              type="number"
              maxlength="11"
              placeholder="请输入新手机号"
              value="{{formData.newPhone}}"
              data-field="newPhone"
              bindinput="onInputChange"
            />
          </view>
          <view wx:if="{{errors.newPhone}}" class="form-error">
            {{errors.newPhone}}
          </view>
        </view>

        <!-- 验证码输入和发送 -->
        <view class="form-group mb-30">
          <view class="form-label font-size-small text-dark mb-10">验证码</view>
          <view class="code-input-group">
            <input 
              class="form-input {{errors.verifyCode ? 'error' : ''}}"
              type="number"
              maxlength="6"
              placeholder="请输入6位验证码"
              value="{{formData.verifyCode}}"
              data-field="verifyCode"
              bindinput="onInputChange"
            />
            <view 
              class="code-btn {{countdown > 0 || isSendingCode ? 'disabled' : ''}}"
              bindtap="sendVerifyCode"
            >
              <view wx:if="{{isSendingCode}}">发送中...</view>
              <view wx:elif="{{countdown > 0}}">{{countdown}}s后重发</view>
              <view wx:else>发送验证码</view>
            </view>
          </view>
          <view wx:if="{{errors.verifyCode}}" class="form-error">
            {{errors.verifyCode}}
          </view>
        </view>

        <!-- 温馨提示 -->
        <view class="tips-section mb-30">
          <view class="tips-title">温馨提示：</view>
          <view class="tips-list">
            <view class="tips-item">
              • 验证码将发送至新手机号，请保持手机畅通
            </view>
            <view class="tips-item">
              • 修改手机号后，请使用新手机号进行登录
            </view>
            <view class="tips-item">
              • 如有问题，请联系客服处理
            </view>
          </view>
        </view>

        <!-- 提交按钮 -->
        <view class="form-actions text-center">
          <button 
            class="btn btn-primary btn-center {{isSubmitting ? 'disabled' : ''}}"
            bindtap="submitChange"
            disabled="{{isSubmitting}}"
          >
            <view wx:if="{{isSubmitting}}">修改中...</view>
            <view wx:else>确认修改</view>
          </button>
        </view>
      </view>
    </view>
  </view>
</view> 