<!--components/product-show/product-show.wxml-->
<view class="product-show-container">
  <!-- 加载状态 -->
  <view class="loading-container py-60" wx:if="{{loading && products.length === 0}}">
    <loading size="24" color="#3B7ADB"></loading>
    <text class="loading-text ml-20">搜索中...</text>
  </view>

  <!-- 产品列表滚动容器 -->
  <scroll-view 
    class="products-scroll-view {{products.length === 0 && !loading ? 'empty-scroll-view' : ''}}"
    scroll-y="{{true}}"
    lower-threshold="{{100}}"
    scroll-top="{{scrollTop}}"
    enable-back-to-top="{{true}}"
    enhanced="{{true}}"
    show-scrollbar="{{false}}"
    wx:if="{{!loading || products.length > 0}}"
    bindscrolltolower="onScrollToLower"
  >
    <!-- 产品列表 -->
    <view class="products-list" wx:if="{{products.length > 0}}">
      <view 
        class="product-item p-20" 
        wx:for="{{products}}" 
        wx:key="{{item.skuId || index}}"
        data-product="{{item}}"
        bindtap="onProductTap"
      >
        <image 
          class="product-image mr-20" 
          src="{{item.imageUrl}}" 
          mode="aspectFit"
          lazy-load="{{true}}"
          binderror="onImageError"
        ></image>
        <view class="product-info">
          <view class="product-name mb-10">{{item.skuName}}</view>
          <view class="product-meta">
            <view>
              <view class="product-date">{{item.releaseDate || '未知'}}</view>
              <view class="product-type">{{item.productType}}</view>
            </view>
            <!-- 价格范围显示 -->
            <view class="product-price" wx:if="{{item.priceRange && (item.priceRange.min > 0 || item.priceRange.max > 0)}}">
              <text class="price-label">价格：</text>
              <text class="price-range" wx:if="{{item.priceRange.min === item.priceRange.max && item.priceRange.min > 0}}">¥{{item.priceRange.min}}</text>
              <text class="price-range" wx:elif="{{item.priceRange.min > 0 && item.priceRange.max > 0}}">¥{{item.priceRange.min}} - ¥{{item.priceRange.max}}</text>
              <text class="price-range" wx:elif="{{item.priceRange.max > 0}}">¥{{item.priceRange.max}}以下</text>
              <text class="price-range" wx:else>价格待定</text>
            </view>
          </view>
        </view>
        <view class="product-actions">
          <view 
            class="compare-btn {{item.isInCompare ? 'compare-btn-active' : ''}}"
            data-product="{{item}}"
            catchtap="onCompareToggle"
          >
            <text class="compare-btn-text">{{item.isInCompare ? '已加入' : '加入对比'}}</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 底部加载状态和操作区域 -->
    <view class="load-more-container py-40" wx:if="{{products.length > 0}}">
      <!-- 查看更多产品文字链接 -->
      <view class="load-more-text-container" wx:if="{{hasMore && !loadingMore}}">
        <view class="load-more-info">
          <text class="remaining-count">还有{{totalCount - products.length > 0 ? totalCount - products.length : 0}}个产品未显示</text>
        </view>
        <view 
          class="load-more-link"
          bindtap="onLoadMoreClick"
        >
          <text class="load-more-text">查看更多产品</text>
          <text class="load-more-arrow">↓</text>
        </view>
      </view>
      
      <!-- 加载中状态 -->
      <view class="load-more-loading py-20" wx:if="{{loadingMore}}">
        <loading size="20" color="#3B7ADB"></loading>
        <text class="ml-20">正在加载更多产品...</text>
      </view>
      
      <!-- 没有更多数据 -->
      <view class="no-more py-20" wx:if="{{!hasMore && !loadingMore}}">
        <view class="no-more-icon">✨</view>
        <text class="no-more-text">已显示全部{{totalCount}}个产品</text>
      </view>
    </view>

    <!-- 空状态 - 移到 scroll-view 内部 -->
    <view class="empty-container" wx:if="{{products.length === 0 && hasSearched && !loading}}">
      <view class="empty-icon mb-20">📱</view>
      <view class="empty-title mb-10">暂无相关产品</view>
      <view class="empty-desc">请尝试调整筛选条件重新搜索</view>
    </view>

    <!-- 初始状态 - 移到 scroll-view 内部 -->
    <view class="empty-container welcome-container" wx:if="{{!loading && products.length === 0 && !hasSearched}}">
      <view class="welcome-icon mb-20">🔍</view>
      <view class="welcome-title mb-10">欢迎使用产品库</view>
      <view class="welcome-desc mb-30">请选择产品类型和品牌进行搜索</view>
    </view>
  </scroll-view>
</view>