#!/usr/bin/env node
/**
 * 产品对比工具函数
 * 基于DeepSeek API实现智能产品参数对比
 * 作者: AI助手
 * 创建时间: 2025-07-03
 */

const axios = require('axios');
const path = require('path');
const fs = require('fs');

// 引入产品数据
const apple_phones_from_json = require('./apple_phones_from_json.js');

// DeepSeek API配置
const DEEPSEEK_API_KEY = process.env.DEEPSEEK_API_KEY || '***********************************';
const DEEPSEEK_API_BASE = process.env.DEEPSEEK_API_BASE || 'https://api.deepseek.com';
const DEEPSEEK_MODEL = process.env.DEEPSEEK_MODEL || 'deepseek-chat';

// 默认配置
const DEFAULT_CONFIG = {
  temperature: 0.7,
  maxTokens: 3000,
  timeout: 120000
};

/**
 * 调用DeepSeek API
 * @param {String} userPrompt 用户提示
 * @param {String} systemPrompt 系统提示
 * @param {Object} config 配置选项
 * @returns {Promise<String>} API响应内容
 */
async function callDeepSeekAPI(userPrompt, systemPrompt = null, config = {}) {
  try {
    const aiConfig = { ...DEFAULT_CONFIG, ...config };
    
    const baseEndpoint = DEEPSEEK_API_BASE.endsWith('/') 
      ? DEEPSEEK_API_BASE.slice(0, -1) 
      : DEEPSEEK_API_BASE;
    
    const url = `${baseEndpoint}/v1/chat/completions`;
    
    console.log(`正在调用DeepSeek API进行产品对比分析...`);
    
    const messages = [];
    if (systemPrompt) {
      messages.push({ role: "system", content: systemPrompt });
    }
    messages.push({ role: "user", content: userPrompt });
    
    const response = await axios({
      url: url,
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${DEEPSEEK_API_KEY}`,
        'Content-Type': 'application/json',
      },
      data: {
        model: DEEPSEEK_MODEL,
        messages: messages,
        temperature: aiConfig.temperature,
        max_tokens: aiConfig.maxTokens,
        stream: false
      },
      timeout: aiConfig.timeout,
    });
    
    if (response.status === 200) {
      console.log(`✅ DeepSeek API调用成功`);
      return response.data.choices[0].message.content;
    } else {
      throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
    }
  } catch (error) {
    console.error('❌ DeepSeek API调用失败:', error.message);
    throw new Error(`DeepSeek AI对比分析失败: ${error.message}`);
  }
}

/**
 * 根据产品名称查找产品数据
 * @param {String} productName 产品名称
 * @returns {Object|null} 产品数据对象
 */
function findProductByName(productName) {
  // 先进行精确匹配
  let product = apple_phones_from_json.find(p => 
    p.skuName.toLowerCase() === productName.toLowerCase() ||
    p.skuId.toLowerCase() === productName.toLowerCase()
  );
  
  if (product) return product;
  
  // 模糊匹配
  product = apple_phones_from_json.find(p => 
    p.skuName.toLowerCase().includes(productName.toLowerCase()) ||
    productName.toLowerCase().includes(p.skuName.toLowerCase())
  );
  
  return product;
}

/**
 * 格式化产品信息为文本
 * @param {Object} product 产品对象
 * @returns {String} 格式化的产品信息
 */
function formatProductInfo(product) {
  if (!product) return '产品信息未找到';
  
  const info = [`产品名称: ${product.skuName}`];
  
  // 基本信息
  info.push(`品牌: ${product.brandName}`);
  info.push(`价格区间: ¥${product.priceRange.min} - ¥${product.priceRange.max}`);
  
  // 配置选项
  info.push(`存储配置: ${product.configurations.map(c => c.storage).join(', ')}`);
  
  // 规格参数
  if (product.productSpecs) {
    const { general, phone } = product.productSpecs;
    
    if (general) {
      info.push(`颜色选择: ${general.color}`);
      info.push(`重量: ${general.weight}`);
      info.push(`尺寸: ${general.dimensions}`);
    }
    
    if (phone) {
      info.push(`屏幕: ${phone.screenSize}`);
      info.push(`分辨率: ${phone.screenResolution}`);
      info.push(`处理器: ${phone.processor}`);
      info.push(`电池: ${phone.battery}`);
      info.push(`后置摄像头: ${phone.camera.rear}`);
      info.push(`前置摄像头: ${phone.camera.front}`);
      info.push(`操作系统: ${phone.operatingSystem}`);
      info.push(`网络: ${phone.network}`);
    }
  }
  
  return info.join('\n');
}

/**
 * 主要的产品对比函数
 * @param {Array<String>} productNames 产品名称列表
 * @param {Object} options 对比选项
 * @returns {Promise<Object>} 产品对比结果
 */
async function compareProducts(productNames, options = {}) {
  try {
    console.log(`\n🔍 开始产品对比分析...`);
    console.log(`待对比产品: ${productNames.join(', ')}`);
    
    // 验证输入
    if (!Array.isArray(productNames) || productNames.length < 2) {
      throw new Error('至少需要提供2个产品名称进行对比');
    }
    
    if (productNames.length > 5) {
      throw new Error('最多支持5个产品同时对比');
    }
    
    // 查找产品数据
    const products = [];
    const notFound = [];
    
    for (const name of productNames) {
      const product = findProductByName(name);
      if (product) {
        products.push(product);
        console.log(`✅ 找到产品: ${product.skuName}`);
      } else {
        notFound.push(name);
        console.log(`❌ 未找到产品: ${name}`);
      }
    }
    
    if (products.length < 2) {
      throw new Error(`找到的有效产品不足2个，无法进行对比。未找到的产品: ${notFound.join(', ')}`);
    }
    
    // 构建产品信息文本
    const productInfoTexts = products.map(product => formatProductInfo(product));
    
    // 构建系统提示
    const systemPrompt = `你是一个专业的数码产品分析师，擅长进行客观、详细的产品对比分析。

请根据提供的产品信息，进行全面的对比分析，包括但不限于：

1. **核心参数对比**: 处理器性能、屏幕规格、摄像头配置、电池续航等
2. **价格性价比分析**: 不同配置的价格区间和性价比评估
3. **使用场景建议**: 根据不同需求（日常使用、摄影、游戏、商务等）推荐合适的产品
4. **优缺点总结**: 每个产品的主要优势和不足
5. **购买建议**: 基于预算和需求的具体推荐

要求：
- 保持客观中立，基于技术参数进行分析
- 使用易懂的语言，避免过于技术化的表述
- 提供具体的数据对比和实用的建议
- 结构化输出，便于阅读理解`;

    // 构建用户提示
    const userPrompt = `请对以下${products.length}款产品进行详细对比分析：

${products.map((product, index) => 
  `## 产品${index + 1}: ${product.skuName}\n${productInfoTexts[index]}`
).join('\n\n')}

请提供完整的对比分析报告。`;

    // 调用AI进行分析
    const analysisResult = await callDeepSeekAPI(userPrompt, systemPrompt, {
      temperature: 0.7,
      maxTokens: 3000
    });
    
    // 构建返回结果
    const result = {
      success: true,
      comparedProducts: products.map(p => ({
        skuId: p.skuId,
        skuName: p.skuName,
        brandName: p.brandName,
        priceRange: p.priceRange
      })),
      notFoundProducts: notFound,
      analysisReport: analysisResult,
      timestamp: new Date().toISOString(),
      comparisonCount: products.length
    };
    
    console.log(`\n✅ 产品对比分析完成！`);
    console.log(`对比产品数量: ${products.length}`);
    if (notFound.length > 0) {
      console.log(`未找到产品: ${notFound.join(', ')}`);
    }
    
    return result;
    
  } catch (error) {
    console.error(`❌ 产品对比失败: ${error.message}`);
    return {
      success: false,
      error: error.message,
      comparedProducts: [],
      notFoundProducts: productNames,
      analysisReport: null,
      timestamp: new Date().toISOString(),
      comparisonCount: 0
    };
  }
}

/**
 * 保存对比结果到文件
 * @param {Object} result 对比结果
 * @param {String} filename 文件名（可选）
 * @returns {Promise<String>} 保存的文件路径
 */
async function saveComparisonResult(result, filename = null) {
  try {
    if (!filename) {
      const timestamp = new Date().toISOString().replace(/[:.]/g, '-');
      filename = `product_comparison_${timestamp}.json`;
    }
    
    const filePath = path.join(__dirname, filename);
    
    // 保存完整结果
    await fs.promises.writeFile(filePath, JSON.stringify(result, null, 2), 'utf8');
    
    // 同时保存一个纯文本版本的分析报告
    if (result.success && result.analysisReport) {
      const txtFilename = filename.replace('.json', '.txt');
      const txtFilePath = path.join(__dirname, txtFilename);
      
      const txtContent = `产品对比分析报告
生成时间: ${result.timestamp}
对比产品: ${result.comparedProducts.map(p => p.skuName).join(', ')}

${result.analysisReport}`;
      
      await fs.promises.writeFile(txtFilePath, txtContent, 'utf8');
      console.log(`📄 分析报告已保存: ${txtFilename}`);
    }
    
    console.log(`💾 对比结果已保存: ${filename}`);
    return filePath;
    
  } catch (error) {
    console.error(`保存文件失败: ${error.message}`);
    throw error;
  }
}

/**
 * 获取可用产品列表
 * @returns {Array} 产品名称列表
 */
function getAvailableProducts() {
  return apple_phones_from_json.map(product => ({
    skuId: product.skuId,
    skuName: product.skuName,
    brandName: product.brandName,
    priceRange: product.priceRange
  }));
}

/**
 * 演示函数
 */
async function demo() {
  console.log('='.repeat(60));
  console.log('📱 产品对比工具演示');
  console.log('='.repeat(60));
  
  // 演示1: 对比最新的iPhone 16系列
  const demo1Products = ['iPhone 16', 'iPhone 16 Pro', 'iPhone 16 Pro Max'];
  console.log(`\n📋 演示1: ${demo1Products.join(' vs ')}`);
  
  const result1 = await compareProducts(demo1Products);
  if (result1.success) {
    console.log('\n📊 对比分析报告:');
    console.log(result1.analysisReport.substring(0, 500) + '...(省略)');
    
    // 保存结果
    await saveComparisonResult(result1, 'demo1_iphone16_comparison.json');
  }
  
  // 演示2: 对比不同代际产品
  const demo2Products = ['iPhone 16', 'iPhone 15', 'iPhone 14'];
  console.log(`\n📋 演示2: ${demo2Products.join(' vs ')}`);
  
  const result2 = await compareProducts(demo2Products);
  if (result2.success) {
    console.log('\n📊 对比分析报告:');
    console.log(result2.analysisReport.substring(0, 500) + '...(省略)');
    
    await saveComparisonResult(result2, 'demo2_generation_comparison.json');
  }
  
  console.log('\n✅ 演示完成！');
}

// 如果直接运行此文件，执行演示
if (require.main === module) {
  // 检查环境变量
  if (!DEEPSEEK_API_KEY || DEEPSEEK_API_KEY === 'your_deepseek_api_key') {
    console.error('❌ 请设置DEEPSEEK_API_KEY环境变量');
    console.log('示例: export DEEPSEEK_API_KEY=your_actual_api_key');
    process.exit(1);
  }
  
  // 显示可用产品
  console.log('📱 可用产品列表:');
  const availableProducts = getAvailableProducts();
  availableProducts.slice(0, 10).forEach((product, index) => {
    console.log(`  ${index + 1}. ${product.skuName} (¥${product.priceRange.min}-${product.priceRange.max})`);
  });
  console.log(`  ... 总共 ${availableProducts.length} 个产品`);
  
  // 运行演示
  demo().catch(error => {
    console.error('演示执行失败:', error.message);
    process.exit(1);
  });
}

module.exports = {
  compareProducts,
  saveComparisonResult,
  getAvailableProducts,
  findProductByName,
  formatProductInfo
};