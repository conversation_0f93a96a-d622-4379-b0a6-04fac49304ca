<!--pages/product/product_detail/product_detail.wxml-->
<wxs module="utils">
  // 判断参数是否为变化参数
  var isVaryingParam = function(varyingParamKeys, category, param) {
    var key = category + '.' + param;
    return varyingParamKeys.indexOf(key) !== -1;
  };
  
  // 判断分类是否包含变化参数
  var isVaryingCategory = function(varyingCategories, category) {
    return varyingCategories.indexOf(category) !== -1;
  };
  
  module.exports = {
    isVaryingParam: isVaryingParam,
    isVaryingCategory: isVaryingCategory
  };
</wxs>

<view class="container">
  <!-- 加载状态 -->
  <view wx:if="{{loading}}" class="loading-container">
    <view class="loading-spinner"></view>
    <text class="loading-text">正在加载产品参数...</text>
  </view>

  <!-- 错误状态 -->
  <view wx:elif="{{error}}" class="error-container">
    <view class="error-icon">⚠️</view>
    <text class="error-message">{{error}}</text>
    <button class="retry-btn" bindtap="onRetry">重新加载</button>
  </view>

  <!-- 产品详情 -->
  <view wx:elif="{{productInfo}}" class="product-detail">
    
    <!-- 产品头部信息 -->
    <view class="product-header">
      <view class="product-image-container">
        <image 
          class="product-image" 
          src="{{productInfo.basic.imageUrl}}" 
          mode="aspectFit"
          bindtap="previewImage"
          lazy-load
        />
        <view class="image-placeholder" wx:if="{{!productInfo.basic.imageUrl}}">
          <text>暂无图片</text>
        </view>
      </view>
      
      <view class="product-info">
        <view class="product-name">{{productInfo.basic.skuName}}</view>
        <view class="product-brand">{{productInfo.basic.brandName}}</view>
        <view class="product-type">{{productInfo.basic.productType}}</view>
        
        <!-- 价格信息 -->
        <view wx:if="{{productInfo.pricing.hasPrice}}" class="price-section">
          <view class="price-range">{{productInfo.pricing.priceRange}}</view>
          <view class="price-note" bindtap="showPriceInfo">
            <text>价格仅供参考</text>
            <text class="info-icon">ⓘ</text>
          </view>
        </view>
        
        <!-- 操作按钮 -->
        <view class="action-buttons">
          <button class="btn-copy" bindtap="copyProductName">
            <text class="btn-icon">📋</text>
            <text class="btn-text">复制名称</text>
          </button>
          <button class="btn-compare" bindtap="addToCompare">
            <text class="btn-icon">⚖️</text>
            <text class="btn-text">{{isInCompareList ? '移除对比' : '加入对比'}}</text>
          </button>
        </view>
      </view>
    </view>

    <!-- 配置选择 -->
    <view wx:if="{{productInfo.configurations.total > 1}}" class="config-section">
      <view class="section-title">⚙️ 配置选项</view>
      <view class="config-selector" bindtap="showConfigSelector">
        <view class="config-current">
          <text class="config-name">{{selectedConfig.name}}</text>
          <text wx:if="{{selectedConfig.price > 0}}" class="config-price">¥{{selectedConfig.price}}</text>
          <text wx:else class="config-price-unavailable">暂无价格</text>
        </view>
        <view class="config-arrow">></view>
      </view>
      <view class="config-stats">
        <text>共{{productInfo.configurations.total}}个配置</text>
      </view>
    </view>

    <!-- 规格参数 -->
    <view class="specs-section">
      <view class="section-title">🔧 产品规格</view>
      
      <!-- 动态规格显示 -->
      <view wx:if="{{displaySpecs}}" class="dynamic-specs">
        <view 
          class="spec-category" 
          wx:for="{{displaySpecs}}" 
          wx:key="category"
          wx:for-item="categorySpecs" 
          wx:for-index="categoryName"
        >
          <view 
            class="category-header {{expandedCategories[categoryName] ? 'expanded' : ''}} {{utils.isVaryingCategory(varyingCategories, categoryName) ? 'varying-category' : ''}}"
            bindtap="toggleCategory"
            data-category="{{categoryName}}"
          >
            <text class="category-name {{utils.isVaryingCategory(varyingCategories, categoryName) ? 'varying-category-name' : ''}}">{{categoryName}}</text>
            <view class="category-toggle">
              <text class="toggle-icon">{{expandedCategories[categoryName] ? '▼' : '▶'}}</text>
            </view>
          </view>
          
          <view class="category-content {{expandedCategories[categoryName] ? 'show' : 'hide'}} {{utils.isVaryingCategory(varyingCategories, categoryName) ? 'varying-category-content' : ''}}">>>
            <view 
              class="spec-item" 
              wx:for="{{categorySpecs}}" 
              wx:key="paramName"
              wx:for-item="paramValue" 
              wx:for-index="paramName"
            >
              <view class="param-name">{{paramName}}</view>
              <view class="param-value">
                <text 
                  class="{{utils.isVaryingParam(varyingParamKeys, categoryName, paramName) ? 'varying-param-value' : 'normal-param-value'}}"
                >{{paramValue}}</text>
              </view>
            </view>
          </view>
        </view>
      </view>
    </view>

    <!-- 产品评分 -->
    <view wx:if="{{productInfo && productInfo.productID}}" class="rating-section">
      <view class="section-title">⭐ 产品评分</view>
      <view class="rating-content">
        <rate-score
          target-id="{{productInfo.productID}}"
          rating-type="product"
          average-rating="{{productInfo.ratingStats ? productInfo.ratingStats.averageRating : 0}}"
          total-ratings="{{productInfo.ratingStats ? productInfo.ratingStats.totalRatings : 0}}"
          user-rating="{{productInfo.userRating ? productInfo.userRating.rating : null}}"
          has-rated="{{productInfo.userRating ? productInfo.userRating.hasRated : false}}"
          show-stats="{{true}}"
          size="medium"
          disabled="{{false}}"
          panel-title="为这个产品评分"
          success-message="评分成功"
          bind:submitRating="onSubmitRating"
          bind:ratingUpdated="onRatingUpdated"
        />
      </view>
    </view>
  </view>

  <!-- 空状态 -->
  <view wx:else class="empty-container">
    <view class="empty-icon">📱</view>
    <view class="empty-title">暂无产品信息</view>
    <view class="empty-desc">请返回产品库选择产品查看详情</view>
  </view>
  
  <!-- 产品对比输入组件 -->
  <product-compare-input 
    visible="{{compareVisible}}"
    bind:toggleVisible="onToggleCompareVisible"
  />
</view>

<!-- 配置选择弹窗 -->
<view wx:if="{{showConfigModal}}" class="modal-overlay" bindtap="hideConfigModal">
  <view class="config-modal" catchtap="">
    <view class="modal-header">
      <text class="modal-title">选择配置</text>
      <view class="modal-close" bindtap="hideConfigModal">×</view>
    </view>
    <view class="modal-content">
      <view 
        class="config-option {{selectedConfig.name === item.name ? 'selected' : ''}}"
        wx:for="{{productInfo.configurations.allConfigurations}}"
        wx:key="name"
        bindtap="selectConfig"
        data-config="{{item}}"
      >
        <view class="config-info">
          <text class="config-name">{{item.name}}</text>
          <text wx:if="{{item.price > 0}}" class="config-price">¥{{item.price}}</text>
          <text wx:else class="config-price-unavailable">暂无价格</text>
        </view>
        <view wx:if="{{selectedConfig.name === item.name}}" class="config-selected">✓</view>
      </view>
    </view>
  </view>
</view>
