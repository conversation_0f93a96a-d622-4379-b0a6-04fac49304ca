/**
 * WebSocket服务器
 * 该文件负责在独立端口上运行WebSocket服务
 */

const http = require('http');
const socketService = require('./services/socketService');

// 创建独立的HTTP服务器实例
const server = http.createServer();

// 获取WebSocket端口，默认为5001（与HTTP API分离）
const SOCKET_PORT = process.env.SOCKET_PORT || 5001;

// 初始化WebSocket服务
const wss = socketService.initWebSocket(server);

// 启动WebSocket服务器
server.listen(SOCKET_PORT, '0.0.0.0', () => {
  console.log('-----------------------------------------------');
  console.log(`✅ WebSocket服务器成功启动! 端口: ${SOCKET_PORT}`);
  console.log(`🔌 WebSocket地址: ws://localhost:${SOCKET_PORT}`);
  console.log('-----------------------------------------------');
});

// 导出sendNotification函数供其他模块使用
exports.sendNotification = socketService.sendNotification;