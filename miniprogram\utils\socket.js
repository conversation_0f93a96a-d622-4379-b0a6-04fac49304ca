/**
 * WebSocket服务工具模块
 * 管理WebSocket连接、消息处理等
 */

// 不要在模块顶部获取应用实例
// const app = getApp();

/**
 * WebSocket服务类
 */
class SocketService {
  constructor() {
    this.socketTask = null;
    this.isConnected = false;
    this.reconnectCount = 0;
    this.maxReconnectCount = 5;
    this.reconnectTimer = null;
    this.heartbeatTimer = null;
    this.listeners = {};
    this.isConnecting = false; // 添加连接中状态标志
  }

  /**
   * 监听连接关闭
   * @param {Object} result 关闭事件数据
   * @param {String} token 用于重连的token
   */
  handleClose(result, token) {
    console.log('WebSocket连接已关闭:', result);
    this.isConnected = false;
    this.isConnecting = false; // 重置连接中状态
    this.stopHeartbeat();
    
    // 更新应用的连接状态
    const app = getApp();
    if (app && app.globalData) {
      app.globalData.socketConnected = false;
    }
    
    // 仅在非正常关闭时且登录状态下尝试重连 (code 1000 表示正常关闭)
    if (result.code !== 1000) {
      if (app && app.globalData && app.globalData.isLoggedIn) {
        this.reconnect(token);
      }
    }
  }

  /**
   * 连接WebSocket服务器
   * @param {String} token JWT令牌
   * @returns {Promise} 连接结果Promise
   */
  connect(token) {
    if (!token) {
      console.error('连接WebSocket失败: 缺少token');
      return Promise.reject(new Error('缺少token'));
    }

    // 如果正在连接中，避免重复连接
    if (this.isConnecting) {
      console.log('WebSocket正在连接中，请勿重复连接');
      return Promise.reject(new Error('WebSocket正在连接中'));
    }

    // 如果已经连接，先断开
    if (this.socketTask || this.isConnected) {
      console.log('关闭现有WebSocket连接...');
      this.close();
    }

    // 标记为连接中
    this.isConnecting = true;

    return new Promise((resolve, reject) => {
      try {
        // 获取app实例
        const app = getApp();
        
        // 确保token是字符串类型
        let tokenStr = '';
        if (typeof token === 'string') {
          tokenStr = token;
        } else if (typeof token === 'object') {
          // 对象可能包含accessToken字段
          tokenStr = token.accessToken || JSON.stringify(token);
        } else {
          tokenStr = String(token);
        }
        
        // 调试日志
        console.log('使用token连接WebSocket:', tokenStr.substring(0, 10) + '...');
        
        // 获取WebSocket服务地址
        const socketUrl = app ? `${app.globalData.socketUrl}?token=${tokenStr}` : `ws://localhost:5001?token=${tokenStr}`;
        
        console.log('正在连接WebSocket服务:', socketUrl);
        
        // 创建WebSocket连接
        this.socketTask = wx.connectSocket({
          url: socketUrl,
          success: () => {
            console.log('WebSocket连接请求已发送');
          },
          fail: (error) => {
            console.error('创建WebSocket连接失败:', error);
            this.socketTask = null;
            this.isConnecting = false; // 重置连接中状态
            // WebSocket连接失败不影响主要功能，静默处理
            reject(new Error('WebSocket连接失败'));
          }
        });
        
        // 监听连接打开
        this.socketTask.onOpen(() => {
          console.log('WebSocket连接已打开');
          this.isConnected = true;
          this.isConnecting = false; // 重置连接中状态
          this.reconnectCount = 0;
          
          // 开始心跳检测
          this.startHeartbeat();
          
          resolve(true);
        });
        
        // 监听接收消息
        this.socketTask.onMessage((result) => {
          try {
            const message = JSON.parse(result.data);
            console.log('收到WebSocket消息:', message);
            
            // 处理不同类型的消息
            this.handleMessage(message);
          } catch (error) {
            console.error('解析WebSocket消息失败:', error, result.data);
          }
        });
        
        // 监听连接关闭
        this.socketTask.onClose((result) => {
          this.handleClose(result, token);
        });
        
        // 监听连接错误
        this.socketTask.onError((error) => {
          console.error('WebSocket连接错误:', error);
          this.isConnected = false;
          this.isConnecting = false; // 重置连接中状态
          
          // 更新应用的连接状态
          const app = getApp();
          if (app && app.globalData) {
            app.globalData.socketConnected = false;
          }
          
          reject(error);
        });
      } catch (err) {
        console.error('连接WebSocket过程中发生异常:', err);
        this.isConnecting = false; // 重置连接中状态
        reject(err);
      }
    });
  }

  /**
   * 关闭WebSocket连接
   */
  close() {
    this.stopHeartbeat();
    
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
      this.reconnectTimer = null;
    }
    
    if (this.socketTask) {
      try {
        // 发送关闭请求
        this.socketTask.close({
          code: 1000, // 正常关闭
          reason: 'manual close',
          success: () => {
            console.log('WebSocket连接已成功关闭');
          },
          fail: (error) => {
            console.error('关闭WebSocket连接失败:', error);
          },
          complete: () => {
            this.socketTask = null;
            this.isConnected = false;
            this.isConnecting = false; // 重置连接中状态
            
            // 更新应用的连接状态
            const app = getApp();
            if (app && app.globalData) {
              app.globalData.socketConnected = false;
            }
          }
        });
      } catch (err) {
        console.error('关闭WebSocket时发生异常:', err);
        this.socketTask = null;
        this.isConnected = false;
        this.isConnecting = false; // 重置连接中状态
        
        // 更新应用的连接状态
        const app = getApp();
        if (app && app.globalData) {
          app.globalData.socketConnected = false;
        }
      }
    } else {
      // 如果已经不存在连接或已断开，直接清理状态
      this.socketTask = null;
      this.isConnected = false;
      this.isConnecting = false; // 重置连接中状态
      
      // 更新应用的连接状态
      const app = getApp();
      if (app && app.globalData) {
        app.globalData.socketConnected = false;
      }
    }
  }

  /**
   * 尝试重新连接
   * @param {String} token JWT令牌
   */
  reconnect(token) {
    if (this.reconnectTimer) {
      clearTimeout(this.reconnectTimer);
    }
    
    if (this.reconnectCount >= this.maxReconnectCount) {
      console.log('达到最大重连次数，停止重连');
      return;
    }
    
    this.reconnectCount++;
    
    // 重连间隔随重连次数增加
    const delay = Math.min(1000 * Math.pow(1.5, this.reconnectCount - 1), 30000);
    
    console.log(`${delay}ms后尝试第${this.reconnectCount}次重连...`);
    
    this.reconnectTimer = setTimeout(() => {
      const app = getApp();
      // 只有在登录状态下才进行重连
      if (app && app.globalData && app.globalData.isLoggedIn) {
        this.connect(token).catch(error => {
          console.error('重连失败:', error);
        });
      } else {
        console.log('用户已登出，取消重连');
      }
    }, delay);
  }

  /**
   * 开始心跳检测
   */
  startHeartbeat() {
    this.stopHeartbeat();
    
    this.heartbeatTimer = setInterval(() => {
      if (this.isConnected && this.socketTask) {
        this.socketTask.send({
          data: JSON.stringify({ type: 'ping' }),
          fail: (error) => {
            console.error('发送心跳包失败:', error);
            this.isConnected = false;
          }
        });
      } else {
        this.stopHeartbeat();
      }
    }, 30000); // 每30秒发送一次心跳
  }

  /**
   * 停止心跳检测
   */
  stopHeartbeat() {
    if (this.heartbeatTimer) {
      clearInterval(this.heartbeatTimer);
      this.heartbeatTimer = null;
    }
  }

  /**
   * 处理收到的消息
   * @param {Object} message 消息对象
   */
  handleMessage(message) {
    if (!message || !message.type) return;
    
    // 根据消息类型分发处理
    switch (message.type) {
      case 'notification':
        this.emit('notification', message.data);
        break;
      case 'connection':
        this.emit('connection', message);
        break;
      case 'pong':
        console.log('收到心跳响应');
        break;
      default:
        console.log('未处理的消息类型:', message.type);
    }
  }

  /**
   * 添加事件监听器
   * @param {String} event 事件名称
   * @param {Function} callback 回调函数
   */
  on(event, callback) {
    if (!this.listeners[event]) {
      this.listeners[event] = [];
    }
    
    this.listeners[event].push(callback);
  }

  /**
   * 移除事件监听器
   * @param {String} event 事件名称
   * @param {Function} callback 回调函数 (可选)
   */
  off(event, callback) {
    if (!this.listeners[event]) return;
    
    if (callback) {
      this.listeners[event] = this.listeners[event].filter(
        listener => listener !== callback
      );
    } else {
      delete this.listeners[event];
    }
  }

  /**
   * 触发事件
   * @param {String} event 事件名称
   * @param {any} data 事件数据
   */
  emit(event, data) {
    if (!this.listeners[event]) return;
    
    this.listeners[event].forEach(callback => {
      try {
        callback(data);
      } catch (error) {
        console.error(`执行${event}事件回调时出错:`, error);
      }
    });
  }
}

// 创建单例实例
const socketService = new SocketService();

module.exports = socketService; 