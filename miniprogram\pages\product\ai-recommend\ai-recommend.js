const app = getApp();
// 引入通用常量
const { REGION_LIST, OCCUPATION_LIST } = require('../../../utils/constants');
// 引入产品类型和品牌配置
const productConfig = require('../../../config/productTypesBrands');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    title: '',
    scene: '',
    keyFactors: '',
    budget: {
      min: null,
      max: null,
      currency: 'CNY'
    },
    tags: [],
    // 标签状态数组
    tagStatus: {},
    // 标签选项 - 从配置文件读取
    tagOptions: Object.keys(productConfig.productTypes),

    // AI推荐相关
    loading: false,
    canRecommend: false,

    // 品牌选择相关数据
    selectedCategory: '', // 选中的产品类别
    selectedBrands: [],
    availableBrands: [],
    brandStatus: {}, // 品牌选中状态追踪对象

    // 异步处理相关
    processing: false,
    taskId: null,
    pollInterval: null,
    processingMessage: '正在为您智能推荐产品...'
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // 初始化标签状态
    const tagStatus = {};
    this.data.tagOptions.forEach(tag => {
      tagStatus[tag] = false;
    });
    
    this.setData({
      tagStatus
    });

    // 检查是否可以获取推荐
    this.checkCanRecommend();
  },

  onShow: function () {
    // 通知应用当前页面已显示，用于更新TabBar角标
    if (app && app.onPageShow) {
      app.onPageShow('pages/product/ai-recommend/ai-recommend');
    }
    
    // 检查用户登录状态，提前告知登录要求
    this.checkLoginStatus();
  },

  onHide: function () {
    // 页面隐藏时的处理
  },

  onUnload: function () {
    // 页面卸载时清理定时器
    this.stopPolling();
  },

  /**
   * 输入标题
   */
  inputTitle: function(e) {
    this.setData({
      title: e.detail.value
    }, () => {
      this.checkCanRecommend();
    });
  },

  /**
   * 输入使用场景
   */
  inputScene: function(e) {
    this.setData({
      scene: e.detail.value
    });
  },

  /**
   * 输入关键考量因素
   */
  inputKeyFactors: function(e) {
    this.setData({
      keyFactors: e.detail.value
    });
  },
  
  /**
   * 输入预算最小值
   */
  inputBudgetMin: function(e) {
    this.setData({
      'budget.min': e.detail.value ? Number(e.detail.value) : null
    });
  },
  
  /**
   * 输入预算最大值
   */
  inputBudgetMax: function(e) {
    this.setData({
      'budget.max': e.detail.value ? Number(e.detail.value) : null
    });
  },
  
  /**
   * 选择标签
   */
  selectTag: function(e) {
    const { tag } = e.currentTarget.dataset;
    const { tags, tagStatus } = this.data;

    // 切换标签选中状态
    if (tagStatus[tag]) {
      // 取消选中
      const newTags = tags.filter(t => t !== tag);
      const newTagStatus = { ...tagStatus };
      newTagStatus[tag] = false;

      this.setData({
        tags: newTags,
        tagStatus: newTagStatus,
        // 清空品牌相关数据
        selectedCategory: '',
        selectedBrands: [],
        availableBrands: [],
        brandStatus: {}
      }, () => {
        this.checkCanRecommend();
        console.log('标签已取消选中:', tag, '当前选中标签:', this.data.tags);
      });
    } else {
      // 选中 - 对于AI推荐页面，只允许选择一个标签
      if (tags.length >= 1) {
        wx.showToast({
          title: '请只选择1个产品类别',
          icon: 'none'
        });
        return;
      }

      const newTags = [...tags, tag];
      const newTagStatus = { ...tagStatus };
      newTagStatus[tag] = true;

      this.setData({
        tags: newTags,
        tagStatus: newTagStatus
      }, () => {
        // 选中标签后立即初始化品牌选择
        this.initBrandSelection(tag);
        this.checkCanRecommend();
        console.log('标签已选中:', tag, '当前选中标签:', this.data.tags);
      });
    }
  },

  /**
   * 检查是否可以获取推荐
   */
  checkCanRecommend: function() {
    const { title, tags } = this.data;
    const canRecommend = title.trim().length > 0 && tags.length > 0;
    this.setData({ canRecommend });
  },

  /**
   * 获取AI推荐
   */
  getAiRecommendation: function() {
    // 首先检查登录状态
    if (!this.checkLoginStatus()) {
      return; // 如果未登录，checkLoginStatus会处理弹窗提示
    }

    const { title, tags, selectedBrands } = this.data;

    // 验证必填项
    if (!title.trim()) {
      wx.showToast({
        title: '请输入需求描述',
        icon: 'none'
      });
      return;
    }

    if (tags.length === 0) {
      wx.showToast({
        title: '请选择产品类别',
        icon: 'none'
      });
      return;
    }

    // 如果用户没有选择任何品牌，给出友好提示
    if (selectedBrands.length === 0) {
      wx.showModal({
        title: '品牌偏好',
        content: '您还没有选择品牌偏好，AI将为您推荐该类别下的所有优质品牌产品。是否继续？',
        showCancel: true,
        cancelText: '选择品牌',
        confirmText: '继续推荐',
        success: (res) => {
          if (res.confirm) {
            // 用户选择继续，直接调用AI推荐
            this.callAiRecommendation();
          }
          // 如果用户选择取消，什么都不做，让用户去选择品牌
        }
      });
      return;
    }

    // 直接调用AI推荐
    this.callAiRecommendation();
  },

  /**
   * 调用AI推荐API
   */
  callAiRecommendation: async function() {
    const { title, scene, keyFactors, budget, tags, selectedBrands, selectedCategory } = this.data;

    // 构建问题信息
    const questionInfo = {
      title: title.trim(),
      scene: scene.trim(),
      keyFactors: keyFactors.trim(),
      budget,
      tags,
      selectedCategory: selectedCategory || tags[0]
    };

    // 构建推荐参数
    const filterOptions = {
      productType: selectedCategory || tags[0],
      brands: selectedBrands.length > 0 ? selectedBrands : [], // 如果没选择品牌，传空数组
      budget: budget || null
    };

    console.log('AI推荐参数:', { questionInfo, filterOptions });

    // 设置加载状态
    this.setData({
      loading: true,
      processing: false
    });

    try {
      // 调用异步AI推荐接口
      const { question } = require('../../../utils/api');
      const response = await question.aiRecommendProducts(questionInfo, filterOptions, true);

      console.log('AI推荐初始响应:', response);

      if (response.success) {
        // 检查是否是异步处理响应
        if (response.code === 202 && response.data.status === 'processing') {
          console.log('AI推荐正在异步处理中，开始轮询...');

          // 设置处理中状态
          this.setData({
            loading: false,
            processing: true,
            taskId: response.data.taskId,
            processingMessage: response.data.message || '正在为您智能推荐产品...'
          });

          // 开始轮询检查结果状态
          this.startPollingRecommendStatus();
        } else {
          // 同步处理完成
          this.handleRecommendSuccess(response.data);
        }
      } else {
        throw new Error(response.message || 'AI推荐失败');
      }
    } catch (error) {
      console.error('AI推荐失败:', error);
      this.setData({
        loading: false,
        processing: false
      });

      wx.showToast({
        title: error.message || 'AI推荐失败，请重试',
        icon: 'none'
      });
    }
  },

  /**
   * 初始化品牌选择
   */
  initBrandSelection: function(selectedCategory) {
    if (!selectedCategory) {
      console.error('未找到选中的产品类别');
      return;
    }

    // 从配置文件获取对应类别的品牌
    const categoryData = productConfig.productTypes[selectedCategory];
    const availableBrands = categoryData ? categoryData.brands : [];

    // 初始化品牌状态对象
    const brandStatus = {};
    availableBrands.forEach(brand => {
      brandStatus[brand] = false;
    });

    this.setData({
      selectedCategory,
      selectedBrands: [],
      availableBrands,
      brandStatus
    });

    console.log('品牌选择已初始化:', selectedCategory, '可用品牌:', availableBrands);
  },

  /**
   * 切换品牌选择
   */
  onToggleBrand: function(e) {
    const { brand } = e.currentTarget.dataset;
    const { selectedBrands, brandStatus } = this.data;

    // 切换品牌选中状态
    if (brandStatus[brand]) {
      // 取消选择
      const newSelectedBrands = selectedBrands.filter(b => b !== brand);
      const newBrandStatus = { ...brandStatus };
      newBrandStatus[brand] = false;

      this.setData({
        selectedBrands: newSelectedBrands,
        brandStatus: newBrandStatus
      });
    } else {
      // 添加选择，最多选择10个品牌
      if (selectedBrands.length >= 10) {
        wx.showToast({
          title: '最多选择10个品牌',
          icon: 'none'
        });
        return;
      }

      const newSelectedBrands = [...selectedBrands, brand];
      const newBrandStatus = { ...brandStatus };
      newBrandStatus[brand] = true;

      this.setData({
        selectedBrands: newSelectedBrands,
        brandStatus: newBrandStatus
      });
    }

    console.log('品牌选择已更新:', this.data.selectedBrands);
  },

  /**
   * 检查用户登录状态
   * @returns {boolean} 是否已登录
   */
  checkLoginStatus: function() {
    const app = getApp();
    
    if (!app.globalData.isLoggedIn) {
      // 用户未登录，显示友好提示弹窗
      wx.showModal({
        title: '需要登录',
        content: 'AI智能推荐功能需要登录后使用，这样我们可以为您提供更个性化的推荐服务。是否前往登录？',
        showCancel: true,
        cancelText: '稍后再说',
        confirmText: '立即登录',
        confirmColor: '#007AFF',
        success: (res) => {
          if (res.confirm) {
            // 用户点击立即登录，跳转到登录页面，并传递当前页面路径
            const pages = getCurrentPages();
            const currentPage = pages[pages.length - 1];
            const currentPagePath = `/${currentPage.route}`;
            
            wx.navigateTo({
              url: `/pages/login/login?redirectTo=${encodeURIComponent(currentPagePath)}`,
              fail: (error) => {
                console.error('跳转到登录页面失败:', error);
                wx.showToast({
                  title: '页面跳转失败，请稍后重试',
                  icon: 'none'
                });
              }
            });
          } else {
            // 用户取消登录，可以记录用户行为或显示其他提示
            console.log('用户取消登录，继续浏览页面');
          }
        },
        fail: (error) => {
          console.error('显示登录提示弹窗失败:', error);
          // 如果弹窗显示失败，使用toast提示
          wx.showToast({
            title: '使用AI推荐功能需要先登录',
            icon: 'none',
            duration: 2000
          });
        }
      });
      return false;
    }
    
    return true;
  },



  /**
   * 开始轮询检查推荐状态
   */
  startPollingRecommendStatus: function() {
    // 清除可能存在的旧定时器
    if (this.data.pollInterval) {
      clearInterval(this.data.pollInterval);
    }

    // 创建新的轮询定时器
    const pollInterval = setInterval(() => {
      this.checkRecommendStatus();
    }, 3000); // 每3秒检查一次状态

    // 保存定时器ID
    this.setData({
      pollInterval: pollInterval
    });
  },

  /**
   * 检查推荐状态
   */
  checkRecommendStatus: async function() {
    const { taskId } = this.data;

    if (!taskId) {
      console.error('任务ID不存在');
      this.stopPolling();
      return;
    }

    try {
      // 调用API检查状态
      const { question } = require('../../../utils/api');
      const response = await question.checkAIRecommendStatus(taskId);

      if (response.success) {
        const statusData = response.data;
        console.log('推荐状态检查结果:', statusData);

        // 如果推荐已生成完成
        if (statusData.status === 'completed') {
          console.log('AI推荐已生成完成');

          // 停止轮询
          this.stopPolling();

          // 处理推荐结果
          this.handleRecommendSuccess(statusData.result.data);
        }
        // 如果推荐失败
        else if (statusData.status === 'failed') {
          console.error('AI推荐失败:', statusData.error);

          // 停止轮询
          this.stopPolling();

          this.setData({
            processing: false
          });

          wx.showToast({
            title: statusData.error || 'AI推荐失败，请重试',
            icon: 'none'
          });
        }
        else {
          // 仍在处理中，更新处理时间
          const processingTime = statusData.processingTime || 0;
          const seconds = Math.floor(processingTime / 1000);

          this.setData({
            processingMessage: `正在为您智能推荐产品... (${seconds}秒)`
          });

          console.log('AI推荐仍在处理中...', `已耗时: ${seconds}秒`);
        }
      } else {
        console.error('检查推荐状态失败:', response.message);
      }
    } catch (error) {
      console.error('检查推荐状态异常:', error.message);
    }
  },

  /**
   * 停止轮询
   */
  stopPolling: function() {
    if (this.data.pollInterval) {
      clearInterval(this.data.pollInterval);
      this.setData({
        pollInterval: null
      });
    }
  },

  /**
   * 处理推荐成功
   */
  handleRecommendSuccess: function(data) {
    console.log('AI推荐成功:', data);

    this.setData({
      loading: false,
      processing: false
    });

    // 触发原有的推荐成功处理逻辑
    this.onAiRecommend({
      detail: {
        success: true,
        data: data
      }
    });

    wx.showToast({
      title: 'AI推荐完成',
      icon: 'success'
    });
  },



  /**
   * 处理AI推荐结果
   */
  onAiRecommend: function(e) {
    const { success, data } = e.detail;

    if (!success || !data) {
      console.error('AI推荐事件数据格式错误:', e.detail);
      wx.showToast({
        title: '推荐数据格式错误',
        icon: 'none'
      });
      return;
    }

    console.log('AI推荐成功，原始数据:', data);

    try {
      // 从不同的数据结构中提取推荐产品列表
      let recommendedProducts = [];

      if (data.recommendedProducts && Array.isArray(data.recommendedProducts)) {
        recommendedProducts = data.recommendedProducts;
      } else if (data.products && Array.isArray(data.products)) {
        recommendedProducts = data.products;
      } else {
        console.error('推荐结果数据结构异常:', data);
        throw new Error('推荐结果数据格式错误');
      }

      if (recommendedProducts.length === 0) {
        console.warn('AI推荐返回的产品列表为空');
        throw new Error('没有找到合适的推荐产品，请调整筛选条件');
      }

      // 处理推荐产品数据
      const processedProducts = recommendedProducts.map((item, index) => {
        // 提取产品名称
        let productName = '';
        if (item.skuName && typeof item.skuName === 'string') {
          productName = item.skuName.trim();
        } else if (item.name && typeof item.name === 'string') {
          productName = item.name.trim();
        } else if (item.content && typeof item.content === 'string') {
          productName = item.content.trim();
        }

        if (!productName) {
          console.warn(`第${index + 1}个推荐产品名称为空:`, item);
          productName = `推荐产品${index + 1}`;
        }

        return {
          name: productName,
          hasRecommendReason: !!(item.recommendReason || item.highlights),
          recommendReason: item.recommendReason || '',
          highlights: item.highlights || [],
          expanded: false, // 默认不展开
          originalData: item // 保留原始数据用于其他操作
        };
      });

      console.log('处理后的推荐产品:', processedProducts);

      // 关闭推荐弹窗
      this.setData({
        showAiRecommendModal: false,
        loading: false
      });

      // 准备跳转到结果页面的数据
      const pageData = {
        recommendedProducts: processedProducts,
        questionInfo: this.data.questionInfo
      };

      // 跳转到AI推荐结果页面
      const dataParam = encodeURIComponent(JSON.stringify(pageData));
      wx.navigateTo({
        url: `/pages/product/ai-recommend-result/ai-recommend-result?data=${dataParam}`,
        success: () => {
          console.log('成功跳转到AI推荐结果页面');
          // 显示成功提示
          wx.showToast({
            title: `为您推荐了${processedProducts.length}款产品`,
            icon: 'success',
            duration: 2000
          });
        },
        fail: (error) => {
          console.error('跳转到AI推荐结果页面失败:', error);
          wx.showToast({
            title: '页面跳转失败，请重试',
            icon: 'none'
          });
        }
      });

    } catch (error) {
      console.error('处理AI推荐结果失败:', error);
      this.setData({
        loading: false,
        showAiRecommendModal: false
      });
      wx.showToast({
        title: error.message || '处理推荐结果失败，请重试',
        icon: 'none',
        duration: 3000
      });
    }
  },



  /**
   * 页面分享
   */
  onShareAppMessage: function () {
    return {
      title: 'AI智能推荐 - 选选',
      path: '/pages/product/ai-recommend/ai-recommend'
    };
  }
});