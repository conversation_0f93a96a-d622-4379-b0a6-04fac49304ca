/**
 * 配置服务 - 获取产品配置文件
 */

const path = require('path');
const fs = require('fs').promises;

class ConfigService {
  /**
   * 获取产品类型和品牌配置
   * @returns {Promise<Object>} 产品类型品牌配置数据
   */
  async getProductTypesBrands() {
    try {
      // 直接读取JSON配置文件
      const configPath = path.join(__dirname, '../../config/productTypesBrands.json');
      const configData = await fs.readFile(configPath, 'utf8');
      
      // 解析JSON并返回标准化响应
      const jsonData = JSON.parse(configData);
      
      return {
        success: true,
        data: jsonData
      };

    } catch (error) {
      console.error('获取产品配置失败:', error);
      return {
        success: false,
        message: `获取产品配置失败: ${error.message}`,
        error: error.message
      };
    }
  }
}

module.exports = new ConfigService();
