const app = getApp();
const { auth } = require('../../utils/api');
const util = require('../../utils/util');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    isLoggedIn: false,
    userInfo: null,
    // 功能入口显示控制配置
    featureConfig: {
      showMyVotes: false,        // 我的投票
      showMyCreated: false,      // 我发起的
      showMyCompleted: false,    // 已完成的
      showChangePhone: false,    // 手机号
      showAbout: false          // 关于我们
    }
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    const isLoggedIn = app.globalData.isLoggedIn;
    
    // 更新页面数据
    this.setData({
      isLoggedIn: isLoggedIn,
      userInfo: app.globalData.userInfo
    });
    
    // 已登录的处理
    if (isLoggedIn) {
      // 检查用户是否已登录且WebSocket确实未连接时，再尝试连接
      if (!app.globalData.socketConnected && !app.globalData.socketService?.isConnecting) {
        console.log('WebSocket未连接，尝试重新连接');
        app.connectSocket();
      }
      
      // 获取最新用户信息
      this.getUserInfo();
    }
  },

  /**
   * 获取用户信息
   */
  getUserInfo: function() {
    util.showLoading('加载中...');
    
    auth.getCurrentUser()
      .then(res => {
        util.hideLoading();
        
        // 只更新本地用户信息，不调用app.login避免WebSocket重连
        app.globalData.userInfo = res.data;
        wx.setStorageSync('userInfo', res.data);
        
        this.setData({
          userInfo: res.data
        });
      })
      .catch(err => {
        util.hideLoading();
        console.error('获取用户信息失败:', err);
        
        // 显示错误提示
        util.showToast('获取用户信息失败');
      });
  },

  /**
   * 跳转到登录页
   */
  goToLogin: function() {
    // 从用户页面跳转到登录，登录成功后应该回到用户页面
    wx.navigateTo({
      url: '/pages/login/login?redirectTo=%2Fpages%2Fuser%2Fuser'
    });
  },

  /**
   * 跳转到修改资料页面
   */
  goToProfile: function() {
    wx.navigateTo({
      url: '/pages/user/profile/profile'
    });
  },

  /**
   * 退出登录
   */
  logout: function() {
    util.showModal('提示', '确定要退出登录吗？')
      .then(confirmed => {
        if (confirmed) {
          util.showLoading('退出中...');
          
          auth.logout()
            .then(() => {
              // 清除登录状态
              app.logout();
              
              util.hideLoading();
              util.showToast('已退出登录', 'success');
              
              // 更新页面状态
              this.setData({
                isLoggedIn: false,
                userInfo: null
              });
            })
            .catch(err => {
              util.hideLoading();
              util.showToast(err.message || '退出失败，请稍后再试');
            });
        }
      });
  },

  /**
   * 跳转到我的投票页面
   */
  goToVotedQuestions: function() {
    wx.navigateTo({
      url: '/pages/user/voted-questions/voted-questions'
    });
  },

  /**
   * 跳转到我发起的页面
   */
  goToCreatedQuestions: function() {
    wx.navigateTo({
      url: '/pages/user/created-questions/created-questions'
    });
  },

  /**
   * 跳转到已完成的页面
   */
  goToClosedQuestions: function() {
    wx.navigateTo({
      url: '/pages/user/closed-questions/closed-questions'
    });
  },

  /**
   * 跳转到意见反馈页面
   */
  goToFeedback: function() {
    wx.navigateTo({
      url: '/pages/user/feedback/feedback'
    });
  },

  /**
   * 跳转到修改手机号页面
   */
  goToChangePhone: function() {
    wx.navigateTo({
      url: '/pages/user/change-phone/change-phone'
    });
  },

  /**
   * 跳转到关于我们页面
   */
  goToAbout: function() {
    wx.navigateTo({
      url: '/pages/user/about/about'
    });
  },

  /**
   * 跳转到AI对比产品历史页面
   */
  goToAiComparisonHistory: function() {
    wx.navigateTo({
      url: '/pages/user/ai-comparison-history/ai-comparison-history'
    });
  },

  /**
   * 更新功能配置
   * @param {Object} config 配置对象
   */
  updateFeatureConfig: function(config) {
    this.setData({
      featureConfig: {
        ...this.data.featureConfig,
        ...config
      }
    });
  },

  /**
   * 一键开启所有功能（用于调试或后续恢复）
   */
  enableAllFeatures: function() {
    this.updateFeatureConfig({
      showMyVotes: true,
      showMyCreated: true,
      showMyCompleted: true,
      showChangePhone: true,
      showAbout: true
    });
  },

  /**
   * 一键关闭指定功能（当前默认状态）
   */
  disableFeatures: function() {
    this.updateFeatureConfig({
      showMyVotes: false,
      showMyCreated: false,
      showMyCompleted: false,
      showChangePhone: false,
      showAbout: false
    });
  }
})