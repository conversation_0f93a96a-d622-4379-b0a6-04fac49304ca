<!--pages/user/voted-questions/voted-questions.wxml-->
<view class="page">
  <view class="container-fluid py-20">
    <!-- 问题列表 -->
    <view class="question-list">
      <block wx:if="{{questionList.length > 0}}">
        <question-item 
          wx:for="{{questionList}}" 
          wx:key="id" 
          question="{{item}}" 
          class="user-page-question-item mb-20"
          bind:questiontap="onQuestionTap">
        </question-item>
      </block>
      
      <!-- 空状态 -->
      <view class="empty-state text-center py-40" wx:if="{{!loading && questionList.length === 0}}">
        <image src="/assets/images/empty.png" class="empty-image mb-30"></image>
        <view class="font-size-medium text-secondary">您还没有参与任何投票，快去首页投票吧</view>
      </view>
      
      <!-- 加载状态 -->
      <view class="loading-state flex-center py-30" wx:if="{{loading && !refreshing}}">
        <view class="loading-spinner mr-10"></view>
        <view class="font-size-xs text-secondary">正在加载...</view>
      </view>
      
      <!-- 没有更多 -->
      <view class="no-more text-center py-30" wx:if="{{!hasMore && questionList.length > 0}}">
        <view class="font-size-xs text-light">— 已经到底了 —</view>
      </view>
    </view>
  </view>
</view> 