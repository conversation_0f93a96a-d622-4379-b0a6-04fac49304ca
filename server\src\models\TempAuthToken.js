const mongoose = require('mongoose');

/**
 * 临时认证令牌模型
 * 用于phoneAuth成功后，在completeRegister时验证用户身份
 */
const TempAuthTokenSchema = new mongoose.Schema({
  phone: {
    type: String,
    required: true,
    index: true
  },
  token: {
    type: String,
    required: true,
    unique: true,
    index: true
  },
  purpose: {
    type: String,
    enum: ['register'],
    required: true
  },
  isUsed: {
    type: Boolean,
    default: false
  },
  createdAt: {
    type: Date,
    default: Date.now,
    expires: 300 // 5分钟自动过期
  },
  usedAt: {
    type: Date
  }
});

// 复合索引
TempAuthTokenSchema.index({ phone: 1, purpose: 1, isUsed: 1 });
TempAuthTokenSchema.index({ token: 1, isUsed: 1 });

const TempAuthToken = mongoose.model('TempAuthToken', TempAuthTokenSchema);

module.exports = TempAuthToken; 