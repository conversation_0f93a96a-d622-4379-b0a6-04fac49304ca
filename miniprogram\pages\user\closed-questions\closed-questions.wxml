<!--pages/user/closed-questions/closed-questions.wxml-->
<view class="page">
  <view class="container p-30">
    <!-- 问题列表 -->
    <view class="question-list pb-30">
      <block wx:if="{{questionList.length > 0}}">
        <question-item 
          wx:for="{{questionList}}" 
          wx:key="id" 
          question="{{item}}" 
          class="user-page-question-item"
          bind:questiontap="onQuestionTap">
        </question-item>
      </block>
      
      <!-- 空状态 -->
      <view class="empty-container" wx:if="{{!loading && questionList.length === 0}}">
        <view class="empty-icon large">
          <image src="/assets/images/empty.png"></image>
        </view>
        <view class="empty-text">您还没有已完成的问题</view>
      </view>
      
      <!-- 加载状态 -->
      <view class="loading-container" wx:if="{{loading && !refreshing}}">
        <view class="loading-spinner"></view>
        <view class="loading-text">正在加载...</view>
      </view>
      
      <!-- 没有更多 -->
      <view class="no-more" wx:if="{{!hasMore && questionList.length > 0}}">
        — 已经到底了 —
      </view>
    </view>
  </view>
</view> 