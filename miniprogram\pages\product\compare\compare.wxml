<!--pages/product/compare/compare.wxml-->
<view class="page">
  <view class="container p-30">
    
    <!-- 页面头部说明 -->
    <view class="header-section mb-30">
      <text class="page-title text-dark mb-10">产品参数对比</text>
      <text class="page-desc text-secondary">输入产品关键词智能搜索，点击选择要对比的产品，最多支持5个产品同时对比</text>
    </view>

    <!-- 产品输入区域 -->
    <view class="input-section card p-30 mb-30">
      <view class="section-title mb-20">
        <text class="title-text text-dark">添加对比产品</text>
        <text class="title-count text-secondary">({{productInputs.length}}/5)</text>
      </view>
      
      <!-- 产品输入列表 -->
      <view class="product-inputs">
        <view class="input-item mb-15" wx:for="{{productInputs}}" wx:key="index">
          <view class="input-row">
            <view class="search-input-wrap flex-1">
              <product-search-input
                value="{{item}}"
                placeholder="请输入产品名称，如：iPhone 15 Pro"
                data-index="{{index}}"
                bindinput="onProductInput"
                bindselect="onProductSelect"
                bindclear="onProductClear"
                limit="{{8}}"
                clearable="{{true}}"
              />
            </view>
            <view class="remove-btn-wrap" wx:if="{{productInputs.length > 2}}">
              <view class="delete-btn" bindtap="removeProduct" data-index="{{index}}">
                <text class="iconfont icon-close"></text>
              </view>
            </view>
          </view>
        </view>
      </view>
      
      <!-- 添加产品按钮 -->
      <button 
        class="btn btn-outline btn-full add-product-btn mt-20" 
        bindtap="addProduct"
        disabled="{{productInputs.length >= 5}}">
        <text class="iconfont icon-add mr-10"></text>
        <text>添加对比产品</text>
      </button>
    </view>

    <!-- 操作按钮区域 -->
    <view class="px-30 mb-30">
      <button 
        class="btn btn-primary btn-full compare-btn" 
        bindtap="startCompare"
        disabled="{{comparing || !canCompare}}"
        loading="{{comparing}}">
        {{comparing ? '对比中...' : '开始对比'}}
      </button>
    </view>

    <!-- 对比结果区域 -->
    <view class="result-section" wx:if="{{compareResult}}">
      <!-- 🆕 价格参考性提示 -->
      <view class="price-disclaimer card p-30 mb-30">
        <view class="disclaimer-header mb-15">
          <view class="disclaimer-icon">
            <text class="iconfont icon-info">ℹ</text>
          </view>
          <text class="disclaimer-title">价格参考说明</text>
        </view>
        <view class="disclaimer-content">
          <text class="disclaimer-text">产品参数来源于官方渠道，但价格仅供参考，不代表实时市场价格。建议您在购买前查看各大电商平台的最新价格信息。</text>
        </view>
      </view>
      
      <!-- 产品信息展示 -->
      <view class="products-overview card p-30 mb-30">
        <view class="section-title mb-20">
          <text class="title-text text-dark">对比产品</text>
          <text class="title-count text-secondary" wx:if="{{compareResult.productType}}">({{compareResult.productType}})</text>
        </view>
        <scroll-view class="products-scroll" scroll-x="true" show-scrollbar="false">
          <view class="products-list">
            <view class="product-card" wx:for="{{compareResult.products}}" wx:key="id">
              <image class="product-image" src="{{item.image}}" mode="aspectFit" />
              
              <!-- 🆕 产品名称显示优化 -->
              <text class="product-name">{{item.displayName || item.name}}</text>
              
              <!-- 🆕 价格显示优化 -->
              <view class="price-info">
                <text class="product-price">¥{{item.price}}</text>
                <!-- 如果有价格范围且不同于单一价格，显示价格范围 -->
                <text class="price-range" wx:if="{{item.priceRange && item.priceRange.min !== item.priceRange.max}}">
                  (¥{{item.priceRange.min}} - ¥{{item.priceRange.max}})
                </text>
              </view>
              
              <text class="product-brand">{{item.brand}}</text>
              
              <!-- 🆕 配置信息显示 -->
              <view class="config-info" wx:if="{{item.defaultConfigDetails}}">
                <view class="config-item" wx:if="{{item.defaultConfigDetails.ram}}">
                  <text class="config-label">内存:</text>
                  <text class="config-value">{{item.defaultConfigDetails.ram}}</text>
                </view>
                <view class="config-item" wx:if="{{item.defaultConfigDetails.storage}}">
                  <text class="config-label">存储:</text>
                  <text class="config-value">{{item.defaultConfigDetails.storage}}</text>
                </view>
              </view>
              
              <!-- 🆕 多配置选择器（如果有多个配置） -->
              <view class="config-selector" wx:if="{{item.configurations.length > 1}}">
                <picker range="{{item.configurations}}" range-key="name" 
                        value="{{item.selectedConfigIndex || 0}}"
                        bindchange="onConfigChange" 
                        data-product-id="{{item.id}}">
                  <view class="selector-display">
                    <text class="selector-text">选择配置</text>
                    <text class="iconfont icon-arrow-down"></text>
                  </view>
                </picker>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>

      <!-- 参数对比表格 -->
      <view class="comparison-table card p-30 mb-30" wx:if="{{compareResult.comparisonTable && compareResult.comparisonTable.length > 0}}">
        <view class="section-title mb-20">
          <text class="title-text text-dark">详细对比</text>
        </view>
        
        <!-- 🔧 修复：使用scroll-view组件实现水平滚动 -->
        <scroll-view 
          class="table-scroll-container" 
          scroll-x="true" 
          show-scrollbar="true"
          enhanced="true"
          scroll-with-animation="true">
          <view class="table-container" style="--product-count: {{compareResult.products.length}};">
            <view class="table-header">
              <view class="table-cell param-header">参数</view>
              <view class="table-cell product-header" wx:for="{{compareResult.products}}" wx:key="id">
                {{item.displayName || item.name}}
              </view>
              <view class="table-cell analysis-header">AI分析</view>
            </view>
            
            <view class="table-body">
              <view class="table-row" wx:for="{{compareResult.comparisonTable}}" wx:key="parameter">
                <view class="table-cell param-cell">
                  <text class="param-name">{{item.parameter}}</text>
                </view>
                <view class="table-cell value-cell" wx:for="{{item.values}}" wx:for-item="value" wx:key="productId">
                  <text class="param-value">{{value.value}}</text>
                </view>
                <view class="table-cell analysis-cell">
                  <text class="analysis-text">{{analysisMap[item.parameter] || '-'}}</text>
                </view>
              </view>
            </view>
          </view>
        </scroll-view>
      </view>

      <!-- 如果没有详细对比数据，显示提示 -->
      <view class="no-comparison-tip card p-30 mb-30" wx:if="{{!compareResult.comparisonTable || compareResult.comparisonTable.length === 0}}">
        <view class="tip-card p-40">
          <text class="tip-title mb-10">暂无详细参数对比</text>
          <text class="tip-desc">当前产品数据不支持详细参数对比，只能显示基本信息</text>
        </view>
      </view>
    </view>

    <!-- 空状态 -->
    <view class="empty-state card text-center py-60 px-30" wx:if="{{!compareResult && !comparing}}">
      <image src="/assets/images/empty.png" mode="aspectFit" class="empty-image"></image>
      <text class="empty-text text-secondary mt-20">请输入产品名称开始对比</text>
    </view>

    <!-- 错误提示 -->
    <view class="error-tip p-30 mt-30" wx:if="{{errorMessage}}">
      <text class="error-text">{{errorMessage}}</text>
    </view>

    <!-- 友好错误提示 -->
    <view class="friendly-error-card card p-30 mt-30" wx:if="{{showFriendlyError}}">
      <view class="error-header mb-20">
        <view class="error-icon-inline">
          <text class="iconfont icon-info">ℹ</text>
        </view>
        <view class="error-content">
          <view class="error-title">产品信息暂未收录</view>
          <view class="error-desc">
            很抱歉，以下产品暂未收录到我们的数据库中：
          </view>
        </view>
        <view class="close-btn-inline" bindtap="closeFriendlyError">
          <text class="iconfont icon-close">×</text>
        </view>
      </view>
      
      <view class="missing-products mb-20">
        <view class="missing-item" wx:for="{{missingProducts}}" wx:key="index">
          <text class="item-number">{{index + 1}}.</text>
          <text class="item-name">{{item}}</text>
        </view>
      </view>
      
      <view class="help-desc mb-30">
        <text class="help-text">您可以通过意见反馈告诉我们这些产品信息，帮助我们完善数据库，让更多用户受益！</text>
      </view>
      
      <view class="card-actions">
        <button class="btn btn-secondary mr-15" bindtap="closeFriendlyError">
          暂不反馈
        </button>
        <button class="btn btn-primary flex-1" bindtap="goToFeedback">
          去反馈产品信息
        </button>
      </view>
    </view>
  </view>
</view> 