/**
 * 问题创建页面样式 - 已重构使用全局组件样式
 * Create Question Page Styles - Refactored with Global Components
 * 依赖：app.wxss 中导入的全局样式系统
 */

/* ==================== 页面布局 Page Layout ==================== */
.page-container {
  padding: 0 0 120rpx 0;
  background-color: var(--bg-color-secondary, #f5f5f5);
  min-height: 100vh;
  box-sizing: border-box;
}

.page-header {
  padding: 30rpx 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  position: relative;
}

.page-title {
  font-size: 36rpx;
  font-weight: bold;
  color: var(--text-color-primary, #333);
  margin-bottom: 10rpx;
}

.page-desc {
  font-size: 28rpx;
  color: var(--text-color-secondary, #666);
}

.form-container {
  padding: 30rpx;
}

/* ==================== 区块容器样式 Section Container ==================== */
/* 使用全局卡片样式，只保留页面特有的调整 */
.section-container {
  /* 继承全局 .card 样式 */
  background-color: #fff;
  border-radius: 12rpx;
  padding: 30rpx;
  margin-bottom: 30rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.section-header {
  margin-bottom: 30rpx;
  border-bottom: 1rpx solid var(--border-color-subtle, #eaeaea);
  padding-bottom: 20rpx;
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: var(--text-color-primary, #333);
}

/* ==================== 表单元素样式 Form Elements ==================== */
/* 移除重复的表单样式，使用全局表单组件 */
/* 特殊尺寸的输入框需要保留 */
.title-input {
  /* 继承 .form-input 基础样式，添加特定高度 */
  width: 100%;
  height: 88rpx;
  border: 1rpx solid var(--border-color-input, #ddd);
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  background-color: var(--bg-color-input, #fff);
  transition: all 0.3s ease;
}

/* 原始选项输入框样式 - 已替换为智能产品输入框组件，保留作为备用 */
/* 
.option-input {
  width: 100%;
  height: 88rpx;
  border: 1rpx solid var(--border-color-input, #ddd);
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  background-color: var(--bg-color-input, #fff);
  transition: all 0.3s ease;
}
*/

.title-input:focus {
  border-color: var(--primary-color, #3B7ADB);
  background-color: var(--bg-color-input-focus, #fff);
  box-shadow: 0 0 0 4rpx rgba(59, 122, 219, 0.1);
}

/* 原始选项输入框焦点样式 - 已替换为智能产品输入框组件，保留作为备用 */
/*
.option-input:focus {
  border-color: var(--primary-color, #3B7ADB);
  background-color: var(--bg-color-input-focus, #fff);
  box-shadow: 0 0 0 4rpx rgba(59, 122, 219, 0.1);
}
*/

.scene-input {
  width: 100%;
  height: 200rpx;
  border: 1rpx solid var(--border-color-input, #ddd);
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  background-color: var(--bg-color-input, #fff);
  transition: all 0.3s ease;
}

.scene-input:focus {
  border-color: var(--primary-color, #3B7ADB);
  box-shadow: 0 0 0 4rpx rgba(59, 122, 219, 0.1);
}

.input-counter {
  font-size: 24rpx;
  color: var(--text-color-placeholder, #999);
  text-align: right;
  display: block;
  margin-top: 10rpx;
}

/* ==================== 标签组件样式 Tag Components ==================== */
.tags-container {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  margin-bottom: 10rpx;
}

/* 使用全局标签样式 */
.tag-item {
  /* 继承 .tag 基础样式，添加特定交互 */
  padding: 10rpx 30rpx;
  background-color: var(--bg-color-light, #f5f5f5);
  border-radius: 30rpx;
  font-size: 28rpx;
  color: var(--text-color-secondary, #666);
  border: 1rpx solid var(--border-color-subtle, #eaeaea);
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  cursor: pointer;
}

.tag-hover {
  opacity: 0.8;
  transform: scale(0.98);
}

.tag-item.active {
  background-color: var(--bg-color-btn-secondary, #e6f0ff) !important;
  color: var(--primary-color, #3B7ADB) !important;
  border-color: var(--primary-color, #3B7ADB) !important;
  font-weight: 500;
  box-shadow: 0 2rpx 8rpx rgba(59, 122, 219, 0.15);
}

.tag-check {
  margin-left: 6rpx;
  font-size: 24rpx;
  color: var(--primary-color, #3B7ADB);
}

/* ==================== 关键考量因素样式 Key Factors ==================== */
.factors-container {
  margin-bottom: 10rpx;
}

.factor-input-container {
  display: flex;
  margin-bottom: 20rpx;
}

.factor-input {
  width: 100%;
  height: 160rpx;
  border: 1rpx solid var(--border-color-input, #ddd);
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  background-color: var(--bg-color-input, #fff);
  transition: all 0.3s ease;
}

.factor-input:focus {
  border-color: var(--primary-color, #3B7ADB);
  box-shadow: 0 0 0 4rpx rgba(59, 122, 219, 0.1);
}

/* 使用全局按钮样式 */
.add-factor-btn {
  /* 继承 .btn .btn-primary 样式，调整尺寸 */
  width: 120rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  background-color: var(--primary-color, #3B7ADB);
  color: #fff;
  border-radius: 8rpx;
  font-size: 28rpx;
  margin-left: 20rpx;
  transition: all 0.3s ease;
  border: none;
}

.add-factor-btn:active {
  background-color: #2E63B8;
  transform: scale(0.98);
}

.factors-list {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
}

.factor-item {
  padding: 10rpx 20rpx;
  background-color: var(--bg-color-light, #f5f5f5);
  border-radius: 30rpx;
  font-size: 28rpx;
  color: var(--text-color-secondary, #666);
  display: flex;
  align-items: center;
}

.delete-factor {
  width: 40rpx;
  height: 40rpx;
  line-height: 40rpx;
  text-align: center;
  border-radius: 50%;
  margin-left: 10rpx;
  color: var(--text-color-placeholder, #999);
  font-size: 32rpx;
  transition: all 0.3s ease;
}

.delete-factor:active {
  color: var(--danger-color, #ff5151);
  transform: scale(0.9);
}

/* ==================== 预算区间样式 Budget Range ==================== */
.budget-container {
  display: flex;
  align-items: center;
}

.budget-input {
  width: 200rpx;
  height: 80rpx;
  border: 1rpx solid var(--border-color-input, #ddd);
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  background-color: var(--bg-color-input, #fff);
  transition: all 0.3s ease;
}

.budget-input:focus {
  border-color: var(--primary-color, #3B7ADB);
  box-shadow: 0 0 0 4rpx rgba(59, 122, 219, 0.1);
}

.budget-separator {
  margin: 0 20rpx;
  font-size: 28rpx;
  color: var(--text-color-secondary, #666);
}

.budget-currency {
  margin-left: 20rpx;
  font-size: 28rpx;
  color: var(--text-color-secondary, #666);
}

/* ==================== 通用提示文本样式 ==================== */
.tip-text {
  font-size: 24rpx;
  color: var(--text-color-placeholder, #999);
  margin-top: 10rpx;
}

/* ==================== 选项列表样式 Options List ==================== */
.options-list {
  margin-bottom: 30rpx;
}

.option-item {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

/* 智能产品输入框包装容器 */
.product-search-wrapper {
  flex: 1;
  margin-right: 20rpx;
}

.option-item .delete-btn-wrap {
  margin-left: 0;
}

/* 区域描述文本样式 */
.section-desc {
  font-size: 24rpx;
  color: var(--text-color-secondary, #666);
  margin-top: 10rpx;
  line-height: 1.4;
}

/* 注意：删除按钮样式已移至全局组件样式 components.wxss 中的 .delete-btn */

.add-option {
  display: flex;
  align-items: center;
  color: var(--primary-color, #3B7ADB);
  font-size: 28rpx;
  margin-bottom: 20rpx;
  transition: all 0.3s ease;
}

.add-option:active {
  opacity: 0.7;
  transform: scale(0.98);
}

.icon-add {
  margin-right: 10rpx;
  color: var(--primary-color, #3B7ADB);
}

.tips {
  font-size: 24rpx;
  color: var(--text-color-placeholder, #999);
}

/* ==================== 选择器样式 Picker Styles ==================== */
.picker-view {
  height: 88rpx;
  border: 1rpx solid var(--border-color-input, #ddd);
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: var(--bg-color-input, #fff);
  transition: all 0.3s ease;
}

.picker-view:active {
  border-color: var(--primary-color, #3B7ADB);
  box-shadow: 0 0 0 4rpx rgba(59, 122, 219, 0.1);
}

/* ==================== Switch 开关组样式 ==================== */
.switch-group {
  margin-top: 30rpx;
}

.switch-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30rpx;
}

.switch-label {
  font-size: 28rpx;
  color: var(--text-color-primary, #333);
}

/* ==================== 投票方式选择样式 Vote Options ==================== */
.vote-options {
  display: flex;
  gap: 30rpx;
  margin-top: 10rpx;
}

.vote-option {
  padding: 16rpx 30rpx;
  background-color: var(--bg-color-light, #f5f5f5);
  border-radius: 8rpx;
  display: flex;
  align-items: center;
  gap: 10rpx;
  color: var(--text-color-secondary, #666);
  font-size: 28rpx;
  border: 1rpx solid transparent;
  transition: all 0.3s ease;
}

.vote-option.active {
  background-color: #e6f7ff;
  color: #1890ff;
  border-color: #91d5ff;
}

.option-text {
  font-size: 28rpx;
}

.option-check {
  color: #1890ff;
  font-weight: bold;
}

/* ==================== 可见性设置样式 Visibility Settings ==================== */
.visibility-group {
  margin-bottom: 30rpx;
}

.radio-item {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  font-size: 28rpx;
  color: var(--text-color-primary, #333);
}

/* ==================== 筛选条件设置样式 Filter Settings ==================== */
.filter-settings {
  border-top: 1rpx solid var(--border-color-subtle, #eaeaea);
  padding-top: 30rpx;
}

.filter-section {
  margin-bottom: 30rpx;
}

.filter-title {
  font-size: 28rpx;
  color: var(--text-color-primary, #333);
  margin-bottom: 10rpx;
  font-weight: 500;
}

.filter-tip {
  font-size: 24rpx;
  color: var(--text-color-placeholder, #999);
  margin-bottom: 20rpx;
}

.checkbox-item {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
  font-size: 28rpx;
  color: var(--text-color-primary, #333);
}

/* ==================== 年龄筛选样式 Age Filter ==================== */
.age-filter {
  display: flex;
  align-items: center;
}

.age-input {
  width: 200rpx;
  height: 88rpx;
  border: 1rpx solid var(--border-color-input, #ddd);
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  background-color: var(--bg-color-input, #fff);
  transition: all 0.3s ease;
}

.age-input:focus {
  border-color: var(--primary-color, #3B7ADB);
  box-shadow: 0 0 0 4rpx rgba(59, 122, 219, 0.1);
}

.separator {
  margin: 0 20rpx;
  font-size: 28rpx;
  color: var(--text-color-secondary, #666);
}

/* ==================== 已选择项目展示样式 Selected Items ==================== */
.selected-items {
  display: flex;
  flex-wrap: wrap;
  gap: 20rpx;
  margin-bottom: 20rpx;
}

.selected-item {
  padding: 10rpx 20rpx;
  background-color: var(--bg-color-light, #f5f5f5);
  border-radius: 30rpx;
  font-size: 24rpx;
  color: var(--text-color-secondary, #666);
  display: flex;
  align-items: center;
}

.remove-item {
  margin-left: 10rpx;
  color: var(--text-color-placeholder, #999);
  font-size: 28rpx;
  transition: all 0.3s ease;
}

.remove-item:active {
  color: var(--danger-color, #ff5151);
  transform: scale(0.9);
}

.region-selector, 
.occupation-selector {
  height: 88rpx;
  border: 1rpx solid var(--border-color-input, #ddd);
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  display: flex;
  align-items: center;
  justify-content: space-between;
  background-color: var(--bg-color-input, #fff);
  transition: all 0.3s ease;
}

.region-selector:active,
.occupation-selector:active {
  border-color: var(--primary-color, #3B7ADB);
  box-shadow: 0 0 0 4rpx rgba(59, 122, 219, 0.1);
}

/* ==================== 提交按钮样式 Submit Button ==================== */
.submit-container {
  padding: 30rpx;
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  background-color: #fff;
  box-shadow: 0 -2rpx 10rpx rgba(0, 0, 0, 0.05);
  z-index: 100;
}

/* 使用全局按钮样式 */
.submit-btn {
  /* 继承 .btn .btn-primary .btn-full 样式 */
  width: 100%;
  height: 88rpx;
  background: linear-gradient(135deg, var(--primary-color, #3B7ADB) 0%, #5A67D8 100%);
  color: #fff;
  font-size: 32rpx;
  border-radius: 44rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
  border: none;
  box-shadow: 0 4rpx 12rpx rgba(59, 122, 219, 0.25);
}

.submit-btn:active {
  background: linear-gradient(135deg, #2E63B8 0%, #4C51BF 100%);
  transform: scale(0.98);
  box-shadow: 0 2rpx 6rpx rgba(59, 122, 219, 0.3);
}

.submit-btn.disabled {
  background-color: #ccc !important;
  transform: none !important;
  box-shadow: none !important;
  pointer-events: none;
}

/* ==================== 模板入口样式 Template Entry ==================== */
.template-entry {
  position: absolute;
  right: 30rpx;
  top: 40rpx;
  display: flex;
  align-items: center;
  background-color: #f0f9ff;
  border-radius: 30rpx;
  padding: 12rpx 20rpx;
  font-size: 24rpx;
  color: #1890ff;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
}

.template-entry:active {
  background-color: #e6f7ff;
  transform: scale(0.98);
}

.template-entry text {
  margin-left: 6rpx;
}

.icon-template {
  font-size: 28rpx;
}

/* ==================== 模板弹窗样式 Template Modal ==================== */
.template-modal {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0;
  visibility: hidden;
  transition: all 0.3s ease;
}

.template-modal.show {
  opacity: 1;
  visibility: visible;
}

.modal-content {
  width: 90%;
  max-height: 80%;
  background-color: #fff;
  border-radius: 12rpx;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.modal-header {
  padding: 30rpx;
  border-bottom: 1rpx solid var(--border-color-subtle, #eaeaea);
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.modal-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-color-primary, #333);
}

.close-btn {
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 40rpx;
  color: var(--text-color-placeholder, #999);
  transition: all 0.3s ease;
}

.close-btn:active {
  color: var(--text-color-secondary, #666);
  transform: scale(0.9);
}

.modal-body {
  flex: 1;
  overflow-y: auto;
  padding: 30rpx;
}

.modal-footer {
  padding: 20rpx 30rpx;
  border-top: 1rpx solid var(--border-color-subtle, #eaeaea);
  display: flex;
  justify-content: space-between;
}

/* 使用全局按钮样式 */
.close-modal-btn {
  /* 继承 .btn .btn-secondary 样式 */
  height: 80rpx;
  line-height: 80rpx;
  background-color: var(--bg-color-btn-light, #f5f5f5);
  color: var(--text-color-secondary, #666);
  font-size: 28rpx;
  border-radius: 8rpx;
  flex: 1;
  margin-left: 20rpx;
  border: none;
  transition: all 0.3s ease;
}

.close-modal-btn:active {
  background-color: var(--bg-color-btn-light-active, #eaecf0);
  transform: scale(0.98);
}

.use-template-btn {
  /* 继承 .btn .btn-primary 样式 */
  height: 80rpx;
  line-height: 80rpx;
  background-color: var(--primary-color, #3B7ADB);
  color: #fff;
  font-size: 28rpx;
  border-radius: 8rpx;
  flex: 1;
  border: none;
  transition: all 0.3s ease;
}

.use-template-btn:active {
  background-color: #2E63B8;
  transform: scale(0.98);
}

/* ==================== 模板示例样式 Template Example ==================== */
.template-example {
  background-color: #f9f9f9;
  border-radius: 16rpx;
  padding: 30rpx;
}

.example-header {
  margin-bottom: 30rpx;
}

.example-title {
  font-size: 32rpx;
  font-weight: bold;
  color: var(--text-color-primary, #333);
}

.example-item {
  margin-bottom: 30rpx;
}

.example-label {
  font-size: 28rpx;
  color: var(--text-color-secondary, #666);
  margin-bottom: 10rpx;
  display: block;
}

.example-content {
  font-size: 30rpx;
  color: var(--text-color-primary, #333);
  line-height: 1.5;
  white-space: pre-wrap;
  display: block;
}

.example-options {
  display: flex;
  flex-direction: column;
}

.example-tags {
  display: flex;
  flex-wrap: wrap;
}

.tag-text {
  font-size: 28rpx;
  color: #1890ff;
  background-color: #e6f7ff;
  border-radius: 20rpx;
  padding: 6rpx 16rpx;
  margin-right: 16rpx;
  margin-bottom: 16rpx;
}

/* ==================== 持续时间选择器样式 Duration Picker ==================== */
.duration-container {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-top: 10rpx;
}

.duration-input-group {
  display: flex;
  flex-direction: row;
  align-items: center;
  margin-right: 30rpx;
}

.duration-input {
  width: 120rpx;
  height: 70rpx;
  background-color: var(--bg-color-light, #f5f5f5);
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  text-align: center;
  border: 1rpx solid var(--border-color-input, #ddd);
  transition: all 0.3s ease;
}

.duration-input:focus {
  border-color: var(--primary-color, #3B7ADB);
  background-color: var(--bg-color-input-focus, #fff);
  box-shadow: 0 0 0 4rpx rgba(59, 122, 219, 0.1);
}

.duration-unit {
  margin-left: 10rpx;
  font-size: 28rpx;
  color: var(--text-color-primary, #333);
}

.duration-tip {
  font-size: 26rpx;
  color: var(--text-color-secondary, #666);
  margin-bottom: 15rpx;
}

.expiry-preview {
  font-size: 26rpx;
  color: var(--primary-color, #3B7ADB);
  margin-top: 15rpx;
  background-color: #f0f7ff;
  padding: 12rpx 20rpx;
  border-radius: 8rpx;
  display: inline-block;
}

/* ==================== AI推荐区域样式 AI Recommend Section ==================== */
.ai-recommend-section {
  margin-bottom: 32rpx;
  padding: 24rpx;
  background: linear-gradient(135deg, #f8f9ff 0%, #f0f8ff 100%);
  border-radius: 16rpx;
  border: 1rpx solid #e8f4ff;
}

.recommend-tip {
  display: flex;
  align-items: center;
  margin-bottom: 20rpx;
}

.tip-icon {
  font-size: 32rpx;
  margin-right: 12rpx;
}

.tip-text {
  font-size: 26rpx;
  color: #666;
  line-height: 1.4;
}

.ai-recommend-btn {
  width: 100%;
  height: 80rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #007aff 0%, #005ce6 100%);
  color: #fff;
  border: none;
  border-radius: 40rpx;
  font-size: 28rpx;
  box-shadow: 0 4rpx 16rpx rgba(0, 122, 255, 0.3);
  transition: all 0.2s ease;
}

.ai-recommend-btn:active {
  transform: translateY(1rpx);
  box-shadow: 0 2rpx 8rpx rgba(0, 122, 255, 0.3);
}

.btn-icon {
  font-size: 28rpx;
  margin-right: 8rpx;
}

.btn-text {
  font-weight: 500;
}

/* ==================== AI推荐理由展示样式 AI Recommend Reason ==================== */
.ai-recommend-reason {
  margin-top: 10rpx;
  background-color: #f8fcff;
  border: 1rpx solid #e6f7ff;
  border-radius: 8rpx;
  padding: 16rpx;
  font-size: 24rpx;
}

.reason-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12rpx;
}

.ai-tag {
  color: #1890ff;
  font-size: 22rpx;
  font-weight: 500;
  display: flex;
  align-items: center;
}

.expand-btn {
  color: #1890ff;
  font-size: 22rpx;
  padding: 4rpx 8rpx;
  border-radius: 4rpx;
  background-color: rgba(24, 144, 255, 0.1);
  transition: all 0.3s ease;
}

.expand-btn:active {
  opacity: 0.7;
  transform: scale(0.95);
}

.reason-content {
  transition: all 0.3s ease;
  overflow: hidden;
}

.reason-content.collapsed {
  max-height: 60rpx;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.reason-content.expanded {
  max-height: none;
}

.recommend-reason {
  margin-bottom: 12rpx;
}

.reason-text {
  color: #595959;
  line-height: 1.5;
  font-size: 24rpx;
}

.highlights-section {
  margin-top: 8rpx;
}

.highlights-title {
  color: #262626;
  font-weight: 500;
  font-size: 24rpx;
  margin-bottom: 8rpx;
  display: block;
}

.highlights-list {
  display: flex;
  flex-direction: column;
  gap: 4rpx;
}

.highlight-item {
  color: #595959;
  font-size: 22rpx;
  line-height: 1.4;
  padding-left: 8rpx;
}

/* ==================== 区域描述文本样式 ==================== */ 