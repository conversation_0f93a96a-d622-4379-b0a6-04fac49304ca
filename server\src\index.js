/**
 * PetSocial 服务器入口文件
 * 此文件负责启动API服务器和WebSocket服务器
 */

const dotenv = require('dotenv');
const { initUploadDirs } = require('./services/fileService');
const { initScheduleTasks } = require('./utils/scheduleService');

// 加载环境变量
dotenv.config();

console.log('===============================================');
console.log('🚀 正在启动选选后端服务系统...');
console.log('===============================================');

// 初始化上传目录
initUploadDirs()
  .then(() => console.log('✅ 上传目录初始化成功'))
  .catch(err => console.error('❌ 上传目录初始化失败:', err));

// 首先启动API服务器
console.log('🔄 正在启动HTTP API服务器...');
require('./server');

// 然后启动WebSocket服务器
console.log('🔄 正在启动WebSocket服务器...');
require('./socketServer');

// 初始化定时任务
// initScheduleTasks();

// 设置进程异常处理
process.on('uncaughtException', (error) => {
  console.error('❌ 未捕获的异常:', error);
});

process.on('unhandledRejection', (error) => {
  console.error('❌ 未处理的Promise拒绝:', error);
});

console.log('===============================================');
console.log('✨ 选选后端服务系统已完全启动');
console.log('===============================================');