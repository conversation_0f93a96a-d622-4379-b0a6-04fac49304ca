/**
 * 选选小程序 - 全局样式文件
 * Global Styles for XuanXuan Mini Program
 */

/* ==================== 样式文件导入 Import Styles ==================== */
/* 按依赖顺序导入：变量 → 基础样式 → 组件 → 布局 → 工具类 → 图标字体 */
@import './styles/variables.wxss';
@import './styles/base.wxss';
@import './styles/components.wxss';
@import './styles/layout.wxss';
@import './styles/utilities.wxss';
@import './assets/iconfont.wxss';

/* ==================== 应用特有的样式 App-specific Styles ==================== */

/* 次要按钮样式（白底蓝边） */
.btn-outline {
  background-color: #f0f4ff;
  color: #3B7ADB;
  border: 2rpx solid #3B7ADB;
  /* 添加阴影提升可识别性 */
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.08);
}

.btn-outline:active {
  background-color: #e6efff;
  transform: scale(0.98);
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.1);
}

/* ==================== 全局下拉框层级保护 Global Dropdown Z-index Protection ==================== */

/* 确保所有产品搜索下拉框在全局范围内都有最高优先级 */
.product-search-input .suggestions-dropdown {
  z-index: 99999 !important;
  /* 在某些Android设备上，使用fixed定位可以更好地处理层级问题 */
  position: fixed !important;
  /* 通过JavaScript动态计算位置，这里提供基础样式支持 */
}

/* 为获得焦点的搜索输入组件提供全局层级保护 */
.product-search-input.focused {
  z-index: 99998 !important;
  position: relative;
}

/* ==================== 微信小程序特有样式重置 WeChat Override ==================== */

/* 按钮样式重置 */
button::after {
  border: none;
}

/* 输入框占位符颜色 */
input::-webkit-input-placeholder,
textarea::-webkit-input-placeholder {
  color: #999999;
} 