<!--pages/product/product_library/product_library.wxml-->
<view class="container p-20">
  <!-- 搜索区域 -->
  <view class="search-section card mb-20">
    <view class="card-header">
      <view class="search-title card-title">关键字搜索</view>
    </view>

    <view class="card-body">
      <view class="search-row">
        <view class="search-input-container">
          <input
            class="search-input"
            type="text"
            placeholder="请输入产品关键字..."
            value="{{searchKeyword}}"
            bindinput="onSearchInput"
            bindconfirm="onSearchConfirm"
            maxlength="50"
          />
        </view>
        <button
          class="btn btn-primary search-btn"
          bindtap="onSearchButtonTap"
          disabled="{{loading || searchKeyword === ''}}"
        >
          {{loading ? '搜索中...' : '搜索'}}
        </button>
      </view>
    </view>
  </view>

  <!-- 筛选区域 -->
  <view class="filter-section card mb-20">
    <view class="card-header">
      <view class="filter-title card-title">产品筛选</view>
    </view>
    
    <view class="card-body">
      <!-- 产品类型选择 -->
      <view class="filter-row mb-30">
        <view class="filter-label form-label">产品类型</view>
        <view class="filter-options mb-10">
          <view 
            class="option-item {{item.value === selectedProductType ? 'selected' : ''}}"
            wx:for="{{productTypeOptions}}" 
            wx:key="value"
            data-value="{{item.value}}"
            data-index="{{index}}"
            bindtap="onProductTypeSelect"
          >
            {{item.label}}
          </view>
        </view>
      </view>

      <!-- 品牌选择 -->
      <view class="filter-row mb-30">
        <view class="filter-label form-label">品牌</view>
        <view class="filter-options {{brandOptions.length === 0 ? 'disabled' : ''}} mb-10">
          <view 
            class="option-item {{item.value === selectedBrand ? 'selected' : ''}}"
            wx:for="{{brandOptions}}" 
            wx:key="value"
            data-value="{{item.value}}"
            data-index="{{index}}"
            bindtap="onBrandSelect"
          >
            {{item.label}}
          </view>
          <view class="no-options" wx:if="{{brandOptions.length === 0}}">
            请先选择产品类型
          </view>
        </view>
      </view>

      <!-- 上市年份选择 -->
      <view class="filter-row mb-30">
        <view class="filter-label form-label">上市年份</view>
        <view class="filter-options mb-10">
          <view 
            class="option-item {{item.value === selectedYear ? 'selected' : ''}}"
            wx:for="{{yearOptions}}" 
            wx:key="value"
            data-value="{{item.value}}"
            data-index="{{index}}"
            bindtap="onYearSelect"
          >
            {{item.label}}
          </view>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="filter-buttons mt-30">
        <button class="btn btn-primary btn-medium btn-full" bindtap="searchProducts" disabled="{{loading}}">
          {{loading ? '搜索中...' : '搜索产品'}}
        </button>
        <button class="btn btn-secondary btn-medium btn-full" bindtap="resetFilters">重置筛选</button>
      </view>
    </view>
  </view>

  <!-- 产品列表区域 -->
  <view class="products-section card">
    <view class="card-header">
      <view class="products-header mb-30 pb-20">
        <view class="products-title card-title">产品列表</view>
        <view class="products-count" wx:if="{{products.length > 0 || totalCount > 0}}">
          共{{totalCount || 0}}个产品
        </view>
      </view>
    </view>

    <view class="card-body">
      <!-- 使用产品展示组件 -->
      <product-show
        products="{{products}}"
        loading="{{loading}}"
        loadingMore="{{loadingMore}}"
        hasSearched="{{hasSearched}}"
        totalCount="{{totalCount}}"
        hasMore="{{hasMore}}"
        scrollTop="{{scrollTop}}"
        detailPagePath="/pages/product/product_detail/product_detail"
        bind:productTap="onProductTap"
        bind:imageError="onImageError"
        bind:loadMore="onLoadMoreClick"
        bind:scrollToLower="onScrollToLower"
        bind:compareToggle="onCompareToggle"
      ></product-show>
    </view>
  </view>

  <!-- 产品对比输入组件 -->
  <product-compare-input
    visible="{{compareVisible}}"
    bind:toggleVisible="onToggleCompareVisible"
  ></product-compare-input>
</view>
