const mongoose = require('mongoose');
const Product = require('../src/models/Product');

// 导入产品数据
const budgetPhones2025 = require('../src/data/phones/2025-1000-2000');
const phones2025 = require('../src/data/phones2025');
const laptops2025 = require('../src/data/laptops2025');

// 连接数据库
const connectDB = async () => {
  try {
    await mongoose.connect(process.env.MONGODB_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('MongoDB 连接成功');
  } catch (error) {
    console.error('MongoDB 连接失败:', error);
    process.exit(1);
  }
};

// 转换产品数据格式
const transformProductData = (products, productType) => {
  return products.map(product => {
    const transformed = {
      skuId: product.skuId,
      skuName: product.skuName,
      productType: productType,
      price: product.price,
      priceWan: product.priceWan,
      imageUrl: product.imageUrl,
      materialUrl: product.materialUrl,
      couponUrl: product.couponUrl || '',
      brandName: product.brandName,
      brandCode: product.brandCode,
      supportsComparison: true,
      productSpecs: {
        general: {
          brand: product.brand,
          model: product.model,
          color: product.color,
          weight: product.weight,
          dimensions: product.dimensions
        }
      }
    };

    // 根据产品类型添加专用参数
    if (productType === 'phone') {
      transformed.productSpecs.phone = {
        screenSize: product.screenSize,
        screenResolution: product.screenResolution,
        processor: product.processor,
        ram: product.ram,
        storage: product.storage,
        battery: product.battery,
        camera: {
          rear: product.rearCamera,
          front: product.frontCamera
        },
        operatingSystem: product.operatingSystem,
        network: product.network
      };
    } else if (productType === 'laptop') {
      transformed.productSpecs.laptop = {
        screenSize: product.screenSize,
        screenResolution: product.screenResolution,
        processor: product.processor,
        ram: product.ram,
        storage: product.storage,
        graphics: product.graphics,
        operatingSystem: product.operatingSystem,
        ports: product.ports,
        battery: product.battery
      };
    }

    return transformed;
  });
};

// 初始化产品数据
const initProductData = async () => {
  try {
    console.log('开始初始化产品数据...');

    // 清空现有产品数据
    await Product.deleteMany({});
    console.log('已清空现有产品数据');

    // 转换数据格式
    const phoneProducts = [];
    const laptopProducts = [];

    // 处理千元机数据
    if (budgetPhones2025 && budgetPhones2025.length > 0) {
      phoneProducts.push(...transformProductData(budgetPhones2025, 'phone'));
      console.log(`已处理千元机数据: ${budgetPhones2025.length} 个产品`);
    }

    // 处理旗舰手机数据
    if (phones2025 && phones2025.length > 0) {
      phoneProducts.push(...transformProductData(phones2025, 'phone'));
      console.log(`已处理旗舰手机数据: ${phones2025.length} 个产品`);
    }

    // 处理笔记本数据
    if (laptops2025 && laptops2025.length > 0) {
      laptopProducts.push(...transformProductData(laptops2025, 'laptop'));
      console.log(`已处理笔记本数据: ${laptops2025.length} 个产品`);
    }

    // 合并所有产品数据
    const allProducts = [...phoneProducts, ...laptopProducts];
    console.log(`总共需要插入 ${allProducts.length} 个产品`);

    // 批量插入产品数据
    if (allProducts.length > 0) {
      await Product.insertMany(allProducts);
      console.log('产品数据初始化完成');
      
      // 统计数据
      const phoneCount = await Product.countDocuments({ productType: 'phone' });
      const laptopCount = await Product.countDocuments({ productType: 'laptop' });
      
      console.log(`统计结果:`);
      console.log(`- 手机产品: ${phoneCount} 个`);
      console.log(`- 笔记本产品: ${laptopCount} 个`);
      console.log(`- 总计: ${phoneCount + laptopCount} 个`);
    } else {
      console.log('没有找到产品数据');
    }

  } catch (error) {
    console.error('初始化产品数据失败:', error);
    throw error;
  }
};

// 主函数
const main = async () => {
  try {
    await connectDB();
    await initProductData();
    console.log('产品数据初始化脚本执行完成');
  } catch (error) {
    console.error('脚本执行失败:', error);
  } finally {
    await mongoose.connection.close();
    console.log('数据库连接已关闭');
    process.exit(0);
  }
};

// 运行脚本
if (require.main === module) {
  main();
}

module.exports = {
  initProductData,
  transformProductData
}; 