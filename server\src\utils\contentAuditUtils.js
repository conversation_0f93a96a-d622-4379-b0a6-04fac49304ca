/**
 * 内容审核工具函数
 * 提供基础的内容审核能力，包括关键词过滤、链接检测等
 */

// 敏感词库 - 根据"选选"平台需求定制
const SENSITIVE_WORDS = [
  // 引流相关 - 禁止用户引流到其他平台
  '微信', 'wx', 'WeChat', 'vx', 'VX', 'wechat', 'WECHAT',
  'QQ', 'qq', 'Qq', 'qQ', 'q群', 'Q群',
  '加我', '私聊', '联系我', '找我', '咨询我', '详聊', 
  '加群', '群号', '群聊', '拉群', '进群',
  '二维码', '扫码', '扫一扫', '识别二维码',
  '关注', '粉丝', '推广', '引流', '导流',
  
  // 网站和外部平台引流
  '官网', '网站', '链接', '点击', '访问',
  '下载APP', '安装APP', 'APP下载',
  
  // 广告营销相关 - 禁止商业推广
  '代理', '招商', '加盟', '合作', '代销', '分销',
  '赚钱', '兼职', '副业', '创业', '投资',
  '刷单', '返利', '返现', '佣金', '提成',
  '优惠券', '红包', '现金券', '代金券',
  '折扣', '特价', '打折', '降价', '促销',
  '限时', '秒杀', '抢购', '特卖', '清仓',
  '包邮', '免费送', '0元购', '免费领', '送货上门',
  
  // 违法违规内容
  '假货', '高仿', '山寨', '盗版', '水货', '走私',
  '黄赌毒', '色情', '赌博', '毒品',
  '枪支', '管制刀具', '爆炸物',
  
  // 欺诈和不当内容
  '垃圾', '骗子', '诈骗', '欺骗', '虚假',
  '传销', '非法', '违法', '套路',
  '木马', '病毒', '钓鱼', '黑客',
  
  // 其他不当行为
  '刷票', '刷评', '买粉', '僵尸粉',
  '洗钱', '套现', '信用卡', '贷款'
];

// 商品链接模式 - 禁止用户发布商品购买链接
const SHOPPING_LINK_PATTERNS = [
  // 淘宝系
  /taobao\.com/i,
  /tmall\.com/i,
  /tb\.cn/i,
  /detail\.tmall\.com/i,
  /item\.taobao\.com/i,
  /淘宝/i,
  /天猫/i,
  
  // 京东
  /jd\.com/i,
  /3\.cn/i,
  /item\.jd\.com/i,
  /京东/i,
  /jd/i,
  
  // 拼多多
  /pinduoduo\.com/i,
  /pdd\.com/i,
  /yangkeduo\.com/i,
  /拼多多/i,
  
  // 其他主流电商
  /suning\.com/i,
  /dangdang\.com/i,
  /amazon\.cn/i,
  /amazon\.com/i,
  /vip\.com/i,
  /唯品会/i,
  /苏宁/i,
  /当当/i,
  
  // 通用购物模式
  /buy\./i,
  /shop\./i,
  /mall\./i,
  /store\./i,
  /商城/i,
  /购买/i,
  /下单/i,
  /立即购买/i,
  /马上购买/i,
  /点击购买/i,
  /商品链接/i,
  /购买链接/i
];

// 通用链接模式 - 禁止用户发布网站链接
const GENERAL_LINK_PATTERNS = [
  // 标准网址
  /http[s]?:\/\/[^\s]+/gi,
  /www\.[^\s]+/gi,
  /[a-zA-Z0-9][-a-zA-Z0-9]{0,62}(\.[a-zA-Z0-9][-a-zA-Z0-9]{0,62})+/gi,
  
  // 常见网址简写
  /\.com[^\s]*/gi,
  /\.cn[^\s]*/gi,
  /\.net[^\s]*/gi,
  /\.org[^\s]*/gi,
  /\.cc[^\s]*/gi,
  
  // 中文网址提示词
  /网址/gi,
  /链接/gi,
  /官网/gi,
  /网站/gi,
  /域名/gi
];

/**
 * 检测敏感词
 * @param {String} content 待检测内容
 * @returns {Object} 检测结果
 */
const detectSensitiveWords = (content) => {
  if (!content || typeof content !== 'string') {
    return { hasSensitiveWords: false, words: [] };
  }

  const foundWords = [];
  const lowerContent = content.toLowerCase();

  for (const word of SENSITIVE_WORDS) {
    if (lowerContent.includes(word.toLowerCase())) {
      foundWords.push(word);
    }
  }

  return {
    hasSensitiveWords: foundWords.length > 0,
    words: foundWords
  };
};

/**
 * 检测商品链接
 * @param {String} content 待检测内容
 * @returns {Object} 检测结果
 */
const detectShoppingLinks = (content) => {
  if (!content || typeof content !== 'string') {
    return { hasShoppingLinks: false, links: [] };
  }

  const foundLinks = [];

  for (const pattern of SHOPPING_LINK_PATTERNS) {
    const matches = content.match(pattern);
    if (matches) {
      foundLinks.push(...matches);
    }
  }

  return {
    hasShoppingLinks: foundLinks.length > 0,
    links: [...new Set(foundLinks)] // 去重
  };
};

/**
 * 检测外部链接
 * @param {String} content 待检测内容
 * @returns {Object} 检测结果
 */
const detectExternalLinks = (content) => {
  if (!content || typeof content !== 'string') {
    return { hasExternalLinks: false, links: [] };
  }

  const foundLinks = [];

  for (const pattern of GENERAL_LINK_PATTERNS) {
    const matches = content.match(pattern);
    if (matches) {
      foundLinks.push(...matches);
    }
  }

  return {
    hasExternalLinks: foundLinks.length > 0,
    links: [...new Set(foundLinks)] // 去重
  };
};

/**
 * 检测内容质量
 * @param {String} content 待检测内容
 * @returns {Object} 检测结果
 */
const detectContentQuality = (content) => {
  if (!content || typeof content !== 'string') {
    return { isLowQuality: true, reasons: ['内容为空'] };
  }

  const reasons = [];
  const trimmedContent = content.trim();

  // 检测内容长度
  if (trimmedContent.length < 2) {
    reasons.push('内容过短');
  }

  // 检测重复字符
  const repeatedCharPattern = /(.)\1{4,}/g; // 连续5个及以上相同字符
  if (repeatedCharPattern.test(content)) {
    reasons.push('包含过多重复字符');
  }

  // 检测纯数字或特殊字符
  const pureNumberPattern = /^\d+$/;
  const pureSpecialCharPattern = /^[^\w\u4e00-\u9fa5]+$/;
  
  if (pureNumberPattern.test(trimmedContent)) {
    reasons.push('内容为纯数字');
  }
  
  if (pureSpecialCharPattern.test(trimmedContent)) {
    reasons.push('内容为纯特殊字符');
  }

  // 检测无意义内容
  const meaninglessPatterns = [
    /^[啊哈嗯呃额]{2,}$/,
    /^[哈哈哈]{3,}$/,
    /^[。。。]{3,}$/,
    /^[…]{2,}$/
  ];

  for (const pattern of meaninglessPatterns) {
    if (pattern.test(trimmedContent)) {
      reasons.push('内容无实际意义');
      break;
    }
  }

  return {
    isLowQuality: reasons.length > 0,
    reasons
  };
};

/**
 * 综合内容审核
 * @param {String} content 待审核内容
 * @param {Object} options 审核选项
 * @returns {Object} 审核结果
 */
const auditContent = (content, options = {}) => {
  const {
    checkSensitiveWords = true,
    checkShoppingLinks = true,
    checkExternalLinks = false, // 对于问题内容，可能允许外部链接
    checkContentQuality = true
  } = options;

  const auditResult = {
    passed: true,
    violations: [],
    warnings: [],
    details: {}
  };

  try {
    // 敏感词检测
    if (checkSensitiveWords) {
      const sensitiveWordsResult = detectSensitiveWords(content);
      auditResult.details.sensitiveWords = sensitiveWordsResult;
      
      if (sensitiveWordsResult.hasSensitiveWords) {
        auditResult.passed = false;
        auditResult.violations.push({
          type: 'sensitive_words',
          message: `内容包含敏感词汇: ${sensitiveWordsResult.words.join(', ')}`,
          data: sensitiveWordsResult.words
        });
      }
    }

    // 商品链接检测
    if (checkShoppingLinks) {
      const shoppingLinksResult = detectShoppingLinks(content);
      auditResult.details.shoppingLinks = shoppingLinksResult;
      
      if (shoppingLinksResult.hasShoppingLinks) {
        auditResult.passed = false;
        auditResult.violations.push({
          type: 'shopping_links',
          message: '内容包含商品购买链接',
          data: shoppingLinksResult.links
        });
      }
    }

    // 外部链接检测
    if (checkExternalLinks) {
      const externalLinksResult = detectExternalLinks(content);
      auditResult.details.externalLinks = externalLinksResult;
      
      if (externalLinksResult.hasExternalLinks) {
        auditResult.passed = false;  // 外部链接也应该导致审核失败
        auditResult.violations.push({
          type: 'external_links',
          message: '内容包含外部链接',
          data: externalLinksResult.links
        });
      }
    }

    // 内容质量检测
    if (checkContentQuality) {
      const qualityResult = detectContentQuality(content);
      auditResult.details.contentQuality = qualityResult;
      
      if (qualityResult.isLowQuality) {
        auditResult.passed = false;
        auditResult.violations.push({
          type: 'low_quality',
          message: `内容质量不符合要求: ${qualityResult.reasons.join(', ')}`,
          data: qualityResult.reasons
        });
      }
    }

  } catch (error) {
    auditResult.passed = false;
    auditResult.violations.push({
      type: 'audit_error',
      message: '内容审核过程中发生错误',
      data: error.message
    });
  }

  return auditResult;
};

/**
 * 问题内容专用审核
 * @param {Object} questionData 问题数据
 * @returns {Object} 审核结果
 */
const auditQuestionContent = (questionData) => {
  const results = {
    passed: true,
    violations: [],
    warnings: [],
    fieldResults: {}
  };

  // 审核标题
  if (questionData.title) {
    const titleResult = auditContent(questionData.title, {
      checkSensitiveWords: true,
      checkShoppingLinks: true,
      checkExternalLinks: true,  // 标题也要检查外部链接
      checkContentQuality: true
    });
    
    results.fieldResults.title = titleResult;
    
    if (!titleResult.passed) {
      results.passed = false;
      results.violations.push(...titleResult.violations.map(v => ({
        ...v,
        field: 'title',
        message: `标题${v.message}`
      })));
    }
    
    results.warnings.push(...titleResult.warnings.map(w => ({
      ...w,
      field: 'title',
      message: `标题${w.message}`
    })));
  }

  // 审核使用场景
  if (questionData.scene) {
    const sceneResult = auditContent(questionData.scene, {
      checkSensitiveWords: true,
      checkShoppingLinks: true,
      checkExternalLinks: true,  // 场景描述也要检查外部链接
      checkContentQuality: false // 场景描述可以较短
    });
    
    results.fieldResults.scene = sceneResult;
    
    if (!sceneResult.passed) {
      results.passed = false;
      results.violations.push(...sceneResult.violations.map(v => ({
        ...v,
        field: 'scene',
        message: `使用场景${v.message}`
      })));
    }
    
    results.warnings.push(...sceneResult.warnings.map(w => ({
      ...w,
      field: 'scene',
      message: `使用场景${w.message}`
    })));
  }

  // 审核关键考量因素
  if (questionData.keyFactors) {
    const keyFactorsResult = auditContent(questionData.keyFactors, {
      checkSensitiveWords: true,
      checkShoppingLinks: true,
      checkExternalLinks: true,  // 关键因素也要检查外部链接
      checkContentQuality: false
    });
    
    results.fieldResults.keyFactors = keyFactorsResult;
    
    if (!keyFactorsResult.passed) {
      results.passed = false;
      results.violations.push(...keyFactorsResult.violations.map(v => ({
        ...v,
        field: 'keyFactors',
        message: `关键因素${v.message}`
      })));
    }
    
    results.warnings.push(...keyFactorsResult.warnings.map(w => ({
      ...w,
      field: 'keyFactors',
      message: `关键因素${w.message}`
    })));
  }

  // 审核选项内容
  if (questionData.options && Array.isArray(questionData.options)) {
    questionData.options.forEach((option, index) => {
      if (option.content) {
        const optionResult = auditContent(option.content, {
          checkSensitiveWords: true,
          checkShoppingLinks: true,
          checkExternalLinks: true,  // 选项内容也要检查外部链接
          checkContentQuality: true
        });
        
        results.fieldResults[`option_${index}`] = optionResult;
        
        if (!optionResult.passed) {
          results.passed = false;
          results.violations.push(...optionResult.violations.map(v => ({
            ...v,
            field: `options[${index}]`,
            message: `选项${index + 1}${v.message}`
          })));
        }
        
        results.warnings.push(...optionResult.warnings.map(w => ({
          ...w,
          field: `options[${index}]`,
          message: `选项${index + 1}${w.message}`
        })));
      }
    });
  }

  return results;
};

/**
 * 回答内容专用审核
 * @param {String} content 回答内容（理由）
 * @returns {Object} 审核结果
 */
const auditAnswerContent = (content) => {
  return auditContent(content, {
    checkSensitiveWords: true,
    checkShoppingLinks: true,
    checkExternalLinks: true,
    checkContentQuality: true
  });
};

/**
 * 评论内容专用审核
 * @param {String} content 评论内容
 * @returns {Object} 审核结果
 */
const auditCommentContent = (content) => {
  return auditContent(content, {
    checkSensitiveWords: true,
    checkShoppingLinks: true,
    checkExternalLinks: true,
    checkContentQuality: true
  });
};



module.exports = {
  detectSensitiveWords,
  detectShoppingLinks,
  detectExternalLinks,
  detectContentQuality,
  auditContent,
  auditQuestionContent,
  auditAnswerContent,
  auditCommentContent
}; 