/**
 * 产品展示组件样式
 * Product Show Component Styles
 * 依赖：全局样式系统（variables.wxss, components.wxss, utilities.wxss）
 */

/* ==================== 组件容器 Component Container ==================== */
.product-show-container {
  width: 100%;
}

/* ==================== 产品列表组件 Products List Component ==================== */
.products-list {
  display: flex;
  flex-direction: column;
  gap: 20rpx;
}

.product-item {
  display: flex;
  align-items: center;
  background: #f8f9fa;
  border-radius: 12rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
}

.product-item:active {
  transform: translateY(2rpx);
  border-color: #3B7ADB;
  box-shadow: 0 4rpx 12rpx rgba(59, 122, 219, 0.15);
}

.product-image {
  width: 120rpx;
  height: 120rpx;
  border-radius: 8rpx;
  background: #f8f9fa;
  border: 2rpx solid #e6e6e6;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  box-sizing: border-box;
}

.product-info {
  flex: 1;
}

.product-name {
  font-size: 28rpx;
  font-weight: 500;
  color: #333;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.product-meta {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.product-meta > view:first-child {
  display: flex;
  gap: 20rpx;
  align-items: center;
}

/* ==================== 产品元信息 Product Meta ==================== */
.product-date {
  font-size: 24rpx;
  color: #666;
  background: white;
  padding: 6rpx 12rpx;
  border-radius: 20rpx;
  border: 1rpx solid #e6e6e6;
}

.product-type {
  font-size: 24rpx;
  color: #3B7ADB;
  background: rgba(59, 122, 219, 0.1);
  padding: 6rpx 12rpx;
  border-radius: 20rpx;
}

/* ==================== 价格范围 Product Price ==================== */
.product-price {
  display: flex;
  align-items: center;
  margin-top: 4rpx;
}

.price-label {
  font-size: 22rpx;
  color: #999;
  margin-right: 8rpx;
}

.price-range {
  font-size: 24rpx;
  color: #ff6b35;
  font-weight: 500;
}

/* ==================== 产品操作区域 Product Actions ==================== */
.product-actions {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-left: 20rpx;
}

.compare-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 12rpx 20rpx;
  background: #f8f9fa;
  border: 2rpx solid #3B7ADB;
  border-radius: 20rpx;
  transition: all 0.3s ease;
  min-width: 80rpx;
}

.compare-btn:active {
  transform: scale(0.95);
}

.compare-btn-active {
  background: #3B7ADB;
  border-color: #3B7ADB;
}

.compare-btn-text {
  font-size: 24rpx;
  font-weight: 500;
  color: #3B7ADB;
  transition: color 0.3s ease;
}

.compare-btn-active .compare-btn-text {
  color: white;
}

/* ==================== 状态组件 State Components ==================== */
/* 加载状态 - 使用全局间距工具类 */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
}

/* 空状态 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 40rpx;
  text-align: center;
  min-height: 300rpx;
}

.empty-icon {
  font-size: 100rpx;
  color: #ddd;
  margin-bottom: 20rpx;
}

.empty-title {
  font-size: 32rpx;
  color: #666;
  font-weight: 500;
  margin-bottom: 12rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #999;
  line-height: 1.5;
}

/* ==================== scroll-view 滚动容器样式 ==================== */
.products-scroll-view {
  height: calc(100vh - 600rpx); /* 根据筛选区域高度调整 */
  min-height: 400rpx;
}

/* 空状态时的 scroll-view 样式 */
.empty-scroll-view {
  height: auto;
  min-height: 300rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* ==================== 欢迎页面样式优化 ==================== */
.welcome-container {
  padding: 60rpx 40rpx;
  min-height: 300rpx;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.welcome-icon {
  font-size: 100rpx;
  margin-bottom: 24rpx;
  animation: welcome-pulse 2s infinite;
}

@keyframes welcome-pulse {
  0%, 100% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.8;
  }
}

.welcome-title {
  font-size: 36rpx;
  color: #333;
  font-weight: 600;
  margin-bottom: 16rpx;
}

.welcome-desc {
  font-size: 28rpx;
  color: #666;
  margin-bottom: 40rpx;
  text-align: center;
  line-height: 1.6;
}

/* ==================== 加载更多文字链接区域 Load More Text Link Area ==================== */
.load-more-text-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16rpx;
  padding: 20rpx 0;
}

.load-more-info {
  margin-bottom: 8rpx;
}

.remaining-count {
  font-size: 24rpx;
  color: #999;
  text-align: center;
  opacity: 0.8;
}

.load-more-link {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 12rpx 20rpx;
  cursor: pointer;
  transition: all 0.3s ease;
  border-radius: 20rpx;
  background: rgba(59, 122, 219, 0.05);
}

.load-more-link:active {
  transform: scale(0.96);
  background: rgba(59, 122, 219, 0.1);
}

.load-more-text {
  font-size: 28rpx;
  color: #3B7ADB;
  font-weight: 500;
}

.load-more-arrow {
  font-size: 20rpx;
  color: #3B7ADB;
  animation: bounce-gentle 2s infinite;
}

@keyframes bounce-gentle {
  0%, 20%, 50%, 80%, 100% {
    transform: translateY(0);
  }
  40% {
    transform: translateY(-3rpx);
  }
  60% {
    transform: translateY(-2rpx);
  }
}

/* ==================== 响应式适配 Responsive Design ==================== */
@media (max-width: 750rpx) {
  .product-item {
    flex-direction: column;
    text-align: center;
  }
  
  .product-image {
    margin-right: 0;
    margin-bottom: 16rpx;
  }
}

/* ==================== 图片优化 Image Optimization ==================== */
/* 为不同尺寸的图片提供更好的显示效果 */
.product-image image {
  width: 100%;
  height: 100%;
  object-fit: contain; /* 确保图片完整显示不被裁剪 */
}

/* 图片加载失败时的占位样式 */
.product-image::before {
  content: "";
  font-size: 40rpx;
  color: #ccc;
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  z-index: 1;
}