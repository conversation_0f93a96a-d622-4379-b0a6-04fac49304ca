const Notification = require('../models/Notification');
const User = require('../models/User');

/**
 * 通知服务
 * 提供通知相关的业务逻辑
 */

/**
 * 创建新通知
 * @param {String} recipientId - 接收者ID
 * @param {String} senderId - 发送者ID
 * @param {String} type - 通知类型
 * @param {String} content - 通知内容
 * @param {String} itemId - 关联内容ID
 * @param {String} itemType - 关联内容类型
 * @param {Object} metadata - 额外的元数据
 * @return {Object} 创建的通知对象
 */
exports.createNotification = async (recipientId, senderId, type, content, itemId, itemType, metadata = {}) => {
  return await Notification.create({
    recipient: recipientId,
    sender: senderId,
    type,
    content,
    relatedItem: {
      itemId,
      itemType
    },
    metadata
  });
};

/**
 * 获取用户的通知列表
 * @param {String} userId - 用户ID
 * @param {Number} page - 页码
 * @param {Number} limit - 每页数量
 * @return {Object} 通知列表和分页信息
 */
exports.getUserNotifications = async (userId, page = 1, limit = 20) => {
  const skip = (page - 1) * limit;
  
  const notifications = await Notification.find({ recipient: userId })
    .sort({ createdAt: -1 })
    .skip(skip)
    .limit(limit)
    .populate('sender', 'nickname avatar')
    .lean();
    
  const total = await Notification.countDocuments({ recipient: userId });
  
  return {
    notifications,
    pagination: {
      total,
      page,
      limit,
      pages: Math.ceil(total / limit)
    }
  };
};

/**
 * 标记通知为已读
 * @param {String} notificationId - 通知ID
 * @param {String} userId - 用户ID
 * @return {Object} 更新后的通知对象
 */
exports.markAsRead = async (notificationId, userId) => {
  return await Notification.findOneAndUpdate(
    { _id: notificationId, recipient: userId },
    { isRead: true },
    { new: true }
  );
};

/**
 * 标记用户所有通知为已读
 * @param {String} userId - 用户ID
 * @return {Object} 更新结果
 */
exports.markAllAsRead = async (userId) => {
  return await Notification.updateMany(
    { recipient: userId, isRead: false },
    { isRead: true }
  );
};

/**
 * 获取用户未读通知数量
 * @param {String} userId - 用户ID
 * @return {Number} 未读通知数量
 */
exports.getUnreadCount = async (userId) => {
  return await Notification.countDocuments({ recipient: userId, isRead: false });
}; 