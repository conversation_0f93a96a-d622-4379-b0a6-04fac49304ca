const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const OptionSchema = new Schema({
  content: {
    type: String,
    required: [true, '选项内容不能为空'],
    maxlength: [100, '选项内容最多100个字符']
  },
  voteCount: {
    type: Number,
    default: 0
  }
});

const VisibilitySchema = new Schema({
  type: {
    type: String,
    enum: ['public', 'filtered'],
    default: 'public'
  },
  filters: {
    gender: {
      type: [String],
      enum: ['male', 'female', 'secret'],
      default: []
    },
    minAge: {
      type: Number,
      default: null
    },
    maxAge: {
      type: Number,
      default: null
    },
    regions: {
      type: [String],
      default: []
    },
    occupations: {
      type: [String],
      default: []
    }
  }
});

const BudgetSchema = new Schema({
  min: {
    type: Number,
    default: null
  },
  max: {
    type: Number,
    default: null
  },
  currency: {
    type: String,
    default: 'CNY'
  }
});

const QuestionSchema = new Schema(
  {
    // 基本信息
    title: {
      type: String,
      required: [true, '问题标题不能为空'],
      maxlength: [50, '问题标题最多50个字符'],
      trim: true
    },
    // 使用场景描述
    scene: {
      type: String,
      maxlength: [500, '使用场景描述最多500个字符'],
      default: ''
    },
    // 关键考量因素 - 修改为字符串类型
    keyFactors: {
      type: String,
      maxlength: [200, '关键考量因素最多200个字符'],
      default: ''
    },
    // 预算区间
    budget: {
      type: BudgetSchema,
      default: () => ({})
    },
    // 标签
    tags: {
      type: [String],
      enum: ['手机', '电脑'],
      default: []
    },
    options: {
      type: [OptionSchema],
      validate: {
        validator: function(options) {
          return options && options.length >= 2;
        },
        message: '问题至少需要2个选项'
      }
    },
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
      index: true
    },
    isAnonymous: {
      type: Boolean,
      default: false
    },
    requireReason: {
      type: Boolean,
      default: false
    },

    // 可见性设置
    visibility: {
      type: VisibilitySchema,
      default: () => ({})
    },

    // 问题状态与统计
    expiryTime: {
      type: Date,
      default: null,
      index: true
    },
    status: {
      type: String,
      enum: ['open', 'closed'],
      default: 'open'
    },
    totalVotes: {
      type: Number,
      default: 0,
      index: true
    },
    commentCount: {
      type: Number,
      default: 0
    }
  },
  {
    timestamps: true
  }
);

// 索引设置
QuestionSchema.index({ 'status': 1, 'createdAt': -1 });
QuestionSchema.index({ 'visibility.type': 1 });
QuestionSchema.index({ 'tags': 1 });

// 添加文本搜索索引 - 优化中文搜索支持
QuestionSchema.index({ 
  title: 'text', 
  scene: 'text', 
  keyFactors: 'text' 
}, {
  weights: {
    title: 10,      // 标题权重最高
    keyFactors: 5,  // 关键因素权重中等
    scene: 1        // 场景描述权重最低
  },
  name: 'question_text_index',
  default_language: 'none',  // 禁用语言特定的词干提取，对中文更友好
  language_override: 'language'
});

// 添加单独的字段索引以支持正则表达式搜索
QuestionSchema.index({ 'title': 1 });
QuestionSchema.index({ 'scene': 1 });
QuestionSchema.index({ 'keyFactors': 1 });

// 静态方法：关闭到期问题
QuestionSchema.statics.closeExpiredQuestions = async function() {
  const now = new Date();
  return this.updateMany(
    { status: 'open', expiryTime: { $lte: now } },
    { $set: { status: 'closed' } }
  );
};

// 级联删除相关的回答和评论
QuestionSchema.pre('remove', async function() {
  const Answer = mongoose.model('Answer');
  const Comment = mongoose.model('Comment');
  
  // 删除相关的评论
  await Comment.deleteMany({ questionId: this._id });
  
  // 删除相关的回答
  await Answer.deleteMany({ questionId: this._id });
});

module.exports = mongoose.model('Question', QuestionSchema); 