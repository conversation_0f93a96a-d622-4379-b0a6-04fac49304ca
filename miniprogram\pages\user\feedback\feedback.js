const app = getApp();
const { feedback } = require('../../../utils/api');
const util = require('../../../utils/util');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    content: '',
    type: 'other',
    typeOptions: [
      { value: 'bug', label: '问题反馈' },
      { value: 'suggestion', label: '建议反馈' },
      { value: 'question', label: '疑问咨询' },
      { value: 'other', label: '其他反馈' }
    ],
    selectedTypeIndex: 3, // 默认选择"其他反馈"
    images: [],
    maxImages: 6,
    loading: false,
    canSubmit: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '意见反馈'
    });

    // 处理从其他页面传递过来的参数
    if (options.content) {
      const content = decodeURIComponent(options.content);
      this.setData({
        content: content,
        canSubmit: content.trim().length > 0
      });
    }

    if (options.type) {
      const typeIndex = this.data.typeOptions.findIndex(option => option.value === options.type);
      if (typeIndex !== -1) {
        this.setData({
          selectedTypeIndex: typeIndex,
          type: options.type
        });
      }
    }
  },

  /**
   * 输入反馈内容
   */
  onContentInput: function(e) {
    const content = e.detail.value;
    const canSubmit = content && content.trim().length > 0;
    
    this.setData({
      content: content,
      canSubmit: canSubmit
    });
  },

  /**
   * 选择反馈类型 - 改为点击选择
   */
  onTypeSelect: function(e) {
    const index = parseInt(e.currentTarget.dataset.index);
    this.setData({
      selectedTypeIndex: index,
      type: this.data.typeOptions[index].value
    });
  },

  /**
   * 选择图片
   */
  chooseImage: function() {
    const remainingCount = this.data.maxImages - this.data.images.length;
    
    if (remainingCount <= 0) {
      util.showToast('最多只能上传6张图片');
      return;
    }

    wx.chooseImage({
      count: remainingCount,
      sizeType: ['original', 'compressed'],
      sourceType: ['album', 'camera'],
      success: (res) => {
        console.log('选择图片成功:', res.tempFilePaths);
        this.uploadImages(res.tempFilePaths);
      },
      fail: (err) => {
        console.error('选择图片失败:', err);
        util.showToast('选择图片失败');
      }
    });
  },

  /**
   * 上传图片
   */
  uploadImages: function(tempFilePaths) {
    console.log('开始上传图片:', tempFilePaths);
    util.showLoading('上传图片中...');
    
    feedback.uploadFeedbackImages(tempFilePaths)
      .then(res => {
        util.hideLoading();
        console.log('图片上传API响应:', res);
        
        if (res.success && res.data && res.data.images) {
          const newImages = res.data.images;
          console.log('上传成功的图片数据:', newImages);
          
          // 验证图片URL
          newImages.forEach((img, index) => {
            console.log(`图片${index + 1} URL:`, img.file_url);
            if (!img.file_url) {
              console.error(`图片${index + 1} 缺少file_url字段`);
            }
          });
          
          this.setData({
            images: [...this.data.images, ...newImages]
          }, () => {
            console.log('更新后的图片列表:', this.data.images);
          });
          
          util.showToast('图片上传成功', 'success');
        } else {
          console.error('图片上传响应格式错误:', res);
          throw new Error(res.message || '上传失败');
        }
      })
      .catch(err => {
        util.hideLoading();
        console.error('上传图片失败:', err);
        util.showToast(err.message || '上传图片失败');
      });
  },

  /**
   * 图片加载成功事件
   */
  onImageLoad: function(e) {
    const index = e.currentTarget.dataset.index;
    console.log(`图片${index}加载成功:`, this.data.images[index].file_url);
  },

  /**
   * 图片加载失败事件
   */
  onImageError: function(e) {
    const index = e.currentTarget.dataset.index;
    const imageUrl = this.data.images[index].file_url;
    console.error(`图片${index}加载失败:`, imageUrl, e.detail);
    
    // 显示加载失败提示
    util.showToast('图片加载失败，请检查网络连接');
    
    // 可以考虑移除加载失败的图片或显示占位图
    // this.deleteImage({ currentTarget: { dataset: { index } } });
  },

  /**
   * 删除图片
   */
  deleteImage: function(e) {
    const index = e.currentTarget.dataset.index;
    console.log('删除图片索引:', index);
    const images = this.data.images.filter((_, i) => i !== index);
    this.setData({ images }, () => {
      console.log('删除后的图片列表:', this.data.images);
    });
  },

  /**
   * 预览图片
   */
  previewImage: function(e) {
    const index = e.currentTarget.dataset.index;
    const urls = this.data.images.map(img => img.file_url);
    
    console.log('预览图片URLs:', urls);
    console.log('当前图片索引:', index);
    
    // 过滤掉空URL
    const validUrls = urls.filter(url => url && url.trim());
    
    if (validUrls.length === 0) {
      util.showToast('没有有效的图片可以预览');
      return;
    }
    
    wx.previewImage({
      current: urls[index],
      urls: validUrls,
      success: () => {
        console.log('图片预览成功');
      },
      fail: (err) => {
        console.error('图片预览失败:', err);
        util.showToast('图片预览失败');
      }
    });
  },

  /**
   * 提交反馈
   */
  submitFeedback: function() {
    // 验证输入
    if (!this.data.content.trim()) {
      util.showToast('请输入反馈内容');
      return;
    }

    if (this.data.content.trim().length > 500) {
      util.showToast('反馈内容不能超过500个字符');
      return;
    }

    this.setData({ loading: true });
    util.showLoading('提交中...');

    // 获取设备信息
    const systemInfo = wx.getSystemInfoSync();
    const deviceInfo = {
      platform: systemInfo.platform,
      system: systemInfo.system,
      version: systemInfo.version,
      model: systemInfo.model,
      brand: systemInfo.brand,
      pixelRatio: systemInfo.pixelRatio,
      screenWidth: systemInfo.screenWidth,
      screenHeight: systemInfo.screenHeight,
      windowWidth: systemInfo.windowWidth,
      windowHeight: systemInfo.windowHeight,
      statusBarHeight: systemInfo.statusBarHeight,
      language: systemInfo.language,
      fontSizeSetting: systemInfo.fontSizeSetting,
      SDKVersion: systemInfo.SDKVersion,
      benchmarkLevel: systemInfo.benchmarkLevel,
      appVersion: app.globalData.version || '1.0.0'
    };

    // 构建提交数据
    const feedbackData = {
      content: this.data.content.trim(),
      type: this.data.type,
      images: this.data.images.map(img => img.file_url),
      deviceInfo: deviceInfo
    };

    feedback.createFeedback(feedbackData)
      .then(res => {
        util.hideLoading();
        this.setData({ loading: false });
        
        if (res.success) {
          util.showToast('反馈提交成功', 'success');
          
          // 延迟返回上一页
          setTimeout(() => {
            wx.navigateBack();
          }, 1500);
        } else {
          throw new Error(res.message || '提交失败');
        }
      })
      .catch(err => {
        util.hideLoading();
        this.setData({ loading: false });
        console.error('提交反馈失败:', err);
        util.showToast(err.message || '提交失败，请稍后再试');
      });
  },

  /**
   * 查看我的反馈
   */
  viewMyFeedback: function() {
    wx.navigateTo({
      url: '/pages/user/feedback/list/list'
    });
  }
}); 