/**
 * WebSocket服务
 * 处理WebSocket连接和实时通知推送
 */

const WebSocket = require('ws');
const jwt = require('jsonwebtoken');
const url = require('url');

let wss;
// 用户连接映射表
const clients = new Map();

/**
 * 初始化WebSocket服务
 * @param {Object} server - HTTP服务器实例
 * @return {Object} WebSocket服务器实例
 */
exports.initWebSocket = (server) => {
  wss = new WebSocket.Server({ server });

  wss.on('connection', (ws, req) => {
    // 解析URL参数获取token
    const parameters = url.parse(req.url, true);
    const token = parameters.query.token;

    if (!token) {
      ws.close(4001, '认证失败: 缺少token');
      return;
    }

    try {
      // 验证Token
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      const userId = decoded.id;

      console.log(`用户 ${userId} 已连接`);
      
      // 存储用户连接
      clients.set(userId.toString(), ws);

      // 发送连接成功消息
      ws.send(JSON.stringify({
        type: 'connection',
        message: '连接成功'
      }));

      // 处理消息
      ws.on('message', (message) => {
        try {
          const data = JSON.parse(message);
          // console.log(`收到用户 ${userId} 的消息:`, data);
        } catch (error) {
          console.error('消息解析错误:', error);
        }
      });

      // 处理关闭连接
      ws.on('close', () => {
        console.log(`用户 ${userId} 已断开连接`);
        clients.delete(userId.toString());
      });

      // 处理错误
      ws.on('error', (error) => {
        console.error(`用户 ${userId} 连接错误:`, error);
        clients.delete(userId.toString());
      });

    } catch (error) {
      console.error('认证失败:', error);
      ws.close(4002, '认证失败: 无效token');
    }
  });

  return wss;
};

/**
 * 发送通知给特定用户
 * @param {String} userId - 接收通知的用户ID
 * @param {Object} notification - 通知内容
 */
exports.sendNotification = (userId, notification) => {
  const userWs = clients.get(userId.toString());
  
  if (userWs && userWs.readyState === WebSocket.OPEN) {
    userWs.send(JSON.stringify({
      type: 'notification',
      data: notification
    }));
  }
};

/**
 * 获取WebSocket服务器实例
 * @return {Object} WebSocket服务器实例
 */
exports.getWSS = () => {
  return wss;
}; 