/**
 * 选选小程序 - 设计规范变量定义
 * Design Variables for XuanXuan Mini Program
 */

/* ==================== 颜色系统 Color System ==================== */

/* 主题色 Primary Colors */
.primary-color {
  color: #3B7ADB;
}

.primary-bg {
  background-color: #3B7ADB;
}

/* 辅助色 Secondary Colors */
.success-color {
  color: #28A745;
}

.warning-color {
  color: #FFC107;
}

.danger-color {
  color: #ff5151;
}

.info-color {
  color: #17A2B8;
}

/* 中性色 Neutral Colors */
.text-color-primary {
  color: #333333;
}

.text-color-secondary {
  color: #666666;
}

.text-color-placeholder {
  color: #999999;
}

.text-color-disabled {
  color: #cccccc;
}

.white-color {
  color: #ffffff;
}

/* 背景色 Background Colors */
.bg-color-primary {
  background-color: #ffffff;
}

.bg-color-secondary {
  background-color: #f5f5f5;
}

.bg-color-light {
  background-color: #fafafa;
}

/* 新增：更多背景色选项 */
.bg-color-card {
  background-color: #ffffff;
}

.bg-color-input {
  background-color: #f8f9fa;
}

.bg-color-input-focus {
  background-color: #ffffff;
}

/* 按钮背景色 */
.bg-color-btn-secondary {
  background-color: #f0f4ff;
}

.bg-color-btn-secondary-active {
  background-color: #e6efff;
}

.bg-color-btn-light {
  background-color: #f5f7fa;
}

.bg-color-btn-light-active {
  background-color: #eaecf0;
}

/* 边框色 Border Colors */
.border-color-light {
  border-color: #e6e6e6;
}

.border-color-lighter {
  border-color: #f5f5f5;
}

/* 新增：更多边框色选项 */
.border-color-default {
  border-color: #e0e0e0;
}

.border-color-input {
  border-color: #d9d9d9;
}

.border-color-input-focus {
  border-color: #3B7ADB;
}

.border-color-subtle {
  border-color: #eaeaea;
}

/* ==================== 字体系统 Typography System ==================== */

/* 字体大小 Font Sizes */
.font-size-large {
  font-size: 36rpx;
}

.font-size-normal {
  font-size: 32rpx;
}

.font-size-medium {
  font-size: 30rpx;
}

.font-size-small {
  font-size: 28rpx;
}

.font-size-xs {
  font-size: 24rpx;
}

.font-size-xxs {
  font-size: 22rpx;
}

/* 字体粗细 Font Weights */
.font-weight-bold {
  font-weight: bold;
}

.font-weight-normal {
  font-weight: normal;
}

/* 行高 Line Heights */
.line-height-normal {
  line-height: 1.5;
}

.line-height-large {
  line-height: 1.8;
}

/* ==================== 间距系统 Spacing System ==================== */

/* 外边距 Margins */
.margin-xs {
  margin: 10rpx;
}

.margin-sm {
  margin: 20rpx;
}

.margin-md {
  margin: 30rpx;
}

.margin-lg {
  margin: 40rpx;
}

.margin-xl {
  margin: 60rpx;
}

/* 单向外边距 Directional Margins */
.margin-top-xs {
  margin-top: 10rpx;
}

.margin-top-sm {
  margin-top: 20rpx;
}

.margin-top-md {
  margin-top: 30rpx;
}

.margin-top-lg {
  margin-top: 40rpx;
}

.margin-bottom-xs {
  margin-bottom: 10rpx;
}

.margin-bottom-sm {
  margin-bottom: 20rpx;
}

.margin-bottom-md {
  margin-bottom: 30rpx;
}

.margin-bottom-lg {
  margin-bottom: 40rpx;
}

.margin-left-sm {
  margin-left: 20rpx;
}

.margin-right-sm {
  margin-right: 20rpx;
}

/* 内边距 Paddings */
.padding-xs {
  padding: 10rpx;
}

.padding-sm {
  padding: 20rpx;
}

.padding-md {
  padding: 30rpx;
}

.padding-lg {
  padding: 40rpx;
}

.padding-xl {
  padding: 60rpx;
}

/* ==================== 边框系统 Border System ==================== */

/* 边框宽度 Border Widths */
.border-thin {
  border: 1rpx solid;
}

.border-thick {
  border: 2rpx solid;
}

/* 边框样式 Border Styles */
.border-solid {
  border-style: solid;
}

.border-dashed {
  border-style: dashed;
}

/* 圆角 Border Radius */
.border-radius-xs {
  border-radius: 4rpx;
}

.border-radius-sm {
  border-radius: 8rpx;
}

.border-radius-md {
  border-radius: 12rpx;
}

.border-radius-lg {
  border-radius: 24rpx;
}

.border-radius-xl {
  border-radius: 44rpx;
}

.border-radius-circle {
  border-radius: 50%;
}

/* ==================== 阴影系统 Shadow System ==================== */

.shadow-light {
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
}

.shadow-normal {
  box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1);
}

.shadow-heavy {
  box-shadow: 0 8rpx 30rpx rgba(0, 0, 0, 0.15);
}

/* ==================== 尺寸系统 Size System ==================== */

/* 头像尺寸 Avatar Sizes */
.avatar-sm {
  width: 60rpx;
  height: 60rpx;
}

.avatar-md {
  width: 80rpx;
  height: 80rpx;
}

.avatar-lg {
  width: 120rpx;
  height: 120rpx;
}

.avatar-xl {
  width: 160rpx;
  height: 160rpx;
}

/* 图标尺寸 Icon Sizes */
.icon-sm {
  width: 32rpx;
  height: 32rpx;
}

.icon-md {
  width: 40rpx;
  height: 40rpx;
}

.icon-lg {
  width: 48rpx;
  height: 48rpx;
}

/* 按钮高度 Button Heights */
.btn-height-sm {
  height: 64rpx;
  line-height: 64rpx;
}

.btn-height-md {
  height: 80rpx;
  line-height: 80rpx;
}

.btn-height-lg {
  height: 88rpx;
  line-height: 88rpx;
}

/* ==================== 布局系统 Layout System ==================== */

/* Flex 布局 */
.flex {
  display: flex;
}

.flex-column {
  flex-direction: column;
}

.flex-row {
  flex-direction: row;
}

.justify-center {
  justify-content: center;
}

.justify-between {
  justify-content: space-between;
}

.justify-start {
  justify-content: flex-start;
}

.justify-end {
  justify-content: flex-end;
}

.align-center {
  align-items: center;
}

.align-start {
  align-items: flex-start;
}

.align-end {
  align-items: flex-end;
}

.flex-1 {
  flex: 1;
}

/* 文本对齐 Text Alignment */
.text-left {
  text-align: left;
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

/* ==================== 状态系统 State System ==================== */

/* 禁用状态 Disabled State */
.disabled {
  opacity: 0.6;
  pointer-events: none;
}

/* 激活状态 Active State */
.active {
  opacity: 1;
}

/* 隐藏/显示 Visibility */
.hidden {
  display: none;
}

.visible {
  display: block;
}

/* ==================== 特殊效果 Special Effects ==================== */

/* 过渡动画 Transitions */
.transition-normal {
  transition: all 0.3s ease;
}

.transition-fast {
  transition: all 0.2s ease;
}

.transition-slow {
  transition: all 0.5s ease;
}

/* 透明度 Opacity */
.opacity-light {
  opacity: 0.8;
}

.opacity-medium {
  opacity: 0.6;
}

.opacity-heavy {
  opacity: 0.4;
}
