const productRatingService = require('../../services/user/productRatingService');
const { success, error } = require('../../utils/response');

/**
 * @desc    用户对产品进行评分
 * @route   POST /api/v1/users/product-ratings
 * @access  Private
 */
const rateProduct = async (req, res) => {
  try {
    const userId = req.user.id;
    const { productId, rating } = req.body;

    // 调用服务层进行评分
    const result = await productRatingService.rateProduct(
      userId,
      productId,
      rating
    );

    // 根据服务层返回结果响应
    if (result.success) {
      return success(res, 201, result.data.message, result.data);
    } else {
      // 根据错误类型返回不同的HTTP状态码
      let statusCode = 400;
      
      if (result.error.includes('不存在')) {
        statusCode = 404;
      } else if (result.error.includes('已被禁用')) {
        statusCode = 403;
      } else if (result.error.includes('已经评分过了')) {
        statusCode = 409; // Conflict
      }
      
      return error(res, statusCode, result.error);
    }
  } catch (err) {
    console.error('产品评分控制器错误:', err);
    return error(res, 500, '服务器内部错误，请稍后重试');
  }
};

module.exports = {
  rateProduct
};