/**
 * 选选小程序 - 工具类样式文件
 * Utility Classes for XuanXuan Mini Program
 * 依赖：variables.wxss
 */

/* ==================== 文本颜色工具类 Text Color Utilities ==================== */
.text-primary { color: #3B7ADB; }
.text-secondary { color: #666666; }
.text-success { color: #52c41a; }
.text-warning { color: #faad14; }
.text-danger { color: #ff4d4f; }
.text-info { color: #1890ff; }
.text-light { color: #999999; }
.text-dark { color: #333333; }
.text-white { color: #ffffff; }

/* ==================== 文本对齐工具类 Text Align Utilities ==================== */
.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }
.text-justify { text-align: justify; }

/* ==================== 外边距工具类 Margin Utilities ==================== */
/* 全方向边距 */
.m-0 { margin: 0; }
.m-10 { margin: 10rpx; }
.m-20 { margin: 20rpx; }
.m-30 { margin: 30rpx; }
.m-40 { margin: 40rpx; }
.m-50 { margin: 50rpx; }

/* 顶部边距 */
.mt-0 { margin-top: 0; }
.mt-10 { margin-top: 10rpx; }
.mt-20 { margin-top: 20rpx; }
.mt-30 { margin-top: 30rpx; }
.mt-40 { margin-top: 40rpx; }
.mt-50 { margin-top: 50rpx; }
.mt-60 { margin-top: 60rpx; }

/* 底部边距 */
.mb-0 { margin-bottom: 0; }
.mb-10 { margin-bottom: 10rpx; }
.mb-20 { margin-bottom: 20rpx; }
.mb-30 { margin-bottom: 30rpx; }
.mb-40 { margin-bottom: 40rpx; }
.mb-50 { margin-bottom: 50rpx; }
.mb-60 { margin-bottom: 60rpx; }

/* 左边距 */
.ml-0 { margin-left: 0; }
.ml-10 { margin-left: 10rpx; }
.ml-20 { margin-left: 20rpx; }
.ml-30 { margin-left: 30rpx; }
.ml-40 { margin-left: 40rpx; }

/* 右边距 */
.mr-0 { margin-right: 0; }
.mr-10 { margin-right: 10rpx; }
.mr-20 { margin-right: 20rpx; }
.mr-30 { margin-right: 30rpx; }
.mr-40 { margin-right: 40rpx; }

/* 水平边距 */
.mx-0 { margin-left: 0; margin-right: 0; }
.mx-10 { margin-left: 10rpx; margin-right: 10rpx; }
.mx-20 { margin-left: 20rpx; margin-right: 20rpx; }
.mx-30 { margin-left: 30rpx; margin-right: 30rpx; }
.mx-auto { margin-left: auto; margin-right: auto; }

/* 垂直边距 */
.my-0 { margin-top: 0; margin-bottom: 0; }
.my-10 { margin-top: 10rpx; margin-bottom: 10rpx; }
.my-20 { margin-top: 20rpx; margin-bottom: 20rpx; }
.my-30 { margin-top: 30rpx; margin-bottom: 30rpx; }

/* ==================== 内边距工具类 Padding Utilities ==================== */
/* 全方向内边距 */
.p-0 { padding: 0; }
.p-10 { padding: 10rpx; }
.p-20 { padding: 20rpx; }
.p-30 { padding: 30rpx; }
.p-40 { padding: 40rpx; }

/* 顶部内边距 */
.pt-0 { padding-top: 0; }
.pt-10 { padding-top: 10rpx; }
.pt-20 { padding-top: 20rpx; }
.pt-30 { padding-top: 30rpx; }
.pt-40 { padding-top: 40rpx; }

/* 底部内边距 */
.pb-0 { padding-bottom: 0; }
.pb-10 { padding-bottom: 10rpx; }
.pb-20 { padding-bottom: 20rpx; }
.pb-30 { padding-bottom: 30rpx; }
.pb-40 { padding-bottom: 40rpx; }

/* 左内边距 */
.pl-0 { padding-left: 0; }
.pl-10 { padding-left: 10rpx; }
.pl-20 { padding-left: 20rpx; }
.pl-30 { padding-left: 30rpx; }
.pl-40 { padding-left: 40rpx; }

/* 右内边距 */
.pr-0 { padding-right: 0; }
.pr-10 { padding-right: 10rpx; }
.pr-20 { padding-right: 20rpx; }
.pr-30 { padding-right: 30rpx; }
.pr-40 { padding-right: 40rpx; }

/* 水平内边距 */
.px-0 { padding-left: 0; padding-right: 0; }
.px-10 { padding-left: 10rpx; padding-right: 10rpx; }
.px-20 { padding-left: 20rpx; padding-right: 20rpx; }
.px-30 { padding-left: 30rpx; padding-right: 30rpx; }

/* 垂直内边距 */
.py-0 { padding-top: 0; padding-bottom: 0; }
.py-10 { padding-top: 10rpx; padding-bottom: 10rpx; }
.py-20 { padding-top: 20rpx; padding-bottom: 20rpx; }
.py-30 { padding-top: 30rpx; padding-bottom: 30rpx; }

/* ==================== 弹性布局工具类 Flexbox Utilities ==================== */
.flex { display: flex; }
.flex-1 { flex: 1; }
.flex-none { flex: none; }
.flex-auto { flex: auto; }

/* 弹性方向 */
.flex-row { flex-direction: row; }
.flex-column { flex-direction: column; }
.flex-row-reverse { flex-direction: row-reverse; }
.flex-column-reverse { flex-direction: column-reverse; }

/* 主轴对齐 */
.justify-start { justify-content: flex-start; }
.justify-center { justify-content: center; }
.justify-end { justify-content: flex-end; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }
.justify-evenly { justify-content: space-evenly; }

/* 交叉轴对齐 */
.items-start { align-items: flex-start; }
.items-center { align-items: center; }
.items-end { align-items: flex-end; }
.items-stretch { align-items: stretch; }
.items-baseline { align-items: baseline; }

/* 弹性布局组合 */
.flex-center {
  display: flex;
  align-items: center;
  justify-content: center;
}

.flex-between {
  display: flex;
  align-items: center;
  justify-content: space-between;
}

.flex-start {
  display: flex;
  align-items: center;
  justify-content: flex-start;
}

.flex-end {
  display: flex;
  align-items: center;
  justify-content: flex-end;
}

/* ==================== 显示隐藏工具类 Display Utilities ==================== */
.show { display: block !important; }
.hide { display: none !important; }
.invisible { visibility: hidden; }
.visible { visibility: visible; }

/* 块级元素 */
.d-block { display: block; }
.d-inline { display: inline; }
.d-inline-block { display: inline-block; }
.d-flex { display: flex; }
.d-inline-flex { display: inline-flex; }

/* ==================== 定位工具类 Position Utilities ==================== */
.position-static { position: static; }
.position-relative { position: relative; }
.position-absolute { position: absolute; }
.position-fixed { position: fixed; }
.position-sticky { position: sticky; }

/* ==================== 宽度高度工具类 Size Utilities ==================== */
.w-full { width: 100%; }
.w-auto { width: auto; }
.h-full { height: 100%; }
.h-auto { height: auto; }

/* ==================== 背景工具类 Background Utilities ==================== */
.bg-white { background-color: #ffffff; }
.bg-gray { background-color: #f5f5f5; }
.bg-primary { background-color: #3B7ADB; }
.bg-transparent { background-color: transparent; }

/* ==================== 边框工具类 Border Utilities ==================== */
.border { border: 1rpx solid #e6e6e6; }
.border-0 { border: none; }
.border-top { border-top: 1rpx solid #e6e6e6; }
.border-bottom { border-bottom: 1rpx solid #e6e6e6; }
.border-left { border-left: 1rpx solid #e6e6e6; }
.border-right { border-right: 1rpx solid #e6e6e6; }

/* 圆角 */
.rounded { border-radius: 8rpx; }
.rounded-lg { border-radius: 12rpx; }
.rounded-xl { border-radius: 20rpx; }
.rounded-full { border-radius: 50%; }
.rounded-none { border-radius: 0; }

/* ==================== 分割线工具类 Divider Utilities ==================== */
.divider {
  height: 1rpx;
  background-color: #f5f5f5;
  margin: 20rpx 0;
}

.divider-thick {
  height: 20rpx;
  background-color: #f5f5f5;
  margin: 30rpx 0;
}

/* 文字分割线 */
.divider-text {
  display: flex;
  align-items: center;
  margin: 40rpx 0;
}

.divider-text::before,
.divider-text::after {
  content: '';
  flex: 1;
  height: 1rpx;
  background-color: #e6e6e6;
}

.divider-text-content {
  padding: 0 30rpx;
  font-size: 24rpx;
  color: #999999;
}

/* ==================== 阴影工具类 Shadow Utilities ==================== */
.shadow-none { box-shadow: none; }
.shadow-sm { box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.05); }
.shadow { box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05); }
.shadow-lg { box-shadow: 0 4rpx 20rpx rgba(0, 0, 0, 0.1); }

/* ==================== 过渡动画工具类 Transition Utilities ==================== */
.transition { transition: all 0.3s ease; }
.transition-fast { transition: all 0.15s ease; }
.transition-slow { transition: all 0.5s ease; }

/* ==================== 动画效果工具类 Animation Utilities ==================== */

/* 淡入动画 */
@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

/* 滑入动画 */
@keyframes slideInUp {
  from {
    transform: translateY(100%);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

.slide-in-up {
  animation: slideInUp 0.3s ease-out;
}

/* 缩放动画 */
@keyframes scaleIn {
  from {
    transform: scale(0);
    opacity: 0;
  }
  to {
    transform: scale(1);
    opacity: 1;
  }
}

.scale-in {
  animation: scaleIn 0.3s ease-out;
}

/* 旋转动画 */
@keyframes rotate {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.rotate {
  animation: rotate 2s linear infinite;
}

/* ==================== 溢出处理工具类 Overflow Utilities ==================== */
.overflow-hidden { overflow: hidden; }
.overflow-auto { overflow: auto; }
.overflow-scroll { overflow: scroll; }
.overflow-visible { overflow: visible; }

/* ==================== Z-index工具类 Z-index Utilities ==================== */
.z-0 { z-index: 0; }
.z-10 { z-index: 10; }
.z-20 { z-index: 20; }
.z-30 { z-index: 30; }
.z-40 { z-index: 40; }
.z-50 { z-index: 50; }
.z-auto { z-index: auto; }
