module.exports = {
  apps: [
    {
      // 应用基本配置
      name: 'xuanxuan-server',
      script: './src/index.js',
      cwd: '/home/<USER>/code/xuanxuan/server', // 请根据你的实际服务器路径修改
      
      // 进程管理配置
      instances: 1, // 可以设置为 'max' 使用所有CPU核心
      exec_mode: 'fork', // 或者 'cluster' 集群模式
      
      // 环境变量
      env: {
        NODE_ENV: 'production'
      },
      env_development: {
        NODE_ENV: 'development'
      },
      env_production: {
        NODE_ENV: 'production'
      },
      
      // 日志配置
      log_file: './logs/xuanxuan-combined.log',
      out_file: './logs/xuanxuan-out.log',
      error_file: './logs/xuanxuan-error.log',
      log_date_format: 'YYYY-MM-DD HH:mm:ss Z',
      merge_logs: true,
      
      // 进程监控和重启配置
      watch: false, // 生产环境建议关闭文件监控
      ignore_watch: [
        'node_modules',
        'logs',
        'uploads',
        '.git'
      ],
      
      // 自动重启配置
      max_restarts: 10,
      min_uptime: '10s',
      max_memory_restart: '1500M',
      
      // 崩溃重启配置
      autorestart: true,
      restart_delay: 4000,
      
      // 其他配置
      node_args: '--max-old-space-size=1024',
      source_map_support: true,
      
      // 时间配置
      time: true,
      
      // 进程ID文件
      pid_file: './pids/xuanxuan-server.pid'
    }
  ],
}; 