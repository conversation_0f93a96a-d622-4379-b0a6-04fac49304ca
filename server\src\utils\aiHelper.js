/**
 * AI辅助工具类
 * 提供常用的AI调用封装函数，简化业务层的AI使用
 */

const { generateText } = require('./aiService');
const { getSystemPrompt } = require('../config/aiPrompts');

/**
 * 生成投票分析报告
 * @param {String} userPrompt 用户输入的分析内容
 * @param {String} provider AI提供商（可选，默认从环境变量获取）
 * @param {Object} config 配置选项（可选）
 * @returns {Promise<String>} AI生成的分析报告
 */
const generateVotingAnalysis = async (userPrompt, provider = process.env.DEFAULT_AI_PROVIDER || 'deepseek', config = {}) => {
  const systemPrompt = getSystemPrompt('voting_analysis');
  return await generateText(provider, userPrompt, systemPrompt, config);
};

/**
 * 生成产品参数对比分析
 * @param {String} userPrompt 产品参数对比数据
 * @param {String} provider AI提供商（可选，默认从环境变量获取）
 * @param {Object} config 配置选项（可选）
 * @returns {Promise<String>} AI生成的参数对比分析
 */
const generateProductComparison = async (userPrompt, provider = process.env.DEFAULT_AI_PROVIDER || 'deepseek', config = {}) => {
  const systemPrompt = getSystemPrompt('product_comparison');
  return await generateText(provider, userPrompt, systemPrompt, config);
};

/**
 * 生成客服回复
 * @param {String} userPrompt 用户问题
 * @param {String} provider AI提供商（可选，默认从环境变量获取）
 * @param {Object} config 配置选项（可选）
 * @returns {Promise<String>} AI生成的客服回复
 */
const generateCustomerServiceReply = async (userPrompt, provider = process.env.DEFAULT_AI_PROVIDER || 'deepseek', config = {}) => {
  const systemPrompt = getSystemPrompt('customer_service');
  return await generateText(provider, userPrompt, systemPrompt, config);
};

/**
 * 内容审核
 * @param {String} userPrompt 需要审核的内容
 * @param {String} provider AI提供商（可选，默认从环境变量获取）
 * @param {Object} config 配置选项（可选）
 * @returns {Promise<String>} AI生成的审核结果
 */
const moderateContent = async (userPrompt, provider = process.env.DEFAULT_AI_PROVIDER || 'deepseek', config = {}) => {
  const systemPrompt = getSystemPrompt('content_moderation');
  return await generateText(provider, userPrompt, systemPrompt, config);
};

/**
 * 通用AI文本生成（支持自定义系统提示）
 * @param {String} userPrompt 用户输入
 * @param {String} customSystemPrompt 自定义系统提示
 * @param {String} provider AI提供商（可选，默认从环境变量获取）
 * @param {Object} config 配置选项（可选）
 * @returns {Promise<String>} AI生成的文本
 */
const generateCustomText = async (userPrompt, customSystemPrompt, provider = process.env.DEFAULT_AI_PROVIDER || 'deepseek', config = {}) => {
  return await generateText(provider, userPrompt, customSystemPrompt, config);
};

/**
 * 生成产品推荐分析
 * @param {String} userPrompt 产品推荐请求数据
 * @param {String} provider AI提供商（可选，默认从环境变量获取）
 * @param {Object} config 配置选项（可选）
 * @returns {Promise<String>} AI生成的产品推荐分析
 */
const generateProductRecommendation = async (userPrompt, provider = process.env.DEFAULT_AI_PROVIDER || 'deepseek', config = {}) => {
  const systemPrompt = getSystemPrompt('product_recommendation');
  return await generateText(provider, userPrompt, systemPrompt, config);
};

module.exports = {
  generateVotingAnalysis,
  generateProductComparison,
  generateCustomerServiceReply,
  moderateContent,
  generateCustomText,
  generateProductRecommendation
}; 