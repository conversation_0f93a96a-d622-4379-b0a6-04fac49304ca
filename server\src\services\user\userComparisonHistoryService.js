const UserComparisonHistory = require('../../models/UserComparisonHistory');
const User = require('../../models/User');

/**
 * 获取用户的AI产品对比历史记录
 * @param {string} userId - 用户ID
 * @param {number} page - 页码，默认为1
 * @param {number} limit - 每页数量，默认为20
 * @returns {Object} 包含历史记录和分页信息的对象
 */
const getUserComparisonHistory = async (userId, page = 1, limit = 20) => {
  try {
    // 验证用户是否存在
    const user = await User.findById(userId);
    if (!user) {
      return {
        success: false,
        error: '用户不存在',
        data: null
      };
    }

    // 验证分页参数
    const pageNum = Math.max(1, parseInt(page) || 1);
    const limitNum = Math.min(50, Math.max(1, parseInt(limit) || 20)); // 限制最大50条

    // 获取用户对比历史记录
    const result = await UserComparisonHistory.getUserHistory(userId, pageNum, limitNum);

    // 格式化返回数据
    const formattedHistories = result.histories.map(history => ({
      id: history._id,
      productNames: history.productNames,
      comparisonCacheId: history.comparisonCacheId?._id || null,
      createdAt: history.createdAt,
      updatedAt: history.updatedAt
    }));

    return {
      success: true,
      data: {
        histories: formattedHistories,
        pagination: result.pagination,
        total: result.histories.length
      }
    };

  } catch (error) {
    console.error('❌ 获取用户对比历史失败:', error.message);
    return {
      success: false,
      error: `获取对比历史失败: ${error.message}`,
      data: null
    };
  }
};


module.exports = {
  getUserComparisonHistory
};