<!--pages/user/feedback/list/list.wxml-->
<view class="page">
  <view class="container-fluid py-20">
    <!-- 新增反馈按钮 -->
    <view class="mb-20">
      <button class="btn btn-primary btn-full btn-large" bindtap="goToSubmitFeedback">
        <text class="mr-10">📝</text>
        <text>新增意见反馈</text>
      </button>
    </view>

    <!-- 反馈列表 -->
    <block wx:if="{{!isEmpty}}">
      <view class="feedback-list">
        <block wx:for="{{feedbackList}}" wx:key="_id">
          <feedback-item 
            feedback="{{item}}"
            bind:feedbacktap="goToFeedbackDetail"
          />
        </block>
      </view>
      
      <!-- 加载更多 -->
      <view class="load-more py-40 text-center" wx:if="{{hasMore}}">
        <view class="loading" wx:if="{{loading}}">
          <text class="text-secondary">加载中...</text>
        </view>
        <view class="load-tip" wx:else>
          <text class="text-light">上拉加载更多</text>
        </view>
      </view>
      
      <!-- 没有更多数据 -->
      <view class="no-more py-40 text-center" wx:if="{{!hasMore && feedbackList.length > 0}}">
        <text class="text-light">没有更多反馈了</text>
      </view>
    </block>
    
    <!-- 空状态 -->
    <view class="empty-state text-center py-40 px-40" wx:if="{{isEmpty && !loading}}">
      <image src="/assets/images/empty-feedback.png" class="empty-image mb-40"></image>
      <text class="empty-text text-secondary mb-20 d-block font-weight-bold">还没有提交过反馈</text>
      <text class="empty-desc text-light d-block font-size-small">快来提交您的宝贵意见吧~</text>
    </view>
    
    <!-- 加载状态 -->
    <view class="loading-state text-center py-40" wx:if="{{loading && feedbackList.length === 0}}">
      <text class="text-secondary">加载中...</text>
    </view>
  </view>
</view> 