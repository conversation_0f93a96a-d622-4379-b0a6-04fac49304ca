<!--pages/question/detail/detail.wxml-->
<view class="page">
  <view class="container">
    <!-- 加载状态 -->
    <view class="loading-container" wx:if="{{loading}}">
      <view class="loading-spinner"></view>
      <text>加载中...</text>
    </view>

    <!-- 问题详情和评论区域的视觉容器 -->
    <view class="question-content-container" wx:if="{{!loading && questionDetail}}">
      <!-- 问题详情 -->
      <view class="question-detail">
        <!-- 问题头部 -->
        <view class="question-header">
          <view class="user-info">
            <image class="avatar" src="{{questionDetail.user.avatar || '/assets/images/default-avatar.png'}}"></image>
            <view class="info">
              <text class="nickname">{{questionDetail.user.nickname}}</text>
              <text class="time">{{questionDetail.createdAt}}</text>
            </view>
            <!-- 删除按钮 - 仅对自己发布的问题显示 -->
            <view class="delete-question-btn" wx:if="{{isQuestionCreator}}" bindtap="deleteQuestion">
              <text class="iconfont icon-delete"></text>
              <text>删除</text>
            </view>
            <!-- 停止投票按钮 - 仅对自己发布的问题且状态为open时显示 -->
            <view class="close-question-btn" wx:if="{{isQuestionCreator && questionDetail.status === 'open'}}" bindtap="closeQuestion">
              <text class="iconfont icon-stop"></text>
              <text>停止投票</text>
            </view>
          </view>
        </view>

        <!-- 问题内容 -->
        <view class="question-content">
          <view class="title">{{questionDetail.title}}</view>
          
          <!-- 问题状态 -->
          <view class="status-container">
            <view class="status-badge {{questionDetail.status === 'open' ? 'status-open' : 'status-closed'}}">
              {{questionDetail.status === 'open' ? '进行中' : '已关闭'}}
            </view>
          </view>
          
          <!-- 标签显示 -->
          <view class="tags-container" wx:if="{{questionDetail.tags && questionDetail.tags.length > 0}}">
            <view class="tag-item" wx:for="{{questionDetail.tags}}" wx:key="*this">{{item}}</view>
          </view>
          
          <!-- 使用场景描述 -->
          <view class="scene-container" wx:if="{{questionDetail.scene}}">
            <view class="section-title">使用场景</view>
            <view class="scene-content">{{questionDetail.scene}}</view>
          </view>
          
          <!-- 关键考量因素 -->
          <view class="factors-container" wx:if="{{questionDetail.keyFactors}}">
            <view class="section-title">关键考量因素</view>
            <view class="factor-content">{{questionDetail.keyFactors}}</view>
          </view>
          
          <!-- 预算区间 -->
          <view class="budget-container" wx:if="{{questionDetail.budget && (questionDetail.budget.min || questionDetail.budget.max)}}">
            <view class="section-title">预算区间</view>
            <view class="budget-content">
              <text wx:if="{{questionDetail.budget.min}}">{{questionDetail.budget.min}}</text>
              <text wx:else>不限</text>
              <text class="budget-separator">-</text>
              <text wx:if="{{questionDetail.budget.max}}">{{questionDetail.budget.max}}</text>
              <text wx:else>不限</text>
              <text class="budget-currency">元</text>
            </view>
          </view>
          
          <!-- 问题图片 -->
          <view class="image-list" wx:if="{{questionDetail.images && questionDetail.images.length > 0}}">
            <image 
              class="question-image {{questionDetail.images.length === 1 ? 'single' : ''}}" 
              wx:for="{{questionDetail.images}}" 
              wx:key="*this" 
              src="{{item}}" 
              mode="{{questionDetail.images.length === 1 ? 'widthFix' : 'aspectFill'}}"
              catchtap="previewImage"
              data-urls="{{questionDetail.images}}"
              data-current="{{item}}"
            ></image>
          </view>
        </view>

        <!-- 投票选项 -->
        <view class="vote-section" wx:if="{{questionDetail.options && questionDetail.options.length > 0}}">
          <view class="section-title">投票选项</view>
          
          <!-- 已关闭状态提示 -->
          <view class="vote-closed-tip" wx:if="{{questionDetail.status === 'closed'}}">
            <text class="iconfont icon-info"></text>
            <text>该问题已停止投票，无法再进行投票操作</text>
          </view>
          
          <view class="options-list">
            <view 
              class="option-item {{selectedOption === item.id ? 'selected' : ''}} {{questionDetail.hasVoted && questionDetail.votedOption === item.id ? 'voted' : ''}}" 
              wx:for="{{questionDetail.options}}" 
              wx:key="id"
              bindtap="{{questionDetail.hasVoted || questionDetail.status === 'closed' ? '' : 'selectOption'}}"
              data-id="{{item.id}}"
            >
              <view class="option-content">
                <view class="option-radio {{selectedOption === item.id ? 'checked' : ''}} {{questionDetail.hasVoted && questionDetail.votedOption === item.id ? 'voted' : ''}}"></view>
                <text class="option-text">{{item.content}}</text>
              </view>
              <view class="vote-stats">
                <view class="progress-bar">
                  <view class="progress" style="width: {{item.percentage}}%"></view>
                </view>
                <text class="vote-count">{{item.voteCount || 0}}票 ({{item.percentage}}%)</text>
              </view>
            </view>
          </view>
        </view>

        <!-- 问题数据 -->
        <view class="question-stats">
          <view class="stat-item">
            <text class="iconfont icon-comment"></text>
            <text>{{questionDetail.commentCount || 0}}评论</text>
          </view>
          <view class="stat-item">
            <text class="iconfont icon-vote"></text>
            <text>{{questionDetail.totalVotes || 0}}票</text>
          </view>
          <!-- 产品对比按钮 - 仅在有选项且选项数量>=2时显示 -->
          <button class="compare-btn" wx:if="{{questionDetail.options && questionDetail.options.length >= 2}}" 
                  bindtap="compareQuestionProducts" disabled="{{comparingProducts}}">
            <text class="iconfont icon-compare"></text>
            <text>{{comparingProducts ? '对比中...' : '选项产品一键参数对比'}}</text>
          </button>
          <button class="share-btn" open-type="share">
            <text class="iconfont icon-share"></text>
            <text>分享</text>
          </button>
        </view>
      </view>

      <!-- 评论列表 -->
      <view class="comment-section">
        <view class="section-title">全部理由 ({{answerList.length || 0}})</view>
        
        <view class="comment-list">
          <!-- 有回答 -->
          <block wx:if="{{answerList.length > 0}}">
            <view class="comment-item {{item.id === highlightedAnswerId ? 'highlighted' : ''}}" 
              wx:for="{{answerList}}" wx:key="id" id="answer-{{item.id}}">
              <view class="comment-user">
                <image class="avatar" src="{{item.user.avatar || '/assets/images/default-avatar.png'}}"></image>
                <view class="info">
                  <text class="nickname">{{item.user.nickname}}</text>
                  <text class="time">{{item.createdAt}}</text>
                </view>
              </view>
              <view class="comment-option">选择了: {{item.optionContent}}</view>
              <view class="comment-content" wx:if="{{item.content}}">{{item.content}}</view>
              <view class="comment-actions">
                <view class="action-item {{item.hasLiked ? 'liked' : ''}}" 
                    bindtap="toggleLike" 
                    data-answer-id="{{item.id}}" 
                    data-has-liked="{{item.hasLiked}}">
                  <text class="iconfont {{item.hasLiked ? 'icon-like-filled' : 'icon-like'}}"></text>
                  <text>{{item.likes || 0}}</text>
                </view>
                <view class="action-item" bindtap="showCommentModal" data-answer-id="{{item.id}}">
                  <text class="iconfont icon-comment"></text>
                  <text>{{item.commentCount || 0}}</text>
                </view>
              </view>
            </view>
          </block>
          
          <!-- 无回答 -->
          <view class="empty-comment" wx:elif="{{!answersLoading && answerList.length === 0}}">
            <text>暂无回答，快来回答问题吧</text>
          </view>
          
          <!-- 加载中 -->
          <view class="loading-container small" wx:if="{{answersLoading}}">
            <view class="loading-spinner"></view>
            <text>加载中...</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 理由输入对话框 -->
    <view class="reason-modal" wx:if="{{showReasonModal}}">
      <view class="reason-modal-mask" bindtap="closeReasonModal"></view>
      <view class="reason-modal-content">
        <view class="reason-modal-header">
          <text class="reason-modal-title">请输入您的理由</text>
          <text class="reason-modal-close" bindtap="closeReasonModal">×</text>
        </view>
        <view class="reason-modal-body">
          <!-- 添加温馨提示 -->
          <view class="reason-tip" wx:if="{{!questionDetail.requireReason}}">
            <text class="tip-text">温馨提示: 理由为选填项，您可以选择不填写直接提交</text>
          </view>
          
          <textarea 
            class="reason-textarea" 
            placeholder="说说你的理由..." 
            bindinput="inputComment"
            value="{{commentContent}}"
            maxlength="300"
            auto-focus
          ></textarea>
          
          <!-- 匿名选项 -->
          <view class="anonymous-option" bindtap="toggleAnonymous">
            <view class="checkbox {{isAnonymous ? 'checked' : ''}}">
              <view class="checkbox-inner" wx:if="{{isAnonymous}}"></view>
            </view>
            <text class="anonymous-text">匿名提交</text>
          </view>
          
          <view class="button-group">
            <button class="cancel-btn" bindtap="closeReasonModal">取消</button>
            <button class="submit-btn" bindtap="submitVote">提交</button>
          </view>
        </view>
      </view>
    </view>

    <!-- 评论模态框 -->
    <view class="comment-modal" wx:if="{{showCommentModal}}">
      <view class="comment-modal-mask" bindtap="closeCommentModal"></view>
      <view class="comment-modal-content">
        <view class="comment-modal-header">
          <text class="comment-modal-title">评论</text>
          <text class="comment-modal-close" bindtap="closeCommentModal">×</text>
        </view>
        
        <scroll-view class="comment-modal-body" scroll-y="true">
          <!-- 评论列表 -->
          <block wx:if="{{commentList[currentAnswerId] && commentList[currentAnswerId].length > 0}}">
            <view class="modal-comment-list">
              <view class="modal-comment-item {{commentItem.id === highlightedCommentId ? 'highlight-item' : ''}}" wx:for="{{commentList[currentAnswerId]}}" wx:key="id" wx:for-item="commentItem" id="comment-{{commentItem.id}}">
                <!-- 评论用户信息 -->
                <view class="modal-comment-user">
                  <image class="avatar small" src="{{commentItem.user.avatar || '/assets/images/default-avatar.png'}}"></image>
                  <view class="info">
                    <text class="nickname">{{commentItem.user.nickname}}</text>
                    <text class="time">{{commentItem.createdAt}}</text>
                  </view>
                  
                  <!-- 删除按钮 - 仅对自己的评论显示 -->
                  <view class="delete-btn" wx:if="{{commentItem.user.id === userInfo.id}}" 
                      catchtap="handleDeleteComment" 
                      data-comment-id="{{commentItem.id}}" 
                      data-answer-id="{{currentAnswerId}}">
                    <text class="iconfont icon-delete"></text>
                    <text>删除</text>
                  </view>
                </view>
                
                <!-- 评论内容 -->
                <view class="modal-comment-content">
                  <text>{{commentItem.content}}</text>
                </view>
                
                <!-- 回复按钮 - 顶级评论 -->
                <view class="modal-comment-actions">
                  <view class="modal-action-item" catchtap="replyToComment" data-comment-id="{{commentItem.id}}" data-nickname="{{commentItem.user.nickname}}">
                    <text class="iconfont icon-reply"></text>
                    <text>回复</text>
                  </view>
                </view>
                
                <!-- 显示回复数量及展开/收起按钮 -->
                <view class="reply-count-toggle" wx:if="{{commentItem.replyCount > 0}}" 
                    bindtap="toggleReplies" 
                    data-comment-id="{{commentItem.id}}" 
                    data-answer-id="{{currentAnswerId}}">
                  <text>{{showReplies[commentItem.id] ? '收起' : '展开'}}全部{{commentItem.replyCount}}条回复</text>
                  <text class="toggle-icon {{showReplies[commentItem.id] ? 'up' : 'down'}}">{{showReplies[commentItem.id] ? '∧' : '∨'}}</text>
                </view>
                
                <!-- 回复列表 -->
                <view class="modal-comment-replies" wx:if="{{showReplies[commentItem.id] && commentItem.replies && commentItem.replies.length > 0}}">
                  <view class="modal-reply-item {{replyItem.id === highlightedCommentId ? 'highlight-item' : ''}} {{replyItem.isDeleted ? 'deleted-reply' : ''}}" wx:for="{{commentItem.replies}}" wx:key="id" wx:for-item="replyItem" id="comment-{{replyItem.id}}">
                    <view class="modal-reply-user">
                      <image class="avatar mini" src="{{replyItem.user.avatar || '/assets/images/default-avatar.png'}}" wx:if="{{!replyItem.isDeleted}}"></image>
                      <view class="reply-content">
                        <block wx:if="{{!replyItem.isDeleted}}">
                          <view class="reply-user-info">
                            <text class="nickname">{{replyItem.user.nickname}}</text>
                            <text class="reply-to" wx:if="{{replyItem.targetUser}}">回复</text>
                            <text class="target-nickname" wx:if="{{replyItem.targetUser}}">{{replyItem.targetUser.nickname}}</text>
                          </view>
                          <text class="reply-text">{{replyItem.content}}</text>
                          <view class="reply-footer">
                            <text class="time">{{replyItem.createdAt}}</text>
                            <view class="reply-action">
                              <view class="modal-action-item" catchtap="replyToComment" 
                                  data-comment-id="{{commentItem.id}}" 
                                  data-nickname="{{replyItem.user.nickname}}"
                                  data-reply-id="{{replyItem.id}}">
                                <text>回复</text>
                              </view>
                              <!-- 删除按钮 - 仅对自己的回复显示 -->
                              <view class="delete-btn" wx:if="{{replyItem.user.id === userInfo.id}}" 
                                  catchtap="handleDeleteComment" 
                                  data-comment-id="{{replyItem.id}}" 
                                  data-answer-id="{{currentAnswerId}}">
                                <text class="iconfont icon-delete"></text>
                                <text>删除</text>
                              </view>
                            </view>
                          </view>
                        </block>
                        <view class="deleted-comment-text" wx:else>
                          <text>该评论已删除</text>
                        </view>
                      </view>
                    </view>
                  </view>
                  
                  <!-- 加载更多回复 -->
                  <view class="load-more-replies" 
                      wx:if="{{replyPage[commentItem.id] && replyPage[commentItem.id].page < replyPage[commentItem.id].pages}}" 
                      bindtap="loadMoreReplies" 
                      data-comment-id="{{commentItem.id}}" 
                      data-answer-id="{{currentAnswerId}}">
                    <text>加载更多回复</text>
                  </view>
                  
                  <!-- 回复加载中 -->
                  <view class="loading-container mini" wx:if="{{replyLoading[commentItem.id]}}">
                    <view class="loading-spinner"></view>
                    <text>加载中...</text>
                  </view>
                </view>
              </view>
            </view>
          </block>
          
          <!-- 无评论 -->
          <view class="empty-modal-comments" wx:elif="{{!commentLoading[currentAnswerId] && (!commentList[currentAnswerId] || commentList[currentAnswerId].length === 0)}}">
            <text>暂无评论，快来发表评论吧</text>
          </view>
          
          <!-- 加载中 -->
          <view class="loading-container small" wx:if="{{commentLoading[currentAnswerId]}}">
            <view class="loading-spinner"></view>
            <text>加载中...</text>
          </view>
        </scroll-view>
        
        <!-- 评论输入区 -->
        <view class="comment-modal-footer">
          <!-- 回复提示 -->
          <view class="reply-tip" wx:if="{{replyTo}}">
            <text>回复 {{replyTo.nickname}}</text>
            <text class="cancel-reply" bindtap="cancelReply">×</text>
          </view>
          
          <view class="comment-input-container">
            <input 
              class="comment-input" 
              placeholder="{{replyTo ? '回复 '+replyTo.nickname : '添加评论...'}}" 
              bindinput="inputReplyComment"
              value="{{replyComment}}"
              focus="{{!!replyTo}}"
            ></input>
            <view class="send-btn" bindtap="submitComment">发送</view>
          </view>
        </view>
      </view>
    </view>
  </view>
</view> 