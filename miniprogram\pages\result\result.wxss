/**
 * 选选小程序 - 结果页面样式
 * Result Page Styles for XuanXuan Mini Program
 * 依赖：app.wxss（已引入全局样式）
 */

/* ==================== 页面布局样式 Page Layout ==================== */

/* 页面容器 - 使用全局容器样式 */
.container {
  padding: 24rpx;
  background: #f7f8fc;
  min-height: 100vh;
}

/* ==================== 状态组件样式 State Components ==================== */

/* 加载状态 - 可复用的全局加载状态样式 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 500rpx;
}

.loading-spinner {
  width: 80rpx;
  height: 80rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #3B7ADB; /* 使用全局主色 */
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  margin-top: 30rpx;
  font-size: 28rpx;
  color: #999999; /* 使用全局文字颜色 */
}

/* 错误状态 - 可复用的全局错误状态样式 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
}

.error-text {
  margin: 30rpx 0;
  font-size: 28rpx;
  color: #666666; /* 使用全局次要文字颜色 */
  text-align: center;
}

/* 重试按钮 - 使用全局按钮样式 */
.retry-btn {
  /* 已在全局定义 .btn .btn-primary，此处只需添加额外样式 */
  margin-top: 20rpx;
}

/* ==================== 卡片组件样式 Card Components ==================== */

/* 结果卡片容器 - 使用全局卡片样式 */
.result-container {
  background: #ffffff;
  border-radius: 16rpx;
  padding: 30rpx;
  box-shadow: 0 2rpx 20rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 30rpx;
}

/* ==================== 结果页面特有样式 Result-specific Styles ==================== */

/* 问题标题区域 */
.result-header {
  margin-bottom: 40rpx;
  border-bottom: 1rpx solid #f0f0f0;
  padding-bottom: 24rpx;
}

.result-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333; /* 使用全局主要文字颜色 */
  margin-bottom: 24rpx;
  line-height: 1.4;
}

.result-stats {
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;
}

.stat-item {
  display: flex;
  flex-direction: column;
  margin-bottom: 16rpx;
}

.stat-label {
  font-size: 24rpx;
  color: #999999; /* 使用全局占位符文字颜色 */
  margin-bottom: 8rpx;
}

.stat-value {
  font-size: 28rpx;
  color: #333333; /* 使用全局主要文字颜色 */
  font-weight: 500;
}

/* 区块标题样式 */
.section-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333; /* 使用全局主要文字颜色 */
  margin-bottom: 24rpx;
  position: relative;
  padding-left: 20rpx;
}

.section-title::before {
  content: '';
  position: absolute;
  left: 0;
  top: 8rpx;
  height: 32rpx;
  width: 6rpx;
  background: #3B7ADB; /* 使用全局主色 */
  border-radius: 3rpx;
}

/* 投票选项列表样式 */
.options-list {
  margin-bottom: 40rpx;
}

.option-item {
  margin-bottom: 24rpx;
  padding: 20rpx;
  background: #f8f9fd;
  border-radius: 12rpx;
}

.winning-option {
  background: rgba(59, 122, 219, 0.1); /* 使用全局主色的透明版本 */
  border: 1rpx solid rgba(59, 122, 219, 0.3);
}

.option-header {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
}

.option-rank {
  width: 40rpx;
  height: 40rpx;
  line-height: 40rpx;
  text-align: center;
  background: #dddddd;
  color: #666666;
  border-radius: 50%;
  margin-right: 16rpx;
  font-size: 24rpx;
}

.winning-option .option-rank {
  background: #3B7ADB; /* 使用全局主色 */
  color: #ffffff;
}

.option-content {
  flex: 1;
  font-size: 28rpx;
  color: #333333; /* 使用全局主要文字颜色 */
}

.option-count {
  font-size: 26rpx;
  color: #666666; /* 使用全局次要文字颜色 */
  margin-left: 16rpx;
}

/* 进度条样式 */
.option-progress {
  height: 32rpx;
  background: #ebebeb;
  border-radius: 16rpx;
  position: relative;
  overflow: hidden;
}

.progress-bar {
  height: 100%;
  background: linear-gradient(90deg, #3B7ADB, #52c0ff); /* 使用全局主色渐变 */
  border-radius: 16rpx;
}

.progress-text {
  position: absolute;
  right: 16rpx;
  top: 0;
  line-height: 32rpx;
  font-size: 22rpx;
  color: #666666; /* 使用全局次要文字颜色 */
}

/* ==================== 商品链接样式 Product Links ==================== */

.product-links {
  margin-bottom: 40rpx;
}

.links-list {
  background: #f8f9fd;
  border-radius: 12rpx;
  padding: 16rpx;
}

.link-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 16rpx;
  border-bottom: 1rpx solid rgba(0, 0, 0, 0.05);
}

.link-item:last-child {
  border-bottom: none;
}

.link-platform {
  display: flex;
  align-items: center;
}

.platform-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 12rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 6rpx;
  font-size: 24rpx;
  font-weight: bold;
  color: white;
}

.platform-common {
  background-color: #3B7ADB; /* 使用全局主色 */
}

/* 复制按钮 - 使用全局按钮样式的小尺寸版本 */
.copy-btn {
  background: #3B7ADB; /* 使用全局主色 */
  color: white;
  font-size: 24rpx;
  padding: 8rpx 24rpx;
  border-radius: 30rpx;
  line-height: 1.5;
  margin: 0;
  border: none;
}

.copy-btn:active {
  background: #2E63B8; /* 使用全局主色的深色版本 */
  transform: scale(0.98);
}

.link-tips {
  font-size: 22rpx;
  color: #999999; /* 使用全局占位符文字颜色 */
  text-align: center;
  margin-top: 16rpx;
}

/* ==================== 热门理由样式 Top Reasons ==================== */

.top-reasons {
  margin-bottom: 40rpx;
}

.reason-item {
  padding: 24rpx;
  background: #f8f9fd;
  border-radius: 12rpx;
  margin-bottom: 20rpx;
}

.reason-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 16rpx;
}

.reason-user {
  display: flex;
  align-items: center;
}

.user-avatar {
  width: 50rpx;
  height: 50rpx;
  border-radius: 50%;
  margin-right: 12rpx;
}

.user-name {
  font-size: 26rpx;
  color: #666666; /* 使用全局次要文字颜色 */
}

.option-tag {
  font-size: 22rpx;
  color: #3B7ADB; /* 使用全局主色 */
  background: rgba(59, 122, 219, 0.1);
  padding: 4rpx 12rpx;
  border-radius: 30rpx;
}

.reason-content {
  font-size: 28rpx;
  color: #333333; /* 使用全局主要文字颜色 */
  line-height: 1.5;
  margin-bottom: 16rpx;
}

.reason-likes {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #999999; /* 使用全局占位符文字颜色 */
}

.reason-likes .iconfont {
  margin-right: 8rpx;
}

/* ==================== 水印样式 Watermark ==================== */

.watermark {
  text-align: center;
  margin: 40rpx 0 20rpx;
  font-size: 24rpx;
  color: #999999; /* 使用全局占位符文字颜色 */
  opacity: 0.7;
  display: flex;
  flex-direction: column;
}

.watermark-desc {
  font-size: 20rpx;
  margin-top: 6rpx;
}

/* ==================== 操作按钮区域 Action Bar ==================== */

.action-bar {
  display: flex;
  justify-content: space-around;
  padding: 24rpx 0;
}

/* 操作按钮 - 基于全局按钮样式扩展 */
.action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 300rpx;
  height: 80rpx;
  border-radius: 40rpx;
  font-size: 28rpx;
  border: none;
  transition: all 0.3s ease;
}

.action-btn .iconfont {
  margin-right: 8rpx;
}

.action-btn:active {
  transform: scale(0.98);
}

.save-btn {
  background: #3B7ADB; /* 使用全局主色 */
  color: #ffffff;
}

.save-btn:active {
  background: #2E63B8; /* 使用全局主色的深色版本 */
}

.share-btn {
  background: #52c41a;
  color: #ffffff;
}

.share-btn:active {
  background: #449a18;
}

/* ==================== Canvas 样式 Canvas ==================== */

.share-canvas {
  position: fixed;
  left: -2000px;
  width: 300px;
  height: 500px;
}

/* ==================== 分析结果样式 Analysis Result ==================== */

.analysis-result {
  margin-top: 40rpx;
  padding-top: 30rpx;
  border-top: 1rpx solid #f0f0f0;
}

.markdown-content {
  font-size: 28rpx;
  line-height: 1.6;
  color: #333333; /* 使用全局主要文字颜色 */
  word-break: break-all;
}

/* Markdown 内容样式 */
.markdown-content view {
  margin-bottom: 20rpx;
}

.markdown-content h1,
.markdown-content h2,
.markdown-content h3,
.markdown-content h4,
.markdown-content h5,
.markdown-content h6 {
  font-weight: bold;
  margin: 24rpx 0 16rpx;
  color: #333333; /* 使用全局主要文字颜色 */
}

.markdown-content h1 {
  font-size: 36rpx;
  padding-bottom: 10rpx;
  border-bottom: 1px solid #eeeeee;
}

.markdown-content h2 {
  font-size: 32rpx;
  padding-bottom: 8rpx;
  border-bottom: 1px solid #f0f0f0;
}

.markdown-content h3 {
  font-size: 30rpx;
}

.markdown-content h4 {
  font-size: 28rpx;
}

.markdown-content p {
  margin: 16rpx 0;
}

.markdown-content ul,
.markdown-content ol {
  padding-left: 40rpx;
  margin: 16rpx 0;
}

.markdown-content li {
  margin: 8rpx 0;
}

.markdown-content strong {
  font-weight: bold;
}

.markdown-content em {
  font-style: italic;
}

.markdown-content code {
  background-color: #f0f0f0;
  padding: 2rpx 6rpx;
  border-radius: 4rpx;
  font-family: Consolas, monospace;
}

.markdown-content pre {
  background-color: #f6f8fa;
  padding: 16rpx;
  border-radius: 8rpx;
  overflow-x: auto;
  margin: 16rpx 0;
}

.markdown-content blockquote {
  padding: 0 16rpx;
  border-left: 4px solid #dddddd;
  color: #666666; /* 使用全局次要文字颜色 */
  margin: 16rpx 0;
}

.markdown-content table {
  border-collapse: collapse;
  margin: 16rpx 0;
  width: 100%;
}

.markdown-content table th,
.markdown-content table td {
  border: 1px solid #dddddd;
  padding: 12rpx;
  text-align: left;
}

.markdown-content table th {
  background-color: #f6f8fa;
}

/* ==================== 处理中状态样式 Processing State ==================== */

.processing-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px 20px;
  min-height: 300px;
}

.processing-animation {
  margin-bottom: 20px;
}

.processing-icon {
  width: 80px;
  height: 80px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;
}

.processing-spinner {
  width: 60px;
  height: 60px;
  border: 4px solid rgba(59, 122, 219, 0.1); /* 使用全局主色的透明版本 */
  border-radius: 50%;
  border-top-color: #3B7ADB; /* 使用全局主色 */
  animation: spin 1.5s linear infinite;
}

.processing-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 10px;
  color: #333333; /* 使用全局主要文字颜色 */
}

.processing-desc {
  font-size: 16px;
  color: #666666; /* 使用全局次要文字颜色 */
  margin-bottom: 20px;
}

.processing-tips {
  display: flex;
  flex-direction: column;
  align-items: center;
  background-color: #f5f5f5;
  padding: 15px;
  border-radius: 8px;
  width: 90%;
}

.processing-tips text {
  font-size: 14px;
  color: #888888;
  line-height: 1.5;
}

/* ==================== 原始文本和无分析内容样式 Raw Text & No Analysis ==================== */

.raw-summary {
  background: #f8f9fa;
  padding: 24rpx;
  border-radius: 12rpx;
  border-left: 6rpx solid #3B7ADB; /* 使用全局主色 */
  margin: 20rpx 0;
}

.raw-summary text {
  font-size: 28rpx;
  line-height: 1.6;
  color: #333333; /* 使用全局主要文字颜色 */
  white-space: pre-wrap;
  word-break: break-all;
}

.no-analysis {
  text-align: center;
  padding: 60rpx 0;
  color: #999999; /* 使用全局占位符文字颜色 */
}

.no-analysis text {
  font-size: 28rpx;
}

/* ==================== towxml 组件样式优化 towxml Optimization ==================== */

.analysis-result .h2w {
  padding: 20rpx;
  line-height: 1.6;
}

.analysis-result .h2w__p {
  margin-bottom: 16rpx;
}

.analysis-result .towxml-wrapper {
  line-height: 1.6;
}

.analysis-result .towxml-wrapper .h1,
.analysis-result .towxml-wrapper .h2,
.analysis-result .towxml-wrapper .h3 {
  color: #333333; /* 使用全局主要文字颜色 */
  margin: 20rpx 0 16rpx 0;
  font-weight: bold;
}

.analysis-result .towxml-wrapper .p {
  margin: 12rpx 0;
  color: #555555;
  line-height: 1.6;
}

.analysis-result .towxml-wrapper .ul,
.analysis-result .towxml-wrapper .ol {
  margin: 16rpx 0;
  padding-left: 32rpx;
}

.analysis-result .towxml-wrapper .li {
  margin: 8rpx 0;
  color: #555555;
} 