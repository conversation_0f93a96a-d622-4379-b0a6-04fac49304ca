const app = getApp();

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 推荐结果数据
    recommendedProducts: [],
    // 用户问题信息
    questionInfo: {},
    // 加载状态
    loading: false,
    // 产品对比相关
    compareVisible: false,
    isInCompareList: {} // 用于存储每个产品是否在对比列表中 {skuId: true/false}
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    console.log('AI推荐结果页面加载，参数:', options);

    // 从页面参数中获取数据
    if (options.data) {
      try {
        const pageData = JSON.parse(decodeURIComponent(options.data));
        console.log('解析的页面数据:', pageData);

        this.setData({
          recommendedProducts: pageData.recommendedProducts || [],
          questionInfo: pageData.questionInfo || {}
        });

        // 检查对比状态
        this.checkCompareStatus();
      } catch (error) {
        console.error('解析页面数据失败:', error);
        wx.showToast({
          title: '数据加载失败',
          icon: 'none'
        });
      }
    }
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady: function () {
    // 设置页面标题
    wx.setNavigationBarTitle({
      title: 'AI推荐结果'
    });
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function () {
    // 每次显示页面时检查对比状态
    this.checkCompareStatus();
  },

  /**
   * 检查产品对比状态
   */
  checkCompareStatus: function() {
    const { recommendedProducts } = this.data;
    const isInCompareList = {};

    recommendedProducts.forEach(product => {
      if (product.originalData && product.originalData.skuId) {
        const skuId = product.originalData.skuId;
        isInCompareList[skuId] = app.isInCompare(skuId);
      }
    });

    this.setData({ isInCompareList });
  },

  /**
   * 切换推荐理由展开/收起状态
   */
  toggleReasonExpanded: function(e) {
    const { index } = e.currentTarget.dataset;
    const { recommendedProducts } = this.data;

    recommendedProducts[index].expanded = !recommendedProducts[index].expanded;

    this.setData({
      recommendedProducts
    });
  },

  /**
   * 添加到对比或从对比中移除
   */
  addToCompare: function(e) {
    const { product } = e.currentTarget.dataset;
    const skuId = product.originalData.skuId;

    if (!skuId) {
      wx.showToast({
        title: '产品ID不存在，无法对比',
        icon: 'none'
      });
      return;
    }

    const isInCompare = app.isInCompare(skuId);

    if (isInCompare) {
      // 如果已在对比列表中，则移除
      const compareProducts = app.getCompareProducts();
      const index = compareProducts.findIndex(p => p.skuId === skuId);
      if (index > -1) {
        app.removeFromCompare(index);
        wx.showToast({ title: '已移出对比', icon: 'success' });
      }
    } else {
      // 如果不在对比列表中，则添加
      const productToAdd = {
        skuId: skuId,
        skuName: product.name,
        // 其他需要保存到对比列表的信息
      };
      const success = app.addToCompare(productToAdd);
      if (success) {
        wx.showToast({ title: '已加入对比', icon: 'success' });
      }
    }

    // 更新对比状态
    this.checkCompareStatus();
  },

  /**
   * 切换产品对比输入组件显示状态
   */
  onToggleCompareVisible: function() {
    this.setData({
      compareVisible: !this.data.compareVisible
    });
  },

  /**
   * 查看产品详情
   */
  viewDetail: function(e) {
    const { product } = e.currentTarget.dataset;
    const skuName = product.name;

    if (!skuName) {
      wx.showToast({
        title: '产品名称不存在，无法查看详情',
        icon: 'none'
      });
      return;
    }

    wx.navigateTo({
      url: `/pages/product/product_detail/product_detail?productName=${skuName}`
    });
  },

  /**
   * 重新推荐
   */
  reRecommend: function() {
    wx.navigateBack({
      delta: 1
    });
  },

  /**
   * 页面分享
   */
  onShareAppMessage: function () {
    return {
      title: 'AI推荐结果 - 选选',
      path: '/pages/product/ai-recommend-result/ai-recommend-result'
    };
  }
});