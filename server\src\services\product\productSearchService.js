const NewProduct = require('../../models/NewProduct');

/**
 * 字符串标准化处理（用于搜索匹配）
 * @param {String} str 要标准化的字符串
 * @returns {String} 标准化后的字符串
 */
const normalizeStringForSearch = (str) => {
  if (!str || typeof str !== 'string') return '';
  
  return str
    .toLowerCase()                          // 转换为小写
    .replace(/\s+/g, '')                   // 移除所有空格（包括多个连续空格）
    .replace(/[^\w\u4e00-\u9fa5]/g, '')    // 移除标点符号，只保留字母、数字、中文字符
    .trim();                               // 移除首尾空白
};

/**
 * 智能字符串匹配检查
 * 使用searchMatch字段进行匹配，同时支持标准化匹配
 * @param {String} searchMatchValue 产品的searchMatch字段值
 * @param {String} keyword 搜索关键词
 * @returns {Object} 匹配结果 { matched: boolean, matchType: string, score: number }
 */
const checkStringMatch = (searchMatchValue, keyword) => {
  const originalSearchMatch = (searchMatchValue || '').toLowerCase();
  const originalKeyword = keyword.toLowerCase();
  
  const normalizedSearchMatch = normalizeStringForSearch(searchMatchValue || '');
  const normalizedKeyword = normalizeStringForSearch(keyword);
  
  // 检查各种匹配情况
  const matches = {
    // 原始字符串匹配（优先级最高）
    originalExact: originalSearchMatch === originalKeyword,
    originalPrefix: originalSearchMatch.startsWith(originalKeyword),
    originalContains: originalSearchMatch.includes(originalKeyword),
    
    // 标准化字符串匹配（解决空格等问题）
    normalizedExact: normalizedSearchMatch === normalizedKeyword,
    normalizedPrefix: normalizedSearchMatch.startsWith(normalizedKeyword),
    normalizedContains: normalizedSearchMatch.includes(normalizedKeyword)
  };
  
  // 根据匹配类型返回结果
  if (matches.originalExact || matches.normalizedExact) {
    return { matched: true, matchType: 'exact', score: 100 };
  } else if (matches.originalPrefix || matches.normalizedPrefix) {
    return { matched: true, matchType: 'prefix', score: 80 };
  } else if (matches.originalContains || matches.normalizedContains) {
    return { matched: true, matchType: 'contains', score: 60 };
  }
  
  return { matched: false, matchType: 'none', score: 0 };
};

/**
 * 🆕 智能分词匹配检查
 * 支持中英文分词匹配，解决"华为mate70"匹配"华为huaweimate70"的问题
 * @param {String} searchMatchValue 产品的searchMatch字段值
 * @param {String} keyword 搜索关键词
 * @returns {Object} 匹配结果 { matched: boolean, matchType: string, score: number }
 */
const checkIntelligentMatch = (searchMatchValue, keyword) => {
  if (!searchMatchValue || !keyword) return { matched: false, matchType: 'none', score: 0 };
  
  const searchMatch = searchMatchValue.toLowerCase();
  const searchKeyword = keyword.toLowerCase();
  
  // 1. 先尝试原有的完整匹配
  const directMatch = checkStringMatch(searchMatchValue, keyword);
  if (directMatch.matched) {
    return directMatch;
  }
  
  // 2. 🆕 分词匹配：将关键词拆分为中文和英文数字部分
  const chineseChars = searchKeyword.match(/[\u4e00-\u9fa5]+/g) || [];
  const englishChars = searchKeyword.match(/[a-zA-Z0-9]+/g) || [];
  
  // 如果没有分词，返回不匹配
  if (chineseChars.length === 0 && englishChars.length === 0) {
    return { matched: false, matchType: 'none', score: 0 };
  }
  
  let matchScore = 0;
  let matchedParts = 0;
  const totalParts = chineseChars.length + englishChars.length;
  
  console.log(`分词匹配分析 - 关键词: "${keyword}"`);
  console.log(`中文部分: [${chineseChars.join(', ')}]`);
  console.log(`英文数字部分: [${englishChars.join(', ')}]`);
  console.log(`目标searchMatch: "${searchMatch}"`);
  
  // 检查中文部分匹配
  chineseChars.forEach((chinese, index) => {
    if (searchMatch.includes(chinese)) {
      console.log(`✅ 中文部分 "${chinese}" 匹配成功`);
      // 中文匹配给予更高权重，因为中文通常是品牌或关键信息
      matchScore += 35;
      matchedParts++;
      
      // 如果中文部分在开头，额外加分
      if (searchMatch.startsWith(chinese)) {
        matchScore += 5;
      }
    } else {
      console.log(`❌ 中文部分 "${chinese}" 未匹配`);
    }
  });
  
  // 检查英文数字部分匹配
  englishChars.forEach((english, index) => {
    if (searchMatch.includes(english)) {
      console.log(`✅ 英文数字部分 "${english}" 匹配成功`);
      // 英文数字匹配给予标准权重
      matchScore += 30;
      matchedParts++;
      
      // 如果英文部分在末尾，额外加分（通常是型号）
      if (searchMatch.endsWith(english)) {
        matchScore += 5;
      }
    } else {
      console.log(`❌ 英文数字部分 "${english}" 未匹配`);
    }
  });
  
  console.log(`匹配结果: ${matchedParts}/${totalParts} 部分匹配，得分: ${matchScore}`);
  
  // 3. 判断匹配结果
  const matchThreshold = 0.8; // 80%的部分需要匹配
  const requiredMatches = Math.ceil(totalParts * matchThreshold);
  
  if (matchedParts >= requiredMatches && matchedParts > 0) {
    // 根据匹配完整度确定匹配类型
    let matchType = 'intelligent_partial';
    let finalScore = matchScore;
    
    if (matchedParts === totalParts) {
      // 所有部分都匹配
      matchType = 'intelligent_complete';
      finalScore = Math.min(matchScore + 10, 85); // 最高85分，低于精确匹配但高于包含匹配
    } else {
      // 部分匹配
      finalScore = Math.min(matchScore, 70); // 最高70分
    }
    
    console.log(`🎉 智能匹配成功: 类型=${matchType}, 最终得分=${finalScore}`);
    
    return {
      matched: true,
      matchType: matchType,
      score: finalScore
    };
  }
  
  console.log(`❌ 智能匹配失败: 匹配部分不足`);
  return { matched: false, matchType: 'none', score: 0 };
};

/**
 * 🆕 组合匹配检查（整合所有匹配策略）
 * @param {String} searchMatchValue 产品的searchMatch字段值
 * @param {String} keyword 搜索关键词
 * @returns {Object} 最佳匹配结果
 */
const checkCombinedMatch = (searchMatchValue, keyword) => {
  // 1. 先尝试智能匹配（包含了原有匹配逻辑）
  const intelligentResult = checkIntelligentMatch(searchMatchValue, keyword);
  
  // 2. 如果智能匹配成功，直接返回
  if (intelligentResult.matched) {
    return intelligentResult;
  }
  
  // 3. 如果智能匹配失败，尝试原有的字符串匹配作为备选
  return checkStringMatch(searchMatchValue, keyword);
};

/**
 * 转义正则表达式特殊字符
 * @param {String} str 要转义的字符串
 * @returns {String} 转义后的字符串
 */
const escapeRegex = (str) => {
  return str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
};

/**
 * 构建智能搜索查询（使用searchMatch字段优化）
 * @param {Object} baseQuery 基础查询条件
 * @param {String} keyword 搜索关键词
 * @returns {Array} 搜索查询配置数组
 */
const buildIntelligentSearchQueries = (baseQuery, keyword) => {
  const queries = [];
  
  const escapedKeyword = escapeRegex(keyword);
  const normalizedKeyword = normalizeStringForSearch(keyword);
  const escapedNormalizedKeyword = escapeRegex(normalizedKeyword);
  
  // 🆕 优先使用searchMatch字段进行匹配
  queries.push(
    // searchMatch精确匹配
    {
      query: { ...baseQuery, searchMatch: { $regex: `^${escapedNormalizedKeyword}$`, $options: 'i' } },
      weight: 100,
      matchType: 'searchMatch_exact',
      keyword: keyword
    },
    // searchMatch前缀匹配
    {
      query: { ...baseQuery, searchMatch: { $regex: `^${escapedNormalizedKeyword}`, $options: 'i' } },
      weight: 90,
      matchType: 'searchMatch_prefix',
      keyword: keyword
    },
    // searchMatch包含匹配
    {
      query: { ...baseQuery, searchMatch: { $regex: escapedNormalizedKeyword, $options: 'i' } },
      weight: 80,
      matchType: 'searchMatch_contains',
      keyword: keyword
    }
  );
  
  // 🆕 添加分词匹配查询
  const chineseMatch = keyword.match(/[\u4e00-\u9fa5]+/g);
  const englishMatch = keyword.match(/[a-zA-Z0-9]+/g);
  
  if (chineseMatch && englishMatch) {
    // 构建分词查询：同时包含中文和英文部分
    const chineseRegex = chineseMatch.map(escapeRegex).join('.*');
    const englishRegex = englishMatch.map(escapeRegex).join('.*');
    
    queries.push({
      query: {
        ...baseQuery,
        $and: [
          { searchMatch: { $regex: chineseRegex, $options: 'i' } },
          { searchMatch: { $regex: englishRegex, $options: 'i' } }
        ]
      },
      weight: 85,
      matchType: 'searchMatch_split',
      keyword: keyword
    });
  }
  
  // 🆕 如果原始关键词与标准化关键词不同，也尝试用原始关键词匹配
  if (keyword.toLowerCase() !== normalizedKeyword && keyword.trim().length > 0) {
    queries.push(
      {
        query: { ...baseQuery, searchMatch: { $regex: escapedKeyword, $options: 'i' } },
        weight: 70,
        matchType: 'searchMatch_original',
        keyword: keyword
      }
    );
  }
  
  // 保留品牌名称匹配作为补充
  queries.push({
    query: { ...baseQuery, brandName: { $regex: escapedKeyword, $options: 'i' } },
    weight: 40,
    matchType: 'brand',
    keyword: keyword
  });
  
  return queries;
};

/**
 * 计算产品名称相关性得分（使用searchMatch字段优化版）
 * @param {String} productName 产品名称
 * @param {String} keyword 搜索关键词
 * @param {Number} baseWeight 基础权重
 * @param {String} matchType 匹配类型
 * @param {String} matchedKeyword 实际匹配的关键词
 * @returns {Number} 相关性得分
 */
const calculateEnhancedRelevanceScore = (productName, keyword, baseWeight, matchType, matchedKeyword = keyword) => {
  let score = baseWeight;
  
  // 🆕 对于不同的searchMatch匹配类型给予相应奖励
  if (matchType && matchType.includes('searchMatch')) {
    score += 10; // searchMatch匹配奖励
    
    // 🆕 对智能匹配给予额外奖励
    if (matchType.includes('intelligent')) {
      score += 5; // 智能分词匹配奖励
    }
    
    // 对分词匹配给予奖励
    if (matchType.includes('split')) {
      score += 8; // 分词匹配奖励
    }
  }
  
  // 标准化关键词进行匹配分析
  const normalizedKeyword = normalizeStringForSearch(matchedKeyword);
  const normalizedName = normalizeStringForSearch(productName);
  
  // 关键词在名称中的位置权重（越靠前越好）
  const keywordIndex = normalizedName.indexOf(normalizedKeyword);
  if (keywordIndex !== -1) {
    score += Math.max(0, 20 - keywordIndex * 0.5);
  }
  
  // 产品名称长度权重（较短的名称通常更相关）
  const nameLength = productName.length;
  score += Math.max(0, 100 - nameLength) * 0.1;
  
  // 关键词覆盖度（关键词占产品名称的比例）
  if (normalizedName.length > 0) {
    const coverage = normalizedKeyword.length / normalizedName.length;
    score += coverage * 10;
  }
  
  // 对标准化匹配类型给予额外奖励
  if (matchType && matchType.includes('normalized')) {
    score += 5; // 智能匹配奖励
  }
  
  return score;
};

/**
 * 获取补充匹配结果（客户端智能匹配 - 使用searchMatch字段优化版）
 * 当数据库查询结果不足时，使用客户端匹配作为补充
 * @param {Object} baseQuery 基础查询条件
 * @param {String} keyword 搜索关键词
 * @param {Number} limit 查询限制
 * @param {Set} seenNames 已经找到的产品名称集合
 * @returns {Promise<Array>} 补充匹配结果
 */
const getSupplementaryMatches = async (baseQuery, keyword, limit, seenNames) => {
  // 获取更多产品进行客户端匹配，现在选择searchMatch字段
  const allProducts = await NewProduct.find(baseQuery)
    .select('skuName brandName searchMatch imageUrl commonSpecs.基本信息.上市日期 commonSpecs.基本信息.国内发布日期 commonSpecs.基本信息.国内发布时间 productType priceRange')
    .limit(limit)
    .lean();

  const supplementResults = [];

  console.log(`开始补充匹配，产品数量: ${allProducts.length}, 关键词: "${keyword}"`);

  for (const product of allProducts) {
    const productName = product.skuName;
    
    // 跳过已经找到的产品
    if (seenNames.has(productName)) {
      continue;
    }

    // 🆕 使用组合匹配策略进行智能匹配检查
    const matchResult = checkCombinedMatch(product.searchMatch, keyword);
    
    if (matchResult.matched) {
      console.log(`💡 补充匹配成功: ${productName} (${matchResult.matchType}, 得分: ${matchResult.score})`);
      
      // 获取产品的上市日期
      const basicInfo = product.commonSpecs?.基本信息;
      const releaseDate = basicInfo ? (basicInfo.上市日期 || basicInfo.国内发布日期 || basicInfo.国内发布时间 || '未知') : '未知';
      
      supplementResults.push({
        skuName: productName,
        imageUrl: product.imageUrl,
        上市日期: releaseDate,
        productType: product.productType,
        brandName: product.brandName,
        priceRange: product.priceRange || { min: 0, max: 0 }, // 添加价格范围字段
        matchType: `client_${matchResult.matchType}`,
        relevanceScore: matchResult.score,
        matchedKeyword: keyword
      });
      
      seenNames.add(productName);
    }
  }

  console.log(`补充匹配完成，新增结果: ${supplementResults.length}`);
  return supplementResults;
};

/**
 * 搜索匹配的产品名称（用于自动完成功能）
 * 增强版：支持智能字符串匹配，解决空格、标点符号等匹配问题
 * @param {String} keyword 搜索关键词
 * @param {Number} limit 返回数量限制
 * @param {String} category 产品类别筛选
 * @returns {Promise<Object>} 搜索结果
 */
const searchProducts = async (keyword, limit = 10, category = '') => {
  const startTime = Date.now();

  try {
    // 验证输入参数
    if (!keyword || !keyword.trim()) {
      return {
        success: false,
        message: '搜索关键词不能为空'
      };
    }
    console.log("关键字是", keyword)
    const trimmedKeyword = keyword.trim();
    
    // 限制搜索关键词长度
    if (trimmedKeyword.length > 50) {
      return {
        success: false,
        message: '搜索关键词过长，请控制在50字符以内'
      };
    }

    // 限制返回数量
    const searchLimit = Math.min(Math.max(limit, 1), 20);

    // 构建基础查询条件
    const baseQuery = {
      supportsComparison: true // 只搜索支持对比的产品
    };

    // 添加类别筛选
    if (category && category.trim()) {
      baseQuery.productType = category.trim();
    }

    // 构建智能搜索查询
    const searchQueries = buildIntelligentSearchQueries(baseQuery, trimmedKeyword);

    // 执行搜索并收集产品名称
    const allProductNames = [];
    const seenNames = new Set();

    for (const searchQuery of searchQueries) {
      try {
        // 使用基础查询（移除聚合查询逻辑）
        const results = await NewProduct.find(searchQuery.query)
          .select('skuName brandName imageUrl commonSpecs.基本信息.上市日期 commonSpecs.基本信息.上市时间 commonSpecs.基本信息.国内发布日期 commonSpecs.基本信息.国内发布时间 productType priceRange')
          .limit(searchLimit * 2)
          .lean();

        // 提取产品信息并去重
        for (const product of results) {
          const productName = product.skuName;
          
          // 避免重复产品名称
          if (!seenNames.has(productName)) {
            seenNames.add(productName);
            
            // 获取产品的上市日期
            const basicInfo = product.commonSpecs?.基本信息;
            const releaseDate = basicInfo ? (basicInfo.上市日期 || basicInfo.上市时间 || basicInfo.国内发布日期 || basicInfo.国内发布时间 || '未知') : '未知';
            
            // 使用增强的相关性得分计算
            const relevanceScore = calculateEnhancedRelevanceScore(
              productName, 
              trimmedKeyword, 
              searchQuery.weight, 
              searchQuery.matchType,
              searchQuery.keyword
            );
            
            allProductNames.push({
              skuName: productName,
              imageUrl: product.imageUrl,
              上市日期: releaseDate,
              productType: product.productType,
              brandName: product.brandName,
              priceRange: product.priceRange || { min: 0, max: 0 }, // 添加价格范围
              matchType: searchQuery.matchType,
              relevanceScore
            });
          }
        }
      } catch (queryError) {
        console.error(`产品名称搜索查询执行失败 (${searchQuery.matchType}):`, queryError);
        // 继续执行其他查询
      }
    }

    // 如果常规查询结果不足，使用客户端智能匹配作为补充
    if (allProductNames.length < searchLimit) {
      try {
        const supplementResults = await getSupplementaryMatches(
          baseQuery, 
          trimmedKeyword, 
          searchLimit * 3,
          seenNames
        );
        allProductNames.push(...supplementResults);
      } catch (supplementError) {
        console.error('补充匹配查询失败:', supplementError);
      }
    }

    // 按相关性得分排序并限制结果数量
    const sortedProducts = allProductNames
      .sort((a, b) => b.relevanceScore - a.relevanceScore)
      .slice(0, searchLimit)
      .map(item => ({
        skuName: item.skuName,
        imageUrl: item.imageUrl,
        上市日期: item.上市日期,
        productType: item.productType,
        brandName: item.brandName,
        priceRange: item.priceRange || { min: 0, max: 0 } // 添加价格范围字段
      }));

    const searchTime = Date.now() - startTime;

    return {
      success: true,
      data: {
        products: sortedProducts,
        pagination: {
          page: 1,
          limit: searchLimit,
          total: sortedProducts.length,
          totalPages: 1,
          hasNextPage: false,
          hasPrevPage: false
        },
        searchTime,
        keyword: trimmedKeyword,
        searchStrategy: 'intelligent_matching' // 标识使用了智能匹配
      },
      message: `成功搜索到 ${sortedProducts.length} 个产品，按相关性排序`
    };

  } catch (error) {
    console.error('产品名称搜索失败:', error);
    
    const searchTime = Date.now() - startTime;
    
    return {
      success: false,
      error: error.message,
      message: '搜索服务暂时不可用，请稍后重试',
      data: {
        products: [],
        pagination: {
          page: 1,
          limit: searchLimit,
          total: 0,
          totalPages: 0,
          hasNextPage: false,
          hasPrevPage: false
        },
        searchTime,
        keyword: keyword
      }
    };
  }
};

module.exports = {
  searchProducts
};