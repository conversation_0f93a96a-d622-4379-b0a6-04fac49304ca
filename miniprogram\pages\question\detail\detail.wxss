/* pages/question/detail/detail.wxss */

/* 页面容器 */
.page {
  background-color: #f5f5f5;
  min-height: 100vh;
}

.container {
  padding-bottom: 30rpx;
}

/* 问题详情和评论的外层容器 */
.question-content-container {
  background-color: #fff;
  border-radius: 12rpx;
  margin: 20rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.08);
  overflow: hidden;
}

/* 加载容器 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 0;
}

.loading-container.small {
  padding: 30rpx 0;
}

.loading-spinner {
  width: 60rpx;
  height: 60rpx;
  border: 6rpx solid #f3f3f3;
  border-top: 6rpx solid #3B7ADB;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 20rpx;
}

.loading-container.small .loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border-width: 4rpx;
  margin-bottom: 10rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 问题详情 */
.question-detail {
  background-color: #fff;
  padding: 30rpx;
  padding-bottom: 20rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
  margin-bottom: 0;
  border-bottom: 1rpx solid #f0f0f0;
}

/* 问题头部 */
.question-header {
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f0f0f0;
  margin-bottom: 20rpx;
}

.user-info {
  display: flex;
  align-items: center;
}

.avatar {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 16rpx;
}

.info {
  display: flex;
  flex-direction: column;
  flex: 1;
}

.nickname {
  font-size: 28rpx;
  color: #333;
  font-weight: 500;
  margin-bottom: 4rpx;
}

.time {
  font-size: 24rpx;
  color: #999;
}

/* 删除问题按钮 */
.delete-question-btn {
  display: flex;
  align-items: center;
  padding: 8rpx 20rpx;
  background-color: #f5f5f5;
  border-radius: 30rpx;
  color: #ff4d4f;
  font-size: 24rpx;
  margin-left: auto;
}

.delete-question-btn .iconfont {
  margin-right: 8rpx;
  font-size: 26rpx;
}

/* 停止投票按钮 */
.close-question-btn {
  display: flex;
  align-items: center;
  padding: 8rpx 20rpx;
  background-color: #f5f5f5;
  border-radius: 30rpx;
  color: #ff9800;
  font-size: 24rpx;
  margin-left: 16rpx;
}

.close-question-btn .iconfont {
  margin-right: 8rpx;
  font-size: 26rpx;
}

/* 问题内容 */
.question-content {
  margin-bottom: 30rpx;
}

.title {
  font-size: 36rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  line-height: 1.4;
}

/* 问题状态 */
.status-container {
  display: flex;
  margin-bottom: 20rpx;
}

.status-badge {
  display: inline-block;
  padding: 6rpx 16rpx;
  border-radius: 30rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.status-open {
  color: #52c41a;
  background-color: #f6ffed;
  border: 1px solid #b7eb8f;
}

.status-closed {
  color: #ff4d4f;
  background-color: #fff2f0;
  border: 1px solid #ffccc7;
}

/* 标签容器 */
.tags-container {
  display: flex;
  flex-wrap: wrap;
  margin-bottom: 20rpx;
  gap: 16rpx;
}

.tag-item {
  padding: 6rpx 20rpx;
  background-color: #f0f7ff;
  border-radius: 30rpx;
  font-size: 24rpx;
  color: #3B7ADB;
}

/* 使用场景 */
.scene-container {
  margin-bottom: 20rpx;
}

.section-title {
  font-size: 28rpx;
  font-weight: 500;
  color: #666;
  margin-bottom: 10rpx;
}

.scene-content {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
}

/* 关键考量因素 */
.factors-container {
  margin-bottom: 20rpx;
}

.factor-content {
  font-size: 28rpx;
  color: #333;
  line-height: 1.5;
  word-break: break-all;
  white-space: pre-wrap;
}

.factors-list {
  display: flex;
  flex-wrap: wrap;
  gap: 16rpx;
}

.factor-item {
  padding: 8rpx 20rpx;
  background-color: #f5f5f5;
  border-radius: 8rpx;
  font-size: 26rpx;
  color: #666;
}

/* 预算区间 */
.budget-container {
  margin-bottom: 20rpx;
}

.budget-content {
  display: flex;
  align-items: center;
  font-size: 28rpx;
  color: #333;
}

.budget-separator {
  margin: 0 10rpx;
  color: #999;
}

.budget-currency {
  margin-left: 10rpx;
  color: #999;
}

/* 图片列表 */
.image-list {
  display: flex;
  flex-wrap: wrap;
  margin-top: 20rpx;
}

.question-image {
  width: 220rpx;
  height: 220rpx;
  margin-right: 10rpx;
  margin-bottom: 10rpx;
  border-radius: 8rpx;
  object-fit: cover;
}

.question-image.single {
  width: 100%;
  height: auto;
  max-height: 400rpx;
}

/* 投票选项 */
.vote-section {
  margin-bottom: 30rpx;
}

/* 已关闭投票提示 */
.vote-closed-tip {
  display: flex;
  align-items: center;
  background-color: #fff2f0;
  border: 1px solid #ffccc7;
  color: #ff4d4f;
  padding: 16rpx 20rpx;
  border-radius: 8rpx;
  font-size: 26rpx;
  margin: 10rpx 0 20rpx;
}

.vote-closed-tip .iconfont {
  font-size: 28rpx;
  margin-right: 10rpx;
}

.options-list {
  margin-top: 20rpx;
}

.option-item {
  background-color: #f8f8f8;
  border-radius: 8rpx;
  padding: 24rpx;
  margin-bottom: 20rpx;
  position: relative;
  overflow: hidden;
}

.option-item.selected {
  background-color: #f0f7ff;
  border: 1rpx solid #3B7ADB;
}

.option-item.voted {
  background-color: #f0f7ff;
  border: 1rpx solid #3B7ADB;
}

.option-content {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;
  position: relative;
  z-index: 2;
}

.option-radio {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  border: 2rpx solid #ccc;
  margin-right: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.option-radio.checked {
  border-color: #3B7ADB;
  background-color: #3B7ADB;
}

.option-radio.checked::after {
  content: '';
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background-color: #fff;
}

.option-radio.voted {
  border-color: #3B7ADB;
  background-color: #3B7ADB;
}

.option-radio.voted::after {
  content: '';
  width: 16rpx;
  height: 16rpx;
  border-radius: 50%;
  background-color: #fff;
}

.option-text {
  font-size: 28rpx;
  color: #333;
  flex: 1;
}

.vote-stats {
  position: relative;
  z-index: 2;
}

.progress-bar {
  width: 100%;
  height: 10rpx;
  background-color: #e9e9e9;
  border-radius: 5rpx;
  overflow: hidden;
  margin-bottom: 10rpx;
}

.progress {
  height: 100%;
  background-color: #3B7ADB;
  border-radius: 5rpx;
}

.vote-count {
  font-size: 24rpx;
  color: #666;
  text-align: right;
  display: block;
}

/* 问题数据 */
.question-stats {
  display: flex;
  align-items: center;
  gap: 32rpx;
  padding: 32rpx;
  background: white;
  border-radius: 20rpx;
  margin-top: 20rpx;
  flex-wrap: wrap;
}

.question-stats .stat-item {
  display: flex;
  align-items: center;
  gap: 8rpx;
  font-size: 28rpx;
  color: #666;
}

.question-stats .stat-item .iconfont {
  font-size: 32rpx;
  color: #999;
}

/* 产品对比按钮 */
.compare-btn {
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  color: white;
  border: none;
  border-radius: 50rpx;
  padding: 16rpx 24rpx;
  font-size: 24rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
  min-width: 200rpx;
  height: 64rpx;
  line-height: 1;
  margin: 0;
  box-shadow: 0 4rpx 12rpx rgba(102, 126, 234, 0.3);
  transition: all 0.3s ease;
}

.compare-btn:hover {
  transform: translateY(-2rpx);
  box-shadow: 0 6rpx 16rpx rgba(102, 126, 234, 0.4);
}

.compare-btn[disabled] {
  background: linear-gradient(135deg, #ccc 0%, #999 100%);
  color: #666;
  box-shadow: none;
  transform: none;
}

.compare-btn .iconfont {
  font-size: 28rpx;
}

.compare-btn::after {
  border: none;
}

.share-btn {
  background: #f8f9fa;
  color: #495057;
  border: 2rpx solid #e9ecef;
  border-radius: 50rpx;
  padding: 16rpx 24rpx;
  font-size: 24rpx;
  display: flex;
  align-items: center;
  gap: 8rpx;
  height: 64rpx;
  line-height: 1;
  margin: 0;
}

.share-btn .iconfont {
  font-size: 28rpx;
  color: #6c757d;
}

/* 评论部分 */
.comment-section {
  background-color: #fff;
  padding: 30rpx;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.05);
}

.section-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 20rpx;
  padding-bottom: 10rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.comment-list {
  margin-bottom: 20rpx;
}

.comment-item {
  padding: 20rpx 0;
  border-bottom: 1rpx solid #f0f0f0;
}

.comment-user {
  display: flex;
  align-items: center;
  margin-bottom: 16rpx;

}

.comment-option {
  font-size: 28rpx;
  color: #3B7ADB;
  font-weight: 500;
  margin-bottom: 10rpx;
}

.comment-content {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  margin-bottom: 16rpx;
}

.comment-actions {
  display: flex;
  align-items: center;
}

.action-item {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #999;
  margin-right: 30rpx;
}

.action-item.liked {
  color: #3B7ADB;
}

/* 空状态 */
.empty-comment {
  padding: 60rpx 0;
  text-align: center;
  color: #999;
  font-size: 28rpx;
}

/* 理由输入模态框 */
.reason-modal {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
}

.reason-modal-mask {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.reason-modal-content {
  position: relative;
  width: 650rpx;
  background-color: #fff;
  border-radius: 16rpx;
  overflow: hidden;
  z-index: 1001;
}

.reason-modal-header {
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #f0f0f0;
}

.reason-modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.reason-modal-close {
  font-size: 40rpx;
  color: #999;
}

.reason-modal-body {
  padding: 30rpx;
}

.reason-textarea {
  width: 100%;
  height: 200rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  margin-bottom: 20rpx;
  box-sizing: border-box;
}

.anonymous-option {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
}

.checkbox {
  width: 40rpx;
  height: 40rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  margin-right: 16rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

.checkbox.checked {
  border-color: #3B7ADB;
  background-color: #3B7ADB;
}

.checkbox-inner {
  width: 20rpx;
  height: 20rpx;
  background-color: #fff;
  border-radius: 4rpx;
}

.anonymous-text {
  font-size: 28rpx;
  color: #666;
}

.button-group {
  display: flex;
  justify-content: flex-end;
}

.cancel-btn, .submit-btn {
  width: 160rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  border-radius: 8rpx;
  font-size: 28rpx;
}

.cancel-btn {
  color: #666;
  background-color: #f5f5f5;
  margin-right: 20rpx;
}

.submit-btn {
  color: #fff;
  background-color: #3B7ADB;
}

/* 评论模态框 */
.comment-modal {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 1000;
  display: flex;
  justify-content: center;
  align-items: center;
}

.comment-modal-mask {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.comment-modal-content {
  position: relative;
  width: 100%;
  height: 85%;
  background-color: #fff;
  border-radius: 30rpx 30rpx 0 0;
  overflow: hidden;
  z-index: 1001;
  position: absolute;
  bottom: 0;
  display: flex;
  flex-direction: column;
}

.comment-modal-header {
  padding: 30rpx;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1rpx solid #f0f0f0;
}

.comment-modal-title {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

.comment-modal-close {
  font-size: 40rpx;
  color: #999;
}

.comment-modal-body {
  flex: 1;
  padding: 0 30rpx;
  overflow-y: auto;
}

/* 模态框中的评论样式 */
.modal-comment-list {
  padding: 20rpx 0;
}

.modal-comment-item {
  margin-bottom: 30rpx;
  padding-bottom: 20rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.modal-comment-user {
  display: flex;
  align-items: center;
  margin-bottom: 10rpx;
}

.avatar.small {
  width: 60rpx;
  height: 60rpx;
}

.delete-btn {
  margin-left: auto;
  font-size: 28rpx;
  color: #999;
}

.modal-comment-content {
  font-size: 28rpx;
  color: #333;
  line-height: 1.6;
  padding: 0 0 10rpx 76rpx;
}

.modal-comment-actions {
  padding: 0 0 10rpx 76rpx;
  display: flex;
}

.modal-action-item {
  display: flex;
  align-items: center;
  font-size: 24rpx;
  color: #999;
  margin-right: 30rpx;
}

/* 回复列表 */
.modal-comment-replies {
  margin-left: 76rpx;
  background-color: #f9f9f9;
  border-radius: 8rpx;
  padding: 16rpx;
  margin-bottom: 10rpx;
}

.modal-reply-item {
  margin-bottom: 20rpx;
}

.modal-reply-item:last-child {
  margin-bottom: 0;
}

.modal-reply-user {
  display: flex;
  align-items: center;
  margin-bottom: 8rpx;
}

.modal-reply-content {
  font-size: 26rpx;
  color: #333;
  line-height: 1.5;
  padding-left: 76rpx;
  margin-bottom: 8rpx;
}

.reply-target {
  color: #3B7ADB;
}

.modal-reply-actions {
  padding-left: 76rpx;
}

/* 空评论状态 */
.empty-modal-comments {
  padding: 60rpx 0;
  text-align: center;
  color: #999;
  font-size: 28rpx;
}

/* 评论输入区 */
.comment-modal-footer {
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #f0f0f0;
}

.reply-tip {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10rpx 0;
  font-size: 26rpx;
  color: #3B7ADB;
  margin-bottom: 10rpx;
}

.cancel-reply {
  font-size: 32rpx;
  color: #999;
}

.comment-input-container {
  display: flex;
  align-items: center;
}

.comment-input {
  flex: 1;
  height: 80rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 40rpx;
  padding: 0 30rpx;
  font-size: 28rpx;
  background-color: #f5f5f5;
}

.send-btn {
  width: 120rpx;
  height: 80rpx;
  line-height: 80rpx;
  text-align: center;
  font-size: 28rpx;
  color: #3B7ADB;
}

/* u7406u7531u63d0u793au6837u5f0f */
.reason-tip {
  background-color: #fff8e6;
  padding: 14rpx 20rpx;
  border-radius: 8rpx;
  margin-bottom: 20rpx;
  border-left: 6rpx solid #ffb800;
}

.tip-text {
  font-size: 24rpx;
  color: #eb9e05;
  line-height: 1.5;
}

/* 回复数量和展开/收起按钮 */
.reply-count-toggle {
  margin: 8rpx 0 8rpx 80rpx;
  padding: 8rpx 0;
  font-size: 24rpx;
  color: #1989fa;
  display: flex;
  align-items: center;
}

.toggle-icon {
  margin-left: 8rpx;
  font-size: 22rpx;
}

.toggle-icon.up {
  transform: rotate(0deg);
}

.toggle-icon.down {
  transform: rotate(0deg);
}

/* 回复列表样式 */
.modal-comment-replies {
  margin-left: 80rpx;
  background-color: #f7f7f7;
  border-radius: 10rpx;
  padding: 12rpx;
}

.modal-reply-item {
  margin-bottom: 16rpx;
  border-bottom: 1px solid #eee;
  padding-bottom: 12rpx;
}

.modal-reply-item:last-child {
  margin-bottom: 0;
  border-bottom: none;
}

.modal-reply-user {
  display: flex;
  align-items: flex-start;
}

.avatar.mini {
  width: 40rpx;
  height: 40rpx;
  border-radius: 50%;
  margin-right: 12rpx;
  margin-top: 6rpx;
}

.reply-content {
  flex: 1;
}

.reply-user-info {
  display: flex;
  align-items: center;
  font-size: 24rpx;
}

.reply-to {
  margin: 0 6rpx;
  color: #999;
}

.target-nickname {
  color: #1989fa;
}

.reply-text {
  font-size: 26rpx;
  line-height: 1.5;
  margin: 8rpx 0;
  word-wrap: break-word;
}

.reply-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 22rpx;
}

.reply-footer .time {
  color: #999;
}

.reply-action {
  display: flex;
  align-items: center;
}

.reply-action .modal-action-item {
  margin-right: 16rpx;
  color: #666;
}

.load-more-replies {
  text-align: center;
  font-size: 24rpx;
  color: #1989fa;
  padding: 12rpx 0;
}

.loading-container.mini {
  padding: 10rpx 0;
}

.loading-container.mini .loading-spinner {
  width: 30rpx;
  height: 30rpx;
}

/* 高亮样式，用于通知跳转后高亮显示目标元素 */
.highlight-item {
  animation: highlight-fade 3s ease-in-out;
}

@keyframes highlight-fade {
  0% {
    background-color: rgba(255, 230, 0, 0.3);
  }
  70% {
    background-color: rgba(255, 230, 0, 0.3);
  }
  100% {
    background-color: transparent;
  }
}

/* 高亮回答样式 */
.comment-item.highlighted {
  background-color: rgba(250, 219, 20, 0.1);
  border-left: 3px solid #fadb14;
  animation: highlight-pulse 3s ease-in-out;
}

@keyframes highlight-pulse {
  0% {
    background-color: rgba(250, 219, 20, 0.3);
  }
  50% {
    background-color: rgba(250, 219, 20, 0.1);
  }
  100% {
    background-color: rgba(250, 219, 20, 0.3);
  }
}

/* 模态框评论高亮样式 */
.modal-comment-item.highlight-item {
  background-color: rgba(255, 230, 0, 0.2);
  border-left: 3px solid #ffb800;
  border-radius: 4px;
  position: relative;
  z-index: 10;
}

/* 模态框回复高亮样式 */
.modal-reply-item.highlight-item {
  background-color: rgba(255, 230, 0, 0.2);
  border-left: 3px solid #ffb800;
  border-radius: 4px;
  padding-left: 6px;
  position: relative;
  z-index: 10;
}

/* 删除的评论样式 */
.deleted-reply {
  opacity: 0.7;
}

.deleted-comment-text {
  font-size: 26rpx;
  color: #999;
  padding: 16rpx 0;
  display: flex;
  align-items: center;
}

.deleted-comment-text text {
  color: #999;
}

/* 调整删除评论的间距 */
.modal-reply-item.deleted-reply {
  padding: 12rpx 0;
  margin: 8rpx 0;
  background-color: #f8f8f8;
  border-radius: 8rpx;
}

.modal-reply-item.deleted-reply .reply-content {
  padding-left: 20rpx;
} 