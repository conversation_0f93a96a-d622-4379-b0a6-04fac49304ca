const mongoose = require('mongoose');

const ProductSchema = new mongoose.Schema(
  {
    // 商品基本信息
    skuId: {
      type: String,
      required: true,
      unique: true,
      index: true
    },
    skuName: {
      type: String,
      required: true,
      trim: true
    },
    
    // 产品类型 (用于参数对比)
    productType: {
      type: String,
      enum: ['phone', 'laptop', 'tablet', 'headphones', 'smartwatch', 'other'],
      required: true,
      index: true
    },
    
    // 🆕 多配置支持 - 用于存储不同内存/存储组合的价格
    configurations: [{
      name: { type: String, required: true }, // 配置名称，如 "8+256GB", "8+512GB"
      storage: { type: String, required: true }, // 存储容量
      ram: { type: String }, // 内存大小（对于手机、电脑等）
      price: { type: Number, required: true }, // 该配置的价格
      available: { type: Boolean, default: true }, // 是否有货
      _id: false // 不生成子文档ID
    }],
    
    // 🆕 默认配置标识 - 用于对比时显示的默认配置
    defaultConfiguration: {
      type: String, // 指向configurations中的name字段
      default: function() {
        return this.configurations && this.configurations.length > 0 ? this.configurations[0].name : '';
      }
    },
    
    // 产品参数规格 (用于对比功能)
    productSpecs: {
      // 通用参数
      general: {
        brand: { type: String },
        model: { type: String },
        color: { type: String },
        weight: { type: String },
        dimensions: { type: String }
      },
      
      // 手机参数
      phone: {
        screenSize: { type: String },          // 屏幕尺寸
        screenResolution: { type: String },    // 分辨率
        processor: { type: String },           // 处理器
        // 🔄 移除固定的ram和storage，改用configurations中的数据
        battery: { type: String },             // 电池容量
        camera: {
          rear: { type: String },              // 后置摄像头
          front: { type: String }              // 前置摄像头
        },
        operatingSystem: { type: String },     // 操作系统
        network: { type: String }              // 网络制式
      },
      
      // 笔记本电脑参数
      laptop: {
        screenSize: { type: String },          // 屏幕尺寸
        screenResolution: { type: String },    // 分辨率
        processor: { type: String },           // 处理器
        // 🔄 移除固定的ram和storage，改用configurations中的数据
        graphics: { type: String },            // 显卡
        operatingSystem: { type: String },     // 操作系统
        ports: { type: String },               // 接口
        battery: { type: String }              // 电池续航
      },
      
      // 可扩展其他产品类型参数
      tablet: {
        screenSize: { type: String },
        screenResolution: { type: String },
        processor: { type: String },
        // 🔄 移除固定的ram和storage，改用configurations中的数据
        battery: { type: String },
        operatingSystem: { type: String }
      }
    },
    
    // 🔄 价格信息改为兼容旧数据的方式
    price: {
      type: Number,
      required: false // 改为非必需，向后兼容
    },
    
    // 🆕 价格范围 - 自动从configurations计算
    priceRange: {
      min: { type: Number },
      max: { type: Number }
    },
    
    // 商品图片
    imageUrl: {
      type: String,
      required: true
    },
    
    // 品牌信息
    brandName: {
      type: String,
      default: ''
    },
    
    // 是否支持参数对比
    supportsComparison: {
      type: Boolean,
      default: true
    },
    
    // 缓存时间戳
    lastUpdated: {
      type: Date,
      default: Date.now,
      // expires: 36000 // 10 小时后自动删除缓存
    },
    
    // 🆕 智能搜索匹配字段
    searchMatch: {
      type: String,
      index: true // 创建索引以提高搜索性能
    }
  },
  {
    timestamps: true
  }
);

// 🆕 中间件：保存前自动处理配置和价格
ProductSchema.pre('save', function() {
  // 🆕 生成智能搜索匹配字段
  this.generateSearchMatch();
  
  // 自动计算价格范围
  if (this.configurations && this.configurations.length > 0) {
    const prices = this.configurations.map(config => config.price);
    this.priceRange = {
      min: Math.min(...prices),
      max: Math.max(...prices)
    };
    
    // 如果没有设置默认配置，自动设置为第一个
    if (!this.defaultConfiguration) {
      this.defaultConfiguration = this.configurations[0].name;
    }
    
    // 向后兼容：如果没有设置price字段，设置为最低价格
    if (!this.price) {
      this.price = this.priceRange.min;
    }
  } else if (this.price) {
    // 如果只有单一价格，创建默认配置
    if (!this.configurations || this.configurations.length === 0) {
      this.configurations = [{
        name: 'default',
        storage: this.productSpecs?.phone?.storage || this.productSpecs?.laptop?.storage || 'N/A',
        ram: this.productSpecs?.phone?.ram || this.productSpecs?.laptop?.ram || 'N/A',
        price: this.price,
        available: true
      }];
      this.defaultConfiguration = 'default';
    }
    
    // 设置价格范围
    this.priceRange = {
      min: this.price,
      max: this.price
    };
  }
});

// 🆕 虚拟字段：获取默认配置对象
ProductSchema.virtual('defaultConfig').get(function() {
  if (!this.configurations || this.configurations.length === 0) {
    return null;
  }
  
  return this.configurations.find(config => config.name === this.defaultConfiguration) 
    || this.configurations[0];
});

// 🆕 实例方法：获取指定配置
ProductSchema.methods.getConfiguration = function(configName) {
  if (!this.configurations || this.configurations.length === 0) {
    return null;
  }
  
  return configName 
    ? this.configurations.find(config => config.name === configName)
    : this.configurations.find(config => config.name === this.defaultConfiguration) || this.configurations[0];
};

// 🆕 实例方法：添加配置
ProductSchema.methods.addConfiguration = function(config) {
  if (!this.configurations) {
    this.configurations = [];
  }
  
  // 检查配置名称是否已存在
  const existingConfig = this.configurations.find(c => c.name === config.name);
  if (existingConfig) {
    throw new Error(`配置 "${config.name}" 已存在`);
  }
  
  this.configurations.push(config);
  
  // 如果是第一个配置，设为默认配置
  if (this.configurations.length === 1) {
    this.defaultConfiguration = config.name;
  }
  
  return this;
};

// 🆕 实例方法：生成搜索匹配字段
ProductSchema.methods.generateSearchMatch = function() {
  const skuName = this.skuName || '';
  const brand = this.productSpecs?.general?.brand || '';
  
  // 拼接skuName和productSpecs.general.brand，去除空格和标点符号，保留中文字符
  const combined = (brand + skuName)
    .replace(/[\s.,!?;:()\[\]{}'""`''""、。，！？；：（）【】《》\-_+=]/g, '') // 只移除空格和常见标点符号
    .toLowerCase(); // 转为小写便于搜索
  
  this.searchMatch = combined;
  return this;
};

// 索引设置
ProductSchema.index({ skuName: 'text', brandName: 'text' }, {
  weights: {
    skuName: 10,
    brandName: 5
  },
  name: 'product_text_search'
}); // 全文搜索索引

// 产品对比和搜索优化索引
ProductSchema.index({ supportsComparison: 1, productType: 1, skuName: 1 }); // 搜索优化复合索引
ProductSchema.index({ supportsComparison: 1, brandName: 1 }); // 品牌搜索索引
ProductSchema.index({ productType: 1, supportsComparison: 1 }); // 类别筛选索引

// 🆕 价格范围索引
ProductSchema.index({ 'priceRange.min': 1, 'priceRange.max': 1 }); // 价格筛选索引

// 🆕 智能搜索匹配索引
ProductSchema.index({ searchMatch: 1 }); // 智能搜索匹配索引

// 移除重复的 createdAt 索引，timestamps: true 已自动创建
ProductSchema.index({ lastUpdated: -1 }); // 保留 lastUpdated 索引

module.exports = mongoose.model('Product', ProductSchema); 