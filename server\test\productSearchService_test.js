/**
 * ProductSearchService 测试脚本
 * 测试智能产品搜索功能
 * 功能: 测试searchProductNames函数的搜索能力
 * 测试关键词: 苹果16
 */

const mongoose = require('mongoose');
const fs = require('fs');
const path = require('path');
const { searchProducts } = require('../src/services/product/productSearchService');

// 数据库连接配置
const DB_URL = process.env.MONGODB_URI || 'mongodb://localhost:27017/xuanxuan_test';

/**
 * 连接数据库
 */
async function connectDB() {
  try {
    await mongoose.connect(DB_URL);
    console.log('✅ 数据库连接成功');
  } catch (error) {
    console.error('❌ 数据库连接失败:', error.message);
    process.exit(1);
  }
}

/**
 * 断开数据库连接
 */
async function disconnectDB() {
  try {
    await mongoose.disconnect();
    console.log('✅ 数据库连接已断开');
  } catch (error) {
    console.error('❌ 断开数据库连接失败:', error.message);
  }
}

/**
 * 测试产品搜索功能
 */
async function testProductSearch() {
  console.log('\n🔍 测试: 产品搜索功能 (关键词: 苹果16)');
  console.log('='.repeat(80));

  const keyword = '苹果16';
  const limit = 10;
  const category = '';

  console.log(`🔎 搜索关键词: "${keyword}"`);
  console.log(`📊 返回数量限制: ${limit}`);
  console.log(`📂 产品类别: ${category || '全部类别'}`);

  try {
    const startTime = Date.now();

    const result = await searchProducts(keyword, limit, category);

    const endTime = Date.now();
    const duration = endTime - startTime;

    console.log(`⏱️ 搜索耗时: ${duration}ms`);

    if (result.success) {
      console.log('\n✅ 产品搜索成功!');
      console.log(`📱 找到产品数量: ${result.data.products.length}`);
      console.log(`🎯 搜索策略: ${result.data.searchStrategy}`);
      console.log(`⏰ 服务端搜索时间: ${result.data.searchTime}ms`);
      
      // 显示搜索结果
      if (result.data.products.length > 0) {
        console.log('\n📋 搜索结果:');
        result.data.products.forEach((product, index) => {
          console.log(`   ${index + 1}. ${product.skuName}`);
          console.log(`      品牌: ${product.brandName}`);
          console.log(`      类型: ${product.productType}`);
          console.log(`      上市日期: ${product.上市日期}`);
          console.log(`      图片: ${product.imageUrl ? '有' : '无'}`);
          console.log('');
        });
      } else {
        console.log('\n📭 未找到匹配的产品');
      }

      // 保存搜索结果到文件
      try {
        const fileName = 'productSearch_result.json';
        const filePath = path.join(__dirname, fileName);

        fs.writeFileSync(filePath, JSON.stringify(result, null, 2), 'utf8');
        console.log(`\n📄 搜索结果已保存到文件: ${filePath}`);
        console.log(`📂 文件大小: ${fs.statSync(filePath).size} 字节`);

      } catch (saveError) {
        console.error(`❌ 保存文件失败: ${saveError.message}`);
      }

    } else {
      console.log(`❌ 产品搜索失败: ${result.message}`);
      if (result.error) {
        console.log(`🔍 错误详情: ${result.error}`);
      }
    }

  } catch (error) {
    console.log(`❌ 产品搜索失败: ${error.message}`);
    console.error('详细错误:', error.stack);
  }
}

/**
 * 主测试函数
 */
async function runSearchTest() {
  console.log('🚀 ProductSearchService 测试开始');
  console.log('='.repeat(80));
  console.log(`测试时间: ${new Date().toLocaleString()}`);
  console.log(`Node.js版本: ${process.version}`);
  console.log(`测试重点: 智能产品搜索功能`);

  try {
    // 连接数据库
    await connectDB();

    // 执行产品搜索测试
    await testProductSearch();

    console.log('\n✅ 测试完成!');
    console.log('\n📊 测试总结:');
    console.log('   - 搜索功能测试: 关键词"苹果16"搜索');
    console.log('   - 返回格式验证: 检查返回数据结构');
    console.log('   - 性能测试: 记录搜索耗时');
    console.log('   - 结果保存: 保存搜索结果到JSON文件');

  } catch (error) {
    console.error('\n❌ 测试过程中发生错误:', error.message);
    console.error('详细错误:', error.stack);
  } finally {
    // 断开数据库连接
    await disconnectDB();
  }
}

// 如果直接运行此文件，执行测试
if (require.main === module) {
  // 检查环境变量
  console.log('🔧 环境检查:');
  console.log(`   MongoDB URI: ${DB_URL}`);

  runSearchTest().catch(error => {
    console.error('测试运行失败:', error.message);
    process.exit(1);
  });
}

module.exports = {
  testProductSearch,
  runSearchTest
};