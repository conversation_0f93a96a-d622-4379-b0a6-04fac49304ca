const Question = require('../models/Question');
const Answer = require('../models/Answer');
const User = require('../models/User');
const mongoose = require('mongoose');

/**
 * 获取用户参与投票的问题列表
 * @param {String} userId 用户ID
 * @param {Number} page 页码
 * @param {Number} limit 每页数量
 * @param {String} sortBy 排序方式
 * @returns {Promise<Object>} 问题列表和分页信息
 */
const getUserVotedQuestions = async (userId, page = 1, limit = 10, sortBy = 'newest') => {
  // 验证ObjectId有效性
  if (!mongoose.Types.ObjectId.isValid(userId)) {
    throw new Error('无效的用户ID');
  }

  // 查找用户回答过的问题ID列表
  const answers = await Answer.find({ userId })
    .select('questionId')
    .lean();

  // 如果用户没有投票，直接返回空结果
  if (answers.length === 0) {
    return {
      questions: [],
      pagination: {
        total: 0,
        page,
        limit,
        pages: 0
      }
    };
  }

  // 提取问题ID
  const questionIds = answers.map(answer => answer.questionId);

  // 根据排序方式设置排序条件
  let sort = {};
  switch (sortBy) {
    case 'newest':
      sort = { createdAt: -1 };
      break;
    case 'hottest':
      sort = { totalVotes: -1, createdAt: -1 };
      break;
    default:
      sort = { createdAt: -1 };
  }

  // 查询总记录数
  const total = await Question.countDocuments({ _id: { $in: questionIds } });
  
  // 分页查询问题详情
  const questions = await Question.find({ _id: { $in: questionIds } })
    .sort(sort)
    .skip((page - 1) * limit)
    .limit(limit)
    .populate({
      path: 'userId',
      select: '_id nickname avatar',
      options: { lean: true }
    })
    .lean();

  // 为每个问题查找用户的投票选项
  const questionsWithVoteInfo = await Promise.all(
    questions.map(async (question) => {
      const answer = await Answer.findOne({
        questionId: question._id,
        userId
      }).select('optionId').lean();

      // 找到用户选择的选项
      const votedOption = question.options.find(
        option => option._id.toString() === (answer ? answer.optionId.toString() : null)
      );

      return {
        id: question._id,
        title: question.title,
        scene: question.scene,
        keyFactors: question.keyFactors,
        budget: question.budget,
        tags: question.tags,
        options: question.options.map(option => ({
          id: option._id,
          content: option.content,
          voteCount: option.voteCount
        })),
        totalVotes: question.totalVotes,
        commentCount: question.commentCount,
        createdAt: question.createdAt,
        expiryTime: question.expiryTime,
        status: question.status,
        user: question.isAnonymous ? 
          { id: null, nickname: '匿名用户', avatar: '/assets/images/default-avatar.png' } : 
          {
            id: question.userId._id,
            nickname: question.userId.nickname,
            avatar: question.userId.avatar
          },
        votedOption: votedOption ? {
          id: votedOption._id,
          content: votedOption.content
        } : null
      };
    })
  );

  // 计算总页数
  const pages = Math.ceil(total / limit);

  return {
    questions: questionsWithVoteInfo,
    pagination: {
      total,
      page,
      limit,
      pages
    }
  };
};

/**
 * 获取用户发起的问题列表
 * @param {String} userId 用户ID
 * @param {Number} page 页码
 * @param {Number} limit 每页数量
 * @param {String} sortBy 排序方式
 * @param {String} status 问题状态
 * @returns {Promise<Object>} 问题列表和分页信息
 */
const getUserCreatedQuestions = async (userId, page = 1, limit = 10, sortBy = 'newest', status = null) => {
  // 验证ObjectId有效性
  if (!mongoose.Types.ObjectId.isValid(userId)) {
    throw new Error('无效的用户ID');
  }

  // 构建查询条件
  const query = { userId };
  
  // 如果指定了状态，添加到查询条件中
  if (status) {
    query.status = status;
  }
  
  // 根据排序方式设置排序条件
  let sort = {};
  switch (sortBy) {
    case 'newest':
      sort = { createdAt: -1 };
      break;
    case 'hottest':
      sort = { totalVotes: -1, createdAt: -1 };
      break;
    case 'expiringSoon':
      sort = { expiryTime: 1, createdAt: -1 };
      break;
    default:
      sort = { createdAt: -1 };
  }

  // 查询总记录数
  const total = await Question.countDocuments(query);
  
  // 分页查询
  const questions = await Question.find(query)
    .sort(sort)
    .skip((page - 1) * limit)
    .limit(limit)
    .populate({
      path: 'userId',
      select: '_id nickname avatar',
      options: { lean: true }
    })
    .lean();

  // 格式化返回数据
  const formattedQuestions = await Promise.all(
    questions.map(async (question) => {
      // 检查当前用户是否已对该问题投票
      const answer = await Answer.findOne({
        questionId: question._id,
        userId
      }).select('optionId').lean();

      const hasVoted = !!answer;
      const votedOptionId = hasVoted ? answer.optionId : null;

      return {
        id: question._id,
        title: question.title,
        scene: question.scene,
        keyFactors: question.keyFactors,
        budget: question.budget,
        tags: question.tags,
        options: question.options.map(option => ({
          id: option._id,
          content: option.content,
          voteCount: option.voteCount,
          isVoted: votedOptionId ? option._id.toString() === votedOptionId.toString() : false
        })),
        totalVotes: question.totalVotes,
        commentCount: question.commentCount,
        createdAt: question.createdAt,
        expiryTime: question.expiryTime,
        status: question.status,
        requireReason: question.requireReason,
        user: {
          id: question.userId._id,
          nickname: question.userId.nickname,
          avatar: question.userId.avatar
        },
        isAnonymous: question.isAnonymous,
        hasVoted
      };
    })
  );

  // 计算总页数
  const pages = Math.ceil(total / limit);

  return {
    questions: formattedQuestions,
    pagination: {
      total,
      page,
      limit,
      pages
    }
  };
};

/**
 * 更新用户基本资料
 * @param {String} userId 用户ID
 * @param {Object} updateData 更新的数据
 * @returns {Promise<Object>} 更新后的用户信息
 */
const updateUserProfile = async (userId, updateData) => {
  // 验证ObjectId有效性
  if (!mongoose.Types.ObjectId.isValid(userId)) {
    throw new Error('无效的用户ID');
  }

  // 查找用户
  const user = await User.findById(userId);
  if (!user) {
    throw new Error('用户不存在');
  }

  // 检查用户是否处于活跃状态
  if (!user.isActive) {
    throw new Error('账户已被禁用');
  }

  // 过滤掉不允许修改的字段
  const allowedFields = ['nickname', 'avatar', 'gender', 'age', 'occupation', 'region'];
  const filteredData = {};
  
  for (const field of allowedFields) {
    if (updateData[field] !== undefined) {
      filteredData[field] = updateData[field];
    }
  }

  // 如果没有要更新的字段
  if (Object.keys(filteredData).length === 0) {
    throw new Error('没有提供要更新的字段');
  }

  // 如果要更新昵称，检查是否重复（如果系统需要唯一昵称的话）
  if (filteredData.nickname) {
    const existingUser = await User.findOne({
      nickname: filteredData.nickname,
      _id: { $ne: userId }
    });
    
    if (existingUser) {
      throw new Error('昵称已被使用');
    }
  }

  // 更新用户信息
  const updatedUser = await User.findByIdAndUpdate(
    userId,
    filteredData,
    { 
      new: true, 
      runValidators: true 
    }
  ).select('-refreshToken -openId -unionId');

  // 返回格式化的用户信息
  return {
    id: updatedUser._id,
    nickname: updatedUser.nickname,
    avatar: updatedUser.avatar,
    gender: updatedUser.gender,
    age: updatedUser.age,
    occupation: updatedUser.occupation,
    region: updatedUser.region,
    phone: updatedUser.phone,
    isActive: updatedUser.isActive,
    lastLoginAt: updatedUser.lastLoginAt,
    createdAt: updatedUser.createdAt,
    updatedAt: updatedUser.updatedAt
  };
};

/**
 * 检查手机号是否可用
 * @param {String} phone 手机号
 * @param {String} currentUserId 当前用户ID（排除自己）
 * @returns {Promise<Object>} 检查结果
 */
const checkPhoneAvailability = async (phone, currentUserId = null) => {
  // 构建查询条件
  const query = { phone };
  
  // 如果提供了当前用户ID，排除当前用户
  if (currentUserId && mongoose.Types.ObjectId.isValid(currentUserId)) {
    query._id = { $ne: currentUserId };
  }

  // 查找是否存在使用该手机号的其他用户
  const existingUser = await User.findOne(query);

  return {
    available: !existingUser,
    phone: existingUser ? null : phone
  };
};

/**
 * 修改用户手机号
 * @param {String} userId 用户ID
 * @param {String} newPhone 新手机号
 * @returns {Promise<Object>} 更新后的用户信息
 */
const changeUserPhone = async (userId, newPhone) => {
  // 验证ObjectId有效性
  if (!mongoose.Types.ObjectId.isValid(userId)) {
    throw new Error('无效的用户ID');
  }

  // 查找用户
  const user = await User.findById(userId);
  if (!user) {
    throw new Error('用户不存在');
  }

  // 检查用户是否处于活跃状态
  if (!user.isActive) {
    throw new Error('账户已被禁用');
  }

  // 检查新手机号是否已被其他用户使用
  const phoneCheck = await checkPhoneAvailability(newPhone, userId);
  if (!phoneCheck.available) {
    throw new Error('该手机号已被其他用户使用');
  }

  // 更新用户手机号
  const updatedUser = await User.findByIdAndUpdate(
    userId,
    { 
      phone: newPhone,
      updatedAt: new Date()
    },
    { 
      new: true, 
      runValidators: true 
    }
  ).select('-refreshToken -openId -unionId');

  // 返回格式化的用户信息
  return {
    id: updatedUser._id,
    nickname: updatedUser.nickname,
    avatar: updatedUser.avatar,
    gender: updatedUser.gender,
    age: updatedUser.age,
    occupation: updatedUser.occupation,
    region: updatedUser.region,
    phone: updatedUser.phone,
    isActive: updatedUser.isActive,
    lastLoginAt: updatedUser.lastLoginAt,
    createdAt: updatedUser.createdAt,
    updatedAt: updatedUser.updatedAt
  };
};

module.exports = {
  getUserVotedQuestions,
  getUserCreatedQuestions,
  updateUserProfile,
  checkPhoneAvailability,
  changeUserPhone
};
