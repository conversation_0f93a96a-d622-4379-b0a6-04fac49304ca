/* pages/login/login.wxss */

/* ==================== 登录页面特有样式 Login Page Specific Styles ==================== */

/* 页面背景渐变 */
.page {
  background: linear-gradient(135deg, #f8f9ff 0%, #ffffff 100%);
}

/* 登录表单容器 */
.login-form {
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
}

/* 确保表单组全宽显示 */
.login-form .form-group {
  width: 100%;
  align-self: stretch;
}

/* 登录页面表单输入框增强效果 */
.login-form .form-input-wrap {
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.06);
}

/* 登录页面验证码按钮在浅色背景下的优化 */
.login-form .code-btn.disabled {
  color: #666666;
  border-color: #bbbbbb;
  background-color: #e8e8e8;
}

/* 当只显示微信登录时的样式调整 */
.btn-wechat-only {
  margin-top: 80rpx !important;
  margin-bottom: 60rpx !important;
}

/* ==================== 响应式优化 Responsive Design ==================== */

/* 小屏幕适配 */
@media screen and (max-width: 375px) {
  .container {
    padding: 40rpx 20rpx 20rpx;
  }
  
  .logo-display .logo-image {
    width: 140rpx;
    height: 140rpx;
  }
  
  .logo-display .logo-title {
    font-size: 42rpx;
  }
}

/* 大屏幕优化 */
@media screen and (min-width: 768px) {
  .container {
    max-width: 600rpx;
    margin: 0 auto;
  }
} 