/**
 * 搜索工具类 - 电子消费产品智能搜索
 * 支持中文分词搜索，后期可扩展为AI智能搜索
 */

/**
 * 智能分词搜索 - 适用于中文电子消费产品场景
 * @param {String} keyword 搜索关键词
 * @returns {Object|null} 分词搜索查询条件
 */
const buildSmartSearchQuery = (keyword) => {
  if (!keyword || !keyword.trim()) {
    return null;
  }

  const trimmedKeyword = keyword.trim();
  
  // 电子消费产品常用词库
  const productKeywords = ['手机', '电脑', '笔记本', '平板', '耳机', '音响', '键盘', '鼠标', '显示器', '充电器'];
  const sceneKeywords = ['大学生', '学生', '上班族', '游戏', '办公', '学习', '娱乐', '商务', '家用', '便携'];
  const brandKeywords = ['苹果', '华为', '小米', '联想', '戴尔', 'iPad', 'iPhone', 'MacBook'];
  const featureKeywords = ['性价比', '续航', '轻薄', '高性能', '拍照', '音质', '散热', '内存', '存储', 'CPU'];
  
  const allKeywords = [...productKeywords, ...sceneKeywords, ...brandKeywords, ...featureKeywords];
  
  // 提取关键词
  const extractedKeywords = [];
  
  // 1. 提取已知关键词
  for (const word of allKeywords) {
    if (trimmedKeyword.includes(word)) {
      extractedKeywords.push(word);
    }
  }
  
  // 2. 如果没有提取到关键词，按字符分解
  if (extractedKeywords.length === 0) {
    // 按2-3字符分组，用于中文词汇
    for (let i = 0; i <= trimmedKeyword.length - 2; i++) {
      const twoChar = trimmedKeyword.substring(i, i + 2);
      if (twoChar.length === 2) {
        extractedKeywords.push(twoChar);
      }
    }
  }
  
  // 3. 同时保留原始关键词用于精确匹配
  if (!extractedKeywords.includes(trimmedKeyword)) {
    extractedKeywords.unshift(trimmedKeyword);
  }
  
  console.log(`智能分词结果: "${trimmedKeyword}" -> [${extractedKeywords.join(', ')}]`);
  
  // 构建查询条件
  const searchConditions = [];
  
  // 精确匹配（权重最高）
  searchConditions.push(
    { title: { $regex: trimmedKeyword, $options: 'i' } },
    { scene: { $regex: trimmedKeyword, $options: 'i' } },
    { keyFactors: { $regex: trimmedKeyword, $options: 'i' } }
  );
  
  // 分词匹配
  for (const word of extractedKeywords.slice(1)) { // 跳过第一个（原始关键词）
    searchConditions.push(
      { title: { $regex: word, $options: 'i' } },
      { scene: { $regex: word, $options: 'i' } },
      { keyFactors: { $regex: word, $options: 'i' } }
    );
  }
  
  // MongoDB文本搜索（如果支持）
  try {
    searchConditions.push({ $text: { $search: trimmedKeyword } });
  } catch (error) {
    // 文本搜索不可用时忽略
  }
  
  return { $or: searchConditions };
};

/**
 * 构建通用搜索查询条件
 * @param {String} keyword 搜索关键词
 * @param {Array} searchFields 要搜索的字段列表
 * @returns {Object|null} 搜索查询条件
 */
const buildGeneralSearchQuery = (keyword, searchFields = ['title', 'content']) => {
  if (!keyword || !keyword.trim()) {
    return null;
  }

  const trimmedKeyword = keyword.trim();
  const searchConditions = [];
  
  // 为每个字段创建搜索条件
  searchFields.forEach(field => {
    searchConditions.push({
      [field]: { $regex: trimmedKeyword, $options: 'i' }
    });
  });
  
  return { $or: searchConditions };
};

/**
 * 未来AI搜索接口预留
 * @param {String} keyword 搜索关键词
 * @param {Object} context 搜索上下文
 * @returns {Promise<Object>} AI搜索结果
 */
const buildAISearchQuery = async (keyword, context = {}) => {
  // TODO: 后期集成AI搜索服务
  // 可以集成如：百度智能搜索、阿里云搜索、自建AI模型等
  console.log('AI搜索功能待开发...', { keyword, context });
  
  // 当前暂时回退到智能分词搜索
  return buildSmartSearchQuery(keyword);
};

/**
 * 搜索配置管理
 */
const SearchConfig = {
  // 搜索类型
  SEARCH_TYPE: {
    SMART: 'smart',      // 智能分词搜索
    GENERAL: 'general',  // 通用搜索
    AI: 'ai'            // AI搜索（未来）
  },
  
  // 默认搜索字段
  DEFAULT_SEARCH_FIELDS: ['title', 'scene', 'keyFactors'],
  
  // 搜索结果限制
  MAX_RESULTS: 100,
  DEFAULT_PAGE_SIZE: 10
};

module.exports = {
  buildSmartSearchQuery,
  buildGeneralSearchQuery,
  buildAISearchQuery,
  SearchConfig
}; 