const mongoose = require('mongoose');
const ProductDataImporter = require('../utils/productDataImporter');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

/**
 * 动态产品数据导入脚本
 * 自动扫描和导入phones_new和laptops_new文件夹中的所有产品数据
 */

/**
 * 递归扫描目录，查找所有JS数据文件
 * @param {string} dirPath 目录路径
 * @param {Array} fileList 文件列表
 * @returns {Array} JS文件路径数组
 */
function getAllJsFiles(dirPath, fileList = []) {
  try {
    const files = fs.readdirSync(dirPath);
    
    files.forEach(file => {
      const filePath = path.join(dirPath, file);
      const stat = fs.statSync(filePath);
      
      if (stat.isDirectory()) {
        // 递归扫描子目录
        getAllJsFiles(filePath, fileList);
      } else if (file.endsWith('.js') && !file.startsWith('.')) {
        // 添加JS文件到列表
        fileList.push(filePath);
      }
    });
    
    return fileList;
  } catch (error) {
    console.warn(`⚠️ 扫描目录 ${dirPath} 时出错:`, error.message);
    return fileList;
  }
}

/**
 * 动态加载数据文件
 * @param {string} filePath 文件路径
 * @returns {Array} 产品数据数组
 */
function loadDataFile(filePath) {
  try {
    // 清除require缓存，确保获取最新数据
    delete require.cache[require.resolve(filePath)];
    
    const data = require(filePath);
    
    // 处理不同的导出格式
    if (Array.isArray(data)) {
      return data;
    } else if (data.default && Array.isArray(data.default)) {
      return data.default;
    } else if (typeof data === 'object') {
      // 查找数组类型的属性
      const keys = Object.keys(data);
      for (const key of keys) {
        if (Array.isArray(data[key])) {
          return data[key];
        }
      }
    }
    
    console.warn(`⚠️ 文件 ${filePath} 没有找到有效的产品数据数组`);
    return [];
  } catch (error) {
    console.error(`❌ 加载文件 ${filePath} 失败:`, error.message);
    return [];
  }
}

/**
 * 扫描并加载手机数据
 * @returns {Array} 所有手机数据
 */
function loadPhonesData() {
  const phonesDir = path.join(__dirname, '../data/phones_new');
  const allPhones = [];
  
  console.log('📱 开始扫描手机数据...');
  
  if (!fs.existsSync(phonesDir)) {
    console.warn(`⚠️ 手机数据目录不存在: ${phonesDir}`);
    return allPhones;
  }
  
  try {
    const brandDirs = fs.readdirSync(phonesDir);
    
    for (const brandDir of brandDirs) {
      const brandPath = path.join(phonesDir, brandDir);
      const stat = fs.statSync(brandPath);
      
      if (stat.isDirectory()) {
        console.log(`🔍 扫描品牌: ${brandDir}`);
        
        const jsFiles = getAllJsFiles(brandPath);
        
        for (const jsFile of jsFiles) {
          const fileName = path.basename(jsFile);
          console.log(`  📄 加载文件: ${fileName}`);
          
          const phoneData = loadDataFile(jsFile);
          
          if (phoneData.length > 0) {
            // 验证并处理数据
            const validPhones = phoneData.filter(phone => {
              if (!phone.skuId || !phone.skuName || !phone.productType) {
                console.warn(`⚠️ 数据不完整，跳过: ${phone.skuName || '未知产品'}`);
                return false;
              }
              
              // 确保productType为phone
              phone.productType = 'phone';
              
              return true;
            });
            
            allPhones.push(...validPhones);
            console.log(`  ✅ 成功加载 ${validPhones.length} 个手机产品`);
          } else {
            console.warn(`  ⚠️ 文件 ${fileName} 没有有效数据`);
          }
        }
      }
    }
    
    console.log(`📱 手机数据扫描完成，共加载 ${allPhones.length} 个产品`);
    return allPhones;
    
  } catch (error) {
    console.error('❌ 扫描手机数据时发生错误:', error.message);
    return allPhones;
  }
}

/**
 * 扫描并加载笔记本数据
 * @returns {Array} 所有笔记本数据
 */
function loadLaptopsData() {
  const laptopsNewDir = path.join(__dirname, '../data/laptops_new');
  const allLaptops = [];
  
  console.log('💻 开始扫描笔记本数据...');
  
  if (!fs.existsSync(laptopsNewDir)) {
    console.warn(`⚠️ 笔记本数据目录不存在: ${laptopsNewDir}`);
    return allLaptops;
  }
  
  try {
    const brandDirs = fs.readdirSync(laptopsNewDir);
    
    if (brandDirs.length === 0) {
      console.log('📄 laptops_new 文件夹为空，跳过笔记本数据加载');
      return allLaptops;
    }
    
    for (const brandDir of brandDirs) {
      const brandPath = path.join(laptopsNewDir, brandDir);
      const stat = fs.statSync(brandPath);
      
      if (stat.isDirectory()) {
        console.log(`🔍 扫描品牌: ${brandDir}`);
        
        const jsFiles = getAllJsFiles(brandPath);
        
        for (const jsFile of jsFiles) {
          const fileName = path.basename(jsFile);
          console.log(`  📄 加载文件: ${fileName}`);
          
          const laptopData = loadDataFile(jsFile);
          
          if (laptopData.length > 0) {
            // 验证并处理数据
            const validLaptops = laptopData.filter(laptop => {
              if (!laptop.skuId || !laptop.skuName || !laptop.productType) {
                console.warn(`⚠️ 数据不完整，跳过: ${laptop.skuName || '未知产品'}`);
                return false;
              }
              
              // 确保productType为laptop
              laptop.productType = 'laptop';
              
              return true;
            });
            
            allLaptops.push(...validLaptops);
            console.log(`  ✅ 成功加载 ${validLaptops.length} 个笔记本产品`);
          } else {
            console.warn(`  ⚠️ 文件 ${fileName} 没有有效数据`);
          }
        }
      }
    }
    
    console.log(`💻 笔记本数据扫描完成，共加载 ${allLaptops.length} 个产品`);
    return allLaptops;
    
  } catch (error) {
    console.error('❌ 扫描笔记本数据时发生错误:', error.message);
    return allLaptops;
  }
}

/**
 * 执行动态数据导入
 */
async function importSampleData() {
  try {
    // 连接数据库
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/xuanxuan', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('📦 数据库连接成功');

    // 加载所有产品数据
    console.log('\n🔄 开始加载产品数据...');
    const samplePhones = loadPhonesData();
    const sampleLaptops = loadLaptopsData();
    
    console.log(`\n📊 数据加载统计:`);
    console.log(`  📱 手机产品: ${samplePhones.length} 个`);
    console.log(`  💻 笔记本产品: ${sampleLaptops.length} 个`);
    console.log(`  📦 总计: ${samplePhones.length + sampleLaptops.length} 个产品`);
    
    if (samplePhones.length === 0 && sampleLaptops.length === 0) {
      console.warn('⚠️ 没有找到任何产品数据，请检查数据文件');
      return;
    }

    // 创建导入器实例
    const importer = new ProductDataImporter();

    // 检查命令行参数
    const args = process.argv.slice(2);
    const forceImport = args.includes('--force');
    const skipDuplicates = !args.includes('--no-skip');
    
    console.log(`\n🔧 导入配置:`);
    console.log(`  导入模式: ${forceImport ? '强制覆盖' : 'Upsert模式'}`);
    console.log(`  跳过重复: ${skipDuplicates ? '是' : '否'}`);

    // 配置导入选项
    const importOptions = {
      upsert: true,              // 启用upsert模式
      skipDuplicates: skipDuplicates, // 跳过重复数据
      batchSize: 10              // 批处理大小
    };

    // 批量导入数据
    console.log('\n🚀 开始批量导入数据...');
    const results = await importer.batchImport({
      phones: samplePhones,
      laptops: sampleLaptops
    }, importOptions);

    // 输出最终统计
    console.log('\n🎉 导入完成!');
    console.log(`📊 最终统计:`);
    console.log(`  ✅ 成功: ${results.success} 个`);
    console.log(`  ❌ 失败: ${results.failed} 个`);
    console.log(`  📈 成功率: ${results.success + results.failed > 0 ? 
      Math.round((results.success / (results.success + results.failed)) * 100) : 0}%`);
    
    if (results.failed > 0) {
      console.log('\n⚠️ 建议执行数据库检查: npm run db:check');
    }
    
    console.log('\n✨ 所有产品的searchMatch字段已自动生成!');
    
  } catch (error) {
    console.error('❌ 导入过程中发生错误:', error.message);
    console.error(error.stack);
    console.error('\n🔧 故障排除建议:');
    console.error('1. 检查数据库连接: npm run db:check');
    console.error('2. 重置数据库: npm run db:reset');
    console.error('3. 强制重新导入: npm run data:import --force');
  } finally {
    // 关闭数据库连接
    await mongoose.connection.close();
    console.log('🔌 数据库连接已关闭');
  }
}

/**
 * 清空现有产品数据（仅用于测试）
 */
async function clearExistingData() {
  try {
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/xuanxuan', {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });

    const Product = require('../models/Product');
    const result = await Product.deleteMany({});
    console.log(`🗑️ 已清空 ${result.deletedCount} 个现有产品数据`);
    
  } catch (error) {
    console.error('❌ 清空数据时发生错误:', error.message);
  } finally {
    await mongoose.connection.close();
  }
}

/**
 * 显示数据统计信息
 */
async function showDataStats() {
  try {
    console.log('📊 数据文件统计:');
    
    // 统计手机数据
    const samplePhones = loadPhonesData();
    console.log(`📱 手机数据: ${samplePhones.length} 个产品`);
    
    // 按品牌分组统计
    const phonesByBrand = samplePhones.reduce((acc, phone) => {
      const brand = phone.brandName || phone.productSpecs?.general?.brand || '未知品牌';
      acc[brand] = (acc[brand] || 0) + 1;
      return acc;
    }, {});
    
    console.log('  品牌分布:');
    Object.entries(phonesByBrand).forEach(([brand, count]) => {
      console.log(`    ${brand}: ${count} 个`);
    });
    
    // 统计笔记本数据
    const sampleLaptops = loadLaptopsData();
    console.log(`💻 笔记本数据: ${sampleLaptops.length} 个产品`);
    
    console.log(`📦 总计: ${samplePhones.length + sampleLaptops.length} 个产品`);
    
  } catch (error) {
    console.error('❌ 统计数据时发生错误:', error.message);
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  const args = process.argv.slice(2);
  
  if (args.includes('--clear')) {
    clearExistingData();
  } else if (args.includes('--stats')) {
    showDataStats();
  } else {
    importSampleData();
  }
}

module.exports = {
  importSampleData,
  clearExistingData,
  showDataStats,
  loadPhonesData,
  loadLaptopsData,
  getAllJsFiles,
  loadDataFile
}; 