/* pages/user/feedback/detail/detail.wxss */
.page {
  background-color: #f5f5f5;
  min-height: 100vh;
}

.container {
  padding: 20rpx;
}

/* 通用卡片样式 */
.card {
  background-color: #fff;
  border-radius: 16rpx;
  padding: 30rpx;
  margin-bottom: 20rpx;
  box-shadow: 0 2rpx 16rpx rgba(0, 0, 0, 0.05);
}

/* 加载和错误状态 */
.loading-state, .error-state {
  text-align: center;
  padding: 100rpx 40rpx;
}

.loading-text, .error-text {
  font-size: 32rpx;
  color: #999;
  margin-bottom: 40rpx;
  display: block;
}

.retry-btn {
  background-color: #3B7ADB;
  color: #fff;
  border-radius: 44rpx;
  height: 88rpx;
  line-height: 88rpx;
  font-size: 32rpx;
  font-weight: 600;
  width: 300rpx;
  margin: 0 auto;
  border: none;
}

/* 反馈头部 */
.detail-header {
  margin-bottom: 20rpx;
}

.header-top {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
}

/* 反馈类型徽章 */
.type-badge {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #fff;
  font-weight: 500;
}

.type-bug {
  background-color: #ff6b6b;
}

.type-suggestion {
  background-color: #4ecdc4;
}

.type-question {
  background-color: #45b7d1;
}

.type-other {
  background-color: #96ceb4;
}

/* 状态徽章 */
.status-badge {
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
  font-size: 24rpx;
  font-weight: 500;
}

.status-pending {
  background-color: #ffeaa7;
  color: #d63031;
}

.status-processing {
  background-color: #81ecec;
  color: #00b894;
}

.status-resolved {
  background-color: #55a3ff;
  color: #fff;
}

.status-rejected {
  background-color: #fd79a8;
  color: #fff;
}

.feedback-info {
  display: flex;
  flex-direction: column;
  gap: 8rpx;
}

.feedback-id {
  font-size: 26rpx;
  color: #666;
}

.create-time {
  font-size: 26rpx;
  color: #999;
}

/* 章节标题 */
.section-title {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20rpx;
  padding-bottom: 16rpx;
  border-bottom: 2rpx solid #f0f0f0;
}

.title-text {
  font-size: 32rpx;
  font-weight: 600;
  color: #333;
}

/* 复制按钮 */
.copy-btn {
  display: flex;
  align-items: center;
  gap: 8rpx;
  padding: 8rpx 16rpx;
  background-color: #f8f9fa;
  border-radius: 20rpx;
  font-size: 24rpx;
  color: #666;
}

.copy-btn:active {
  background-color: #e9ecef;
}

.copy-icon {
  font-size: 24rpx;
}

.copy-text {
  font-size: 24rpx;
}

/* 内容文本 */
.content-text {
  font-size: 30rpx;
  line-height: 1.8;
  color: #333;
  word-break: break-all;
}

/* 图片相关 */
.image-count {
  font-size: 24rpx;
  color: #999;
  background-color: #f8f9fa;
  padding: 4rpx 12rpx;
  border-radius: 12rpx;
}

.images-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16rpx;
}

.image-wrapper {
  aspect-ratio: 1;
  border-radius: 12rpx;
  overflow: hidden;
}

.detail-image {
  width: 100%;
  height: 100%;
}

/* 管理员回复 */
.reply-time {
  font-size: 24rpx;
  color: #999;
}

.reply-content {
  display: flex;
  gap: 20rpx;
}

.reply-avatar {
  flex-shrink: 0;
}

.reply-avatar image {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
}

.reply-text {
  flex: 1;
}

.reply-author {
  font-size: 28rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.reply-message {
  font-size: 30rpx;
  line-height: 1.6;
  color: #666;
  background-color: #f8f9fa;
  padding: 20rpx;
  border-radius: 12rpx;
}

/* 设备信息 */
.toggle-icon {
  font-size: 24rpx;
  color: #999;
  transition: transform 0.3s ease;
  transform: rotate(90deg);
}

.toggle-icon.expanded {
  transform: rotate(-90deg);
}

.device-details {
  margin-top: 20rpx;
}

.device-item {
  display: flex;
  margin-bottom: 12rpx;
  font-size: 28rpx;
}

.device-label {
  color: #666;
  width: 160rpx;
  flex-shrink: 0;
}

.device-value {
  color: #333;
  word-break: break-all;
}

/* 处理记录时间轴 */
.history-timeline {
  position: relative;
}

.timeline-item {
  display: flex;
  margin-bottom: 30rpx;
  position: relative;
}

.timeline-item:not(:last-child)::after {
  content: '';
  position: absolute;
  left: 18rpx;
  top: 36rpx;
  bottom: -30rpx;
  width: 2rpx;
  background-color: #e9ecef;
}

.timeline-dot {
  width: 16rpx;
  height: 16rpx;
  background-color: #3B7ADB;
  border-radius: 50%;
  margin-top: 10rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
  position: relative;
  z-index: 1;
}

.timeline-content {
  flex: 1;
}

.timeline-title {
  font-size: 30rpx;
  font-weight: 600;
  color: #333;
  margin-bottom: 8rpx;
}

.timeline-time {
  font-size: 24rpx;
  color: #999;
  margin-bottom: 8rpx;
}

.timeline-desc {
  font-size: 28rpx;
  color: #666;
  line-height: 1.5;
} 