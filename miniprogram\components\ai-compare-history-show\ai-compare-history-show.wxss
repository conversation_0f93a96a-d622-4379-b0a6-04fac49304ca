/* components/ai-compare-history-show/ai-compare-history-show.wxss */

/**
 * AI对比历史展示组件样式
 * AI Comparison History Show Component Styles
 * 
 * 📋 功能说明:
 * - 展示AI产品对比历史记录列表
 * - 支持加载状态和空状态展示
 * - 优雅的交互反馈效果
 * 
 * 🎨 设计特色:
 * - 卡片式布局展示历史记录，参考product-show组件风格
 * - 清晰的信息层级和状态标识
 * - 响应式设计适配不同屏幕
 * 
 * 依赖: styles/variables.wxss, styles/components.wxss, styles/utilities.wxss
 */

/* ==================== 组件容器 Component Container ==================== */

.ai-compare-history-show {
  width: 100%;
  padding: 0 30rpx;
}

/* ==================== 历史记录列表 History List ==================== */

.history-list {
  display: flex;
  flex-direction: column;
  gap: 24rpx;
}

.history-item {
  background: #f8f9fa;
  border-radius: 16rpx;
  padding: 32rpx;
  border: 2rpx solid transparent;
  transition: all 0.3s ease;
  position: relative;
}

.history-item:active {
  transform: translateY(2rpx);
  border-color: #3B7ADB;
  box-shadow: 0 8rpx 24rpx rgba(59, 122, 219, 0.15);
  background: #ffffff;
}

/* ==================== 头部信息 Header ==================== */

.item-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 24rpx;
  padding-bottom: 16rpx;
  border-bottom: 1rpx solid #e8e9ea;
}

.header-left {
  display: flex;
  align-items: center;
  gap: 16rpx;
}

.type-tag {
  background: #3B7ADB;
  color: #ffffff;
  font-size: 22rpx;
  font-weight: 500;
  padding: 8rpx 16rpx;
  border-radius: 20rpx;
}

.product-count {
  background: rgba(59, 122, 219, 0.1);
  color: #3B7ADB;
  font-size: 22rpx;
  padding: 6rpx 12rpx;
  border-radius: 16rpx;
}

.header-right {
  display: flex;
  align-items: center;
}

.time-text {
  font-size: 24rpx;
  color: #999999;
  font-weight: 400;
}

/* ==================== 产品信息区域 Products Section ==================== */

.products-section {
  margin-bottom: 24rpx;
}

.query-content {
  margin-bottom: 20rpx;
}

.query-text {
  font-size: 28rpx;
  font-weight: 500;
  color: #333333;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.products-preview {
  display: flex;
  flex-wrap: wrap;
  gap: 12rpx;
  align-items: center;
}

.product-tag {
  background: #ffffff;
  border: 1rpx solid #e6e6e6;
  border-radius: 20rpx;
  padding: 8rpx 16rpx;
  transition: all 0.2s ease;
}

.product-name {
  font-size: 24rpx;
  color: #333333;
  font-weight: 400;
}

.more-products {
  background: #e8e9ea;
  border-radius: 20rpx;
  padding: 8rpx 12rpx;
}

.more-text {
  font-size: 22rpx;
  color: #666666;
}

.empty-products {
  padding: 20rpx 0;
}

.empty-text {
  font-size: 24rpx;
  color: #999999;
  font-style: italic;
}

/* ==================== 底部状态 Footer ==================== */

.item-footer {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding-top: 16rpx;
  border-top: 1rpx solid #e8e9ea;
}

.status-badge {
  display: flex;
  align-items: center;
}

.status-text {
  font-size: 24rpx;
  font-weight: 500;
  padding: 6rpx 12rpx;
  border-radius: 12rpx;
}

.status-success {
  background: rgba(82, 196, 26, 0.1);
  color: #52c41a;
}

.status-processing {
  background: rgba(250, 173, 20, 0.1);
  color: #faad14;
}

.status-failed {
  background: rgba(255, 77, 79, 0.1);
  color: #ff4d4f;
}

.action-tip {
  display: flex;
  align-items: center;
  gap: 8rpx;
}

.action-text {
  font-size: 24rpx;
  color: #666666;
}

.action-arrow {
  font-size: 24rpx;
  color: #3B7ADB;
  font-weight: 500;
}

/* ==================== 加载状态 Loading States ==================== */

.load-more {
  text-align: center;
  padding: 40rpx 0;
}

.loading-text {
  font-size: 24rpx;
  color: #999999;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 30rpx;
}

.loading-icon {
  margin-bottom: 24rpx;
}

.loading-spinner {
  font-size: 48rpx;
  color: #3B7ADB;
  animation: spin 1s linear infinite;
}

.loading-content {
  text-align: center;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* ==================== 空状态 Empty State ==================== */

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 30rpx 80rpx;
  text-align: center;
}

.empty-icon {
  margin-bottom: 32rpx;
}

.empty-emoji {
  font-size: 120rpx;
  opacity: 0.6;
}

.empty-content {
  margin-bottom: 48rpx;
}

.empty-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
  margin-bottom: 16rpx;
}

.empty-desc {
  font-size: 26rpx;
  color: #666666;
  line-height: 1.4;
}

.empty-action {
  width: 100%;
}

.action-btn {
  background: #3B7ADB;
  color: #ffffff;
  font-size: 28rpx;
  font-weight: 500;
  padding: 24rpx 48rpx;
  border-radius: 40rpx;
  text-align: center;
  transition: all 0.3s ease;
  min-width: 280rpx;
  margin: 0 auto;
}

.action-btn:active {
  background: #2d5aa0;
  transform: scale(0.98);
}
/* ==================== 响应式设计 Responsive Design ==================== */

/* 小屏幕适配 */
@media (max-width: 375px) {
  .ai-compare-history-show {
    padding: 0 20rpx;
  }
  
  .history-item {
    padding: 24rpx;
  }
  
  .type-tag {
    font-size: 20rpx;
    padding: 6rpx 12rpx;
  }
  
  .query-text {
    font-size: 26rpx;
  }
  
  .empty-emoji {
    font-size: 100rpx;
  }
  
  .action-btn {
    min-width: 240rpx;
    padding: 20rpx 40rpx;
  }
}

/* 大屏幕优化 */
@media (min-width: 414px) {
  .ai-compare-history-show {
    max-width: 750rpx;
    margin: 0 auto;
    padding: 0 40rpx;
  }
}

/* ==================== 交互优化 Interaction Enhancements ==================== */

/* 触摸反馈优化 */
.history-item,
.action-btn {
  -webkit-tap-highlight-color: transparent;
}

/* 过渡动画 */
.history-item,
.type-tag,
.product-tag,
.action-btn {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* 悬停效果（支持的设备） */
@media (hover: hover) {
  .history-item:hover {
    box-shadow: 0 8rpx 24rpx rgba(0, 0, 0, 0.1);
    border-color: #3B7ADB;
  }
  
  .action-btn:hover {
    background: #2d5aa0;
  }
}
