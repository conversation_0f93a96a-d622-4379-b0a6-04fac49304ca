const config = require('../config/SmsConfig');
const SmsVerification = require('../models/SmsVerification');
const { generateRandomCode } = require('../utils/random');

/**
 * 短信服务
 */
class SmsService {
  /**
   * 发送验证码短信
   * @param {String} phone 手机号
   * @param {String} purpose 用途
   * @param {Object} deviceInfo 设备信息
   * @param {String} ip 请求IP
   * @returns {Promise} 发送结果
   */
  async sendVerificationCode(phone, purpose, deviceInfo = {}, ip = '') {
    try {
      // 检查发送频率限制
      await this.checkSendFrequency(phone, ip);
      
      // 模拟或生成验证码
      const verifyCode = config.useMockSms ? config.mockVerifyCode : generateRandomCode(6);
      
      // 如果是模拟模式，打印验证码
      if (config.useMockSms) {
        console.log(`【模拟短信】向手机号 ${phone} 发送验证码: ${verifyCode}，用途: ${purpose}`);
      } else {
        // 实际项目中，这里应该调用短信API发送短信
        // await this.sendRealSms(phone, verifyCode, purpose);
      }
      
      // 保存验证码记录
      await SmsVerification.create({
        phone,
        verifyCode,
        purpose,
        ip,
        deviceInfo
      });
      
      return {
        success: true,
        message: '验证码已发送',
        expiresIn: config.expireTime,
        // 掩码处理手机号
        account: this.maskPhoneNumber(phone)
      };
    } catch (error) {
      console.error('短信发送错误:', error);
      throw error;
    }
  }
  
  /**
   * 检查发送频率限制
   * @param {String} phone 手机号
   * @param {String} ip 请求IP
   */
  async checkSendFrequency(phone, ip) {
    // 检查60秒内是否已发送
    const oneMinuteAgo = new Date(Date.now() - 60 * 1000);
    const recentRecord = await SmsVerification.findOne({
      phone,
      createdAt: { $gt: oneMinuteAgo }
    });
    
    if (recentRecord) {
      throw {
        success: false,
        statusCode: 429,
        message: '操作太频繁，请稍后再试',
        remainingTime: Math.ceil((recentRecord.createdAt.getTime() + 60000 - Date.now()) / 1000)
      };
    }
    
    // 检查1小时内发送次数
    const oneHourAgo = new Date(Date.now() - 3600 * 1000);
    const hourlyCount = await SmsVerification.countDocuments({
      phone,
      createdAt: { $gt: oneHourAgo }
    });
    
    if (hourlyCount >= config.rateLimit.perHour) {
      throw {
        success: false,
        statusCode: 429,
        message: '短信发送过于频繁，请1小时后再试'
      };
    }
    
    // 检查24小时内发送次数
    const oneDayAgo = new Date(Date.now() - 86400 * 1000);
    const dailyCount = await SmsVerification.countDocuments({
      phone,
      createdAt: { $gt: oneDayAgo }
    });
    
    if (dailyCount >= config.rateLimit.perDay) {
      throw {
        success: false,
        statusCode: 429,
        message: '今日发送短信次数已达上限，请明天再试'
      };
    }
    
    // 检查IP限制
    if (ip) {
      const hourlyIpCount = await SmsVerification.countDocuments({
        ip,
        createdAt: { $gt: oneHourAgo }
      });
      
      if (hourlyIpCount >= 20) {
        throw {
          success: false,
          statusCode: 429,
          message: '当前网络环境请求次数过多，请稍后再试'
        };
      }
    }
  }
  
  /**
   * 验证短信验证码
   * @param {String} phone 手机号
   * @param {String} verifyCode 验证码
   * @param {String} purpose 用途
   * @returns {Promise} 验证结果
   */
  async verifyCode(phone, verifyCode, purpose) {
    // 查找验证码记录
    const record = await SmsVerification.findOne({
      phone,
      purpose,
      status: 'pending'
    }).sort({ createdAt: -1 });
    
    // 验证码不存在
    if (!record) {
      return {
        success: false,
        message: '验证码无效或已过期，请重新获取'
      };
    }
    
    // 更新尝试次数
    record.attempts += 1;
    
    // 检查尝试次数
    if (record.attempts >= 5) {
      record.status = 'expired';
      await record.save();
      
      return {
        success: false,
        message: '验证码尝试次数过多，请重新获取'
      };
    }
    
    // 验证码错误
    if (record.verifyCode !== verifyCode) {
      await record.save();
      
      return {
        success: false,
        message: '验证码错误，请重新输入',
        remainingAttempts: 5 - record.attempts
      };
    }
    
    // 验证成功
    record.status = 'verified';
    record.verifiedAt = new Date();
    await record.save();
    
    return {
      success: true,
      message: '验证成功'
    };
  }
  
  /**
   * 掩码处理手机号
   * @param {String} phone 手机号
   * @returns {String} 掩码后的手机号
   */
  maskPhoneNumber(phone) {
    if (!phone || phone.length < 11) return phone;
    return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
  }
  
  /**
   * 根据用途获取短信模板代码
   * @param {String} purpose 用途
   * @returns {String} 模板代码
   */
  getTemplateCode(purpose) {
    const templates = config.templates;
    switch (purpose) {
      case 'register':
        return templates.register;
      case 'login':
        return templates.login;
      case 'reset_password':
        return templates.resetPassword;
      case 'change_phone':
        return templates.changePhone;
      case 'withdraw':
        return templates.withdraw;
      default:
        return templates.register;
    }
  }
}

module.exports = new SmsService(); 