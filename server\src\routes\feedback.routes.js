const express = require('express');
const router = express.Router();
const feedbackController = require('../controllers/feedbackController');
const { protect, admin } = require('../middlewares/auth');

// 提交反馈
router.post('/', protect, feedbackController.createFeedback);

// 上传反馈图片
router.post('/upload-images', protect, feedbackController.uploadFeedbackImage);

// 获取用户的反馈列表
router.get('/my', protect, feedbackController.getUserFeedbacks);

// 获取所有反馈（管理员用）
router.get('/all', protect, admin, feedbackController.getAllFeedbacks);

// 获取反馈详情
router.get('/:id', protect, feedbackController.getFeedbackDetail);

// 更新反馈状态（管理员用）
router.patch('/:id/status', protect, admin, feedbackController.updateFeedbackStatus);

// 管理员回复反馈
router.post('/:id/reply', protect, admin, feedbackController.replyFeedback);

module.exports = router; 