#!/usr/bin/env node

/**
 * 查询和验证 NewProduct 数据的脚本
 */

const mongoose = require('mongoose');
require('dotenv').config();

const NewProduct = require('../src/models/NewProduct');

class NewProductQuery {
  constructor() {
    this.mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/xuanxuan_test';
  }

  async connect() {
    try {
      await mongoose.connect(this.mongoUri);
      console.log('✅ 已连接到 MongoDB:', this.mongoUri);
    } catch (error) {
      console.error('❌ MongoDB 连接失败:', error.message);
      throw error;
    }
  }

  async disconnect() {
    try {
      await mongoose.disconnect();
      console.log('✅ 已断开 MongoDB 连接');
    } catch (error) {
      console.error('❌ 断开 MongoDB 连接失败:', error.message);
    }
  }

  /**
   * 获取基本统计信息
   */
  async getStats() {
    try {
      const total = await NewProduct.countDocuments();
      const byBrand = await NewProduct.aggregate([
        { $group: { _id: '$brandName', count: { $sum: 1 } } },
        { $sort: { count: -1 } }
      ]);
      const byType = await NewProduct.aggregate([
        { $group: { _id: '$productType', count: { $sum: 1 } } },
        { $sort: { count: -1 } }
      ]);
      const byFolder = await NewProduct.aggregate([
        { $group: { _id: '$brandFolder', count: { $sum: 1 } } },
        { $sort: { count: -1 } }
      ]);

      return { total, byBrand, byType, byFolder };
    } catch (error) {
      console.error('❌ 获取统计信息失败:', error.message);
      return null;
    }
  }

  /**
   * 显示统计信息
   */
  async showStats() {
    console.log('📊 NewProduct 数据统计\n');
    
    const stats = await this.getStats();
    if (!stats) return;

    console.log(`📱 总产品数: ${stats.total}\n`);

    console.log('📈 按品牌分布:');
    stats.byBrand.forEach(item => {
      console.log(`  ${item._id}: ${item.count}`);
    });

    console.log('\n📈 按产品类型分布:');
    stats.byType.forEach(item => {
      console.log(`  ${item._id}: ${item.count}`);
    });

    console.log('\n📈 按品牌目录分布:');
    stats.byFolder.forEach(item => {
      console.log(`  ${item._id}: ${item.count}`);
    });
  }

  /**
   * 查看示例产品
   */
  async showSamples(limit = 3) {
    try {
      console.log(`\n📋 示例产品 (前${limit}个):\n`);
      
      const samples = await NewProduct.find().limit(limit);
      
      samples.forEach((product, index) => {
        console.log(`${index + 1}. ${product.skuName}`);
        console.log(`   品牌: ${product.brandName}`);
        console.log(`   类型: ${product.productType}`);
        console.log(`   配置数: ${product.configurations.length}`);
        console.log(`   价格范围: ¥${product.priceRange.min} - ¥${product.priceRange.max}`);
        console.log(`   文件路径: ${product.sourceFilePath}`);
        console.log(`   搜索关键词: ${product.searchKeywords.slice(0, 5).join(', ')}...`);
        console.log('');
      });
    } catch (error) {
      console.error('❌ 获取示例产品失败:', error.message);
    }
  }

  /**
   * 按品牌查询
   */
  async findByBrand(brandName) {
    try {
      const products = await NewProduct.find({ 
        brandName: { $regex: brandName, $options: 'i' } 
      }).select('skuName brandName priceRange configurations');
      
      console.log(`\n🔍 品牌 "${brandName}" 的产品 (${products.length}个):\n`);
      
      products.forEach((product, index) => {
        console.log(`${index + 1}. ${product.skuName}`);
        console.log(`   配置数: ${product.configurations.length}`);
        console.log(`   价格: ¥${product.priceRange.min} - ¥${product.priceRange.max}`);
        console.log('');
      });
      
      return products;
    } catch (error) {
      console.error('❌ 品牌查询失败:', error.message);
      return [];
    }
  }

  /**
   * 搜索产品
   */
  async searchProducts(keyword) {
    try {
      const products = await NewProduct.find({
        $or: [
          { skuName: { $regex: keyword, $options: 'i' } },
          { brandName: { $regex: keyword, $options: 'i' } },
          { searchKeywords: { $regex: keyword, $options: 'i' } }
        ]
      }).select('skuName brandName priceRange');
      
      console.log(`\n🔍 搜索 "${keyword}" 的结果 (${products.length}个):\n`);
      
      products.forEach((product, index) => {
        console.log(`${index + 1}. ${product.skuName} (${product.brandName})`);
        console.log(`   价格: ¥${product.priceRange.min} - ¥${product.priceRange.max}`);
        console.log('');
      });
      
      return products;
    } catch (error) {
      console.error('❌ 搜索失败:', error.message);
      return [];
    }
  }

  /**
   * 价格范围查询
   */
  async findByPriceRange(minPrice, maxPrice) {
    try {
      const products = await NewProduct.find({
        'priceRange.min': { $gte: minPrice },
        'priceRange.max': { $lte: maxPrice }
      }).select('skuName brandName priceRange');
      
      console.log(`\n💰 价格范围 ¥${minPrice} - ¥${maxPrice} 的产品 (${products.length}个):\n`);
      
      products.forEach((product, index) => {
        console.log(`${index + 1}. ${product.skuName} (${product.brandName})`);
        console.log(`   价格: ¥${product.priceRange.min} - ¥${product.priceRange.max}`);
        console.log('');
      });
      
      return products;
    } catch (error) {
      console.error('❌ 价格查询失败:', error.message);
      return [];
    }
  }

  /**
   * 检查数据完整性
   */
  async checkDataIntegrity() {
    try {
      console.log('\n🔍 检查数据完整性...\n');

      // 检查缺失必要字段的记录
      const missingFields = await NewProduct.find({
        $or: [
          { skuId: { $exists: false } },
          { skuName: { $exists: false } },
          { brandName: { $exists: false } },
          { productType: { $exists: false } }
        ]
      }).select('skuId skuName brandName productType sourceFilePath');

      if (missingFields.length > 0) {
        console.log(`❌ 发现 ${missingFields.length} 个记录缺失必要字段:`);
        missingFields.forEach(product => {
          console.log(`  - ${product.sourceFilePath}: ${product.skuName || '无名称'}`);
        });
      } else {
        console.log('✅ 所有记录都包含必要字段');
      }

      // 检查重复的 skuId
      const duplicates = await NewProduct.aggregate([
        { $group: { _id: '$skuId', count: { $sum: 1 }, docs: { $push: '$sourceFilePath' } } },
        { $match: { count: { $gt: 1 } } }
      ]);

      if (duplicates.length > 0) {
        console.log(`\n❌ 发现 ${duplicates.length} 个重复的 skuId:`);
        duplicates.forEach(dup => {
          console.log(`  - skuId: ${dup._id} (${dup.count}次)`);
          dup.docs.forEach(doc => console.log(`    ${doc}`));
        });
      } else {
        console.log('✅ 没有重复的 skuId');
      }

      // 检查空配置
      const emptyConfigs = await NewProduct.find({
        $or: [
          { configurations: { $size: 0 } },
          { configurations: { $exists: false } }
        ]
      }).select('skuName sourceFilePath');

      if (emptyConfigs.length > 0) {
        console.log(`\n⚠️  发现 ${emptyConfigs.length} 个产品没有配置:`);
        emptyConfigs.forEach(product => {
          console.log(`  - ${product.skuName}: ${product.sourceFilePath}`);
        });
      } else {
        console.log('✅ 所有产品都有配置信息');
      }

      return {
        missingFields: missingFields.length,
        duplicates: duplicates.length,
        emptyConfigs: emptyConfigs.length
      };
    } catch (error) {
      console.error('❌ 数据完整性检查失败:', error.message);
      return null;
    }
  }
}

async function main() {
  const args = process.argv.slice(2);
  const query = new NewProductQuery();
  
  try {
    await query.connect();
    
    if (args.includes('--stats')) {
      await query.showStats();
    } else if (args.includes('--samples')) {
      const limit = parseInt(args[args.indexOf('--samples') + 1]) || 3;
      await query.showSamples(limit);
    } else if (args.includes('--brand')) {
      const brand = args[args.indexOf('--brand') + 1];
      if (brand) {
        await query.findByBrand(brand);
      } else {
        console.log('❌ 请指定品牌名称');
      }
    } else if (args.includes('--search')) {
      const keyword = args[args.indexOf('--search') + 1];
      if (keyword) {
        await query.searchProducts(keyword);
      } else {
        console.log('❌ 请指定搜索关键词');
      }
    } else if (args.includes('--price')) {
      const minIdx = args.indexOf('--price') + 1;
      const min = parseInt(args[minIdx]);
      const max = parseInt(args[minIdx + 1]);
      if (min && max) {
        await query.findByPriceRange(min, max);
      } else {
        console.log('❌ 请指定价格范围 (最低价 最高价)');
      }
    } else if (args.includes('--check')) {
      await query.checkDataIntegrity();
    } else if (args.includes('--help')) {
      console.log(`
NewProduct 数据查询工具

使用方法: node scripts/queryNewProducts.js [选项]

选项:
  --stats              显示统计信息
  --samples [数量]     显示示例产品 (默认3个)
  --brand <品牌名>     按品牌查询
  --search <关键词>    搜索产品
  --price <最低价> <最高价>  按价格范围查询
  --check              检查数据完整性
  --help               显示帮助信息

示例:
  node scripts/queryNewProducts.js --stats
  node scripts/queryNewProducts.js --brand 荣耀
  node scripts/queryNewProducts.js --search Magic
  node scripts/queryNewProducts.js --price 2000 5000
  node scripts/queryNewProducts.js --check
      `);
    } else {
      // 默认显示基本信息
      await query.showStats();
      await query.showSamples();
    }
    
  } catch (error) {
    console.error('❌ 查询失败:', error.message);
  } finally {
    await query.disconnect();
  }
}

if (require.main === module) {
  main().catch(error => {
    console.error('❌ 程序执行失败:', error.message);
    process.exit(1);
  });
}

module.exports = NewProductQuery;
