const express = require('express');
const authController = require('../controllers/authController');
const { protect } = require('../middlewares/auth');

const router = express.Router();

// 公开路由
router.post('/verify-code', authController.sendVerifyCode);
router.post('/phone-auth', authController.phoneAuth);
router.post('/wx-login', authController.wxLogin);
router.post('/refresh-token', authController.refreshToken);

// 需要认证的路由
router.post('/logout', protect, authController.logout);
router.get('/me', protect, authController.getCurrentUser);

module.exports = router; 