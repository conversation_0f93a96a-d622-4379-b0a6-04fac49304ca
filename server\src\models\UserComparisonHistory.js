const mongoose = require('mongoose');

const UserComparisonHistorySchema = new mongoose.Schema(
  {
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
      index: true
    },
    productNames: {
      type: [String],
      required: true,
      validate: {
        validator: function(v) {
          return Array.isArray(v) && v.length >= 2 && v.length <= 6;
        },
        message: '产品名称数组长度必须在2-6之间'
      }
    },
    comparisonCacheId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'ProductComparisonV4Cache',
      required: false
    }
  },
  {
    timestamps: true,
    collection: 'user_comparison_histories'
  }
);

// 复合索引：用户ID + 创建时间（用于查询用户历史记录）
UserComparisonHistorySchema.index({ userId: 1, createdAt: -1 });

// 静态方法：创建对比历史记录
UserComparisonHistorySchema.statics.createHistory = async function(userId, productNames, comparisonCacheId = null) {
  try {
    const history = new this({
      userId,
      productNames,
      comparisonCacheId
    });
    
    return await history.save();
  } catch (error) {
    throw new Error(`创建对比历史记录失败: ${error.message}`);
  }
};

// 静态方法：获取用户的对比历史记录
UserComparisonHistorySchema.statics.getUserHistory = async function(userId, page = 1, limit = 20) {
  try {
    const skip = (page - 1) * limit;
    
    const histories = await this.find({ userId })
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit)
      .populate('comparisonCacheId', 'productNames createdAt')
      .lean();
    
    const total = await this.countDocuments({ userId });
    
    return {
      histories,
      pagination: {
        current: page,
        total: Math.ceil(total / limit),
        hasNext: skip + histories.length < total,
        hasPrev: page > 1
      }
    };
  } catch (error) {
    throw new Error(`获取用户对比历史失败: ${error.message}`);
  }
};

// 静态方法：查找或更新产品组对比历史记录
UserComparisonHistorySchema.statics.findOrUpdateHistory = async function(userId, productNames, comparisonCacheId = null) {
  try {
    // 标准化产品名称数组（排序以确保顺序一致性）
    const sortedProductNames = [...productNames].sort();
    
    // 查找是否存在相同的产品组对比历史
    const existingHistory = await this.findOne({
      userId,
      $expr: {
        $and: [
          { $eq: [{ $size: "$productNames" }, sortedProductNames.length] },
          { $setEquals: ["$productNames", sortedProductNames] }
        ]
      }
    }).sort({ createdAt: -1 });
    
    if (existingHistory) {
      // 如果存在相同的产品组对比，更新时间戳和缓存ID
      existingHistory.updatedAt = new Date();
      if (comparisonCacheId) {
        existingHistory.comparisonCacheId = comparisonCacheId;
      }
      return await existingHistory.save();
    } else {
      // 如果不存在，创建新的历史记录
      const history = new this({
        userId,
        productNames: sortedProductNames, // 使用排序后的产品名称
        comparisonCacheId
      });
      
      return await history.save();
    }
  } catch (error) {
    throw new Error(`查找或更新对比历史记录失败: ${error.message}`);
  }
};

module.exports = mongoose.model('UserComparisonHistory', UserComparisonHistorySchema);