#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
中关村在线苹果手机列表页面爬虫 - 增强版
爬取所有苹果手机的详细参数信息（从参数页面获取）
包含反爬虫机制
"""

import requests
from bs4 import BeautifulSoup
import json
import time
import re
import chardet
from urllib.parse import urljoin, urlparse
import os
import random
from fake_useragent import UserAgent

class ZOLApplePhonesCrawler:
    def __init__(self):
        # 使用随机User-Agent
        try:
            ua = UserAgent()
            user_agent = ua.random
        except:
            # 如果fake_useragent失败，使用预设的User-Agent列表
            user_agents = [
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
                'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0',
                'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            ]
            user_agent = random.choice(user_agents)
        
        self.headers = {
            'User-Agent': user_agent,
            'Accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/webp,image/apng,*/*;q=0.8',
            'Accept-Language': 'zh-CN,zh;q=0.9,en;q=0.8,en-US;q=0.7',
            'Accept-Encoding': 'gzip, deflate, br',
            'Connection': 'keep-alive',
            'Upgrade-Insecure-Requests': '1',
            'Cache-Control': 'no-cache',
            'DNT': '1',
            'Sec-Fetch-Dest': 'document',
            'Sec-Fetch-Mode': 'navigate',
            'Sec-Fetch-Site': 'none',
            'Sec-Fetch-User': '?1'
        }
        self.session = requests.Session()
        self.session.headers.update(self.headers)
        self.base_url = 'https://detail.zol.com.cn'
        
        # 反爬虫设置
        self.min_delay = 2  # 最小延时（秒）
        self.max_delay = 5  # 最大延时（秒）
        self.request_count = 0
        self.max_requests_per_session = 20  # 每个session最大请求数
        
    def random_delay(self):
        """随机延时"""
        delay = random.uniform(self.min_delay, self.max_delay)
        print(f"等待 {delay:.1f} 秒...")
        time.sleep(delay)
    
    def rotate_user_agent(self):
        """轮换User-Agent"""
        user_agents = [
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/119.0.0.0 Safari/537.36',
            'Mozilla/5.0 (Windows NT 10.0; Win64; x64; rv:109.0) Gecko/20100101 Firefox/121.0',
            'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
            'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
        ]
        new_ua = random.choice(user_agents)
        self.session.headers.update({'User-Agent': new_ua})
        print(f"轮换User-Agent: {new_ua[:50]}...")
    
    def create_new_session(self):
        """创建新的session"""
        print("创建新的session...")
        self.session.close()
        self.session = requests.Session()
        self.rotate_user_agent()
        self.session.headers.update(self.headers)
        self.request_count = 0
        time.sleep(random.uniform(3, 6))  # 更长的延时
        
    def safe_request(self, url, **kwargs):
        """安全的请求方法，包含反爬虫机制"""
        self.request_count += 1
        
        # 每隔一定请求数就轮换User-Agent
        if self.request_count % 5 == 0:
            self.rotate_user_agent()
        
        # 每隔一定请求数就创建新session
        if self.request_count >= self.max_requests_per_session:
            self.create_new_session()
        
        try:
            # 添加随机延时
            self.random_delay()
            
            # 发送请求
            response = self.session.get(url, timeout=15, **kwargs)
            
            # 检查是否被反爬虫拦截
            if response.status_code == 403:
                print("检测到403错误，可能被反爬虫拦截，等待更长时间...")
                time.sleep(random.uniform(10, 20))
                self.create_new_session()
                response = self.session.get(url, timeout=15, **kwargs)
            
            return response
            
        except requests.exceptions.RequestException as e:
            print(f"请求失败: {e}")
            return None
        
    def detect_and_decode(self, content):
        """检测并正确解码内容"""
        try:
            # 尝试检测编码
            detected = chardet.detect(content)
            encoding = detected.get('encoding', 'utf-8')
            
            # 尝试多种编码方式
            encodings_to_try = [encoding, 'utf-8', 'gbk', 'gb2312', 'gb18030']
            
            for enc in encodings_to_try:
                try:
                    if enc:
                        decoded = content.decode(enc)
                        return decoded
                except (UnicodeDecodeError, LookupError):
                    continue
            
            # 如果都失败了，使用错误忽略模式
            return content.decode('utf-8', errors='ignore')
            
        except Exception as e:
            print(f"编码检测失败: {e}")
            return content.decode('utf-8', errors='ignore')
    
    def get_param_url_from_detail_url(self, detail_url):
        """从详情页URL转换为参数页URL"""
        try:
            # 例如: https://detail.zol.com.cn/cell_phone/index1950439.shtml
            # 转换为: https://detail.zol.com.cn/1951/1950439/param.shtml
            
            # 提取产品ID
            match = re.search(r'index(\d+)\.shtml', detail_url)
            if match:
                product_id = match.group(1)
                # 构建参数页URL - 根据中关村在线的URL规律
                # 通常是 /分类ID/产品ID/param.shtml
                # 手机的分类ID通常是1951
                param_url = f"https://detail.zol.com.cn/1951/{product_id}/param.shtml"
                return param_url
            
            # 如果上面的方法失败，尝试其他模式
            if '/cell_phone/' in detail_url:
                # 替换路径
                param_url = detail_url.replace('/cell_phone/index', '/1951/').replace('.shtml', '/param.shtml')
                return param_url
                
            return None
            
        except Exception as e:
            print(f"转换参数页URL失败: {e}")
            return None
    
    def get_phone_list(self, list_url):
        """获取苹果手机列表"""
        try:
            print(f"正在获取手机列表页面: {list_url}")
            
            response = self.safe_request(list_url)
            if not response:
                return []
                
            html_content = self.detect_and_decode(response.content)
            soup = BeautifulSoup(html_content, 'html.parser')
            
            phones = []
            
            # 查找手机产品列表 - 中关村在线的产品列表通常在特定的容器中
            product_selectors = [
                '.product-list .product-item',
                '.list-item',
                '.product-box',
                '.product-card',
                'li[data-product-id]',
                '.product-info',
                'a[href*="/cell_phone/"]',
                'a[href*="detail.zol.com.cn"]'
            ]
            
            found_products = False
            
            for selector in product_selectors:
                elements = soup.select(selector)
                if elements:
                    print(f"使用选择器 '{selector}' 找到 {len(elements)} 个产品")
                    
                    for element in elements:
                        phone_info = self.extract_phone_info_from_element(element)
                        if phone_info:
                            phones.append(phone_info)
                            found_products = True
                    
                    if found_products:
                        break
            
            # 如果上面的方法没找到，尝试查找所有包含苹果手机链接的元素
            if not found_products:
                print("尝试查找所有苹果手机相关链接...")
                all_links = soup.find_all('a', href=re.compile(r'(detail\.zol\.com\.cn|/cell_phone/)'))
                
                for link in all_links:
                    href = link.get('href', '')
                    text = link.get_text(strip=True)
                    
                    # 检查是否是苹果手机相关
                    if any(keyword in text.lower() for keyword in ['iphone', '苹果', 'apple']) or 'iPhone' in text:
                        # 构建完整URL
                        if href.startswith('//'):
                            full_url = 'https:' + href
                        elif href.startswith('/'):
                            full_url = urljoin(self.base_url, href)
                        elif not href.startswith('http'):
                            continue
                        else:
                            full_url = href
                        
                        # 检查是否是详情页链接
                        if 'detail.zol.com.cn' in full_url and 'cell_phone' in full_url:
                            phone_info = {
                                'name': text,
                                'detail_url': full_url,
                                'source': 'link_extraction'
                            }
                            
                            # 避免重复
                            if not any(p['detail_url'] == full_url for p in phones):
                                phones.append(phone_info)
            
            print(f"总共找到 {len(phones)} 个苹果手机产品")
            return phones
            
        except Exception as e:
            print(f"获取手机列表失败: {e}")
            import traceback
            traceback.print_exc()
            return []
    
    def extract_phone_info_from_element(self, element):
        """从页面元素中提取手机信息"""
        try:
            phone_info = {}
            
            # 查找产品名称
            name_selectors = [
                '.product-name', '.title', 'h3', 'h4', 'h5', 
                '.name', '[class*="title"]', '[class*="name"]'
            ]
            
            for selector in name_selectors:
                name_elem = element.select_one(selector)
                if name_elem:
                    name = name_elem.get_text(strip=True)
                    if name and ('iPhone' in name or '苹果' in name or 'Apple' in name):
                        phone_info['name'] = name
                        break
            
            # 如果元素本身就是链接
            if element.name == 'a':
                href = element.get('href', '')
                text = element.get_text(strip=True)
                if ('iPhone' in text or '苹果' in text) and href:
                    phone_info['name'] = text
                    phone_info['detail_url'] = href if href.startswith('http') else urljoin(self.base_url, href)
            else:
                # 查找详情页链接
                link_elem = element.find('a', href=re.compile(r'(detail\.zol\.com\.cn|/cell_phone/)'))
                if link_elem:
                    href = link_elem.get('href', '')
                    if href:
                        phone_info['detail_url'] = href if href.startswith('http') else urljoin(self.base_url, href)
            
            # 查找价格
            price_selectors = ['.price', '.current-price', '[class*="price"]']
            for selector in price_selectors:
                price_elem = element.select_one(selector)
                if price_elem:
                    price = price_elem.get_text(strip=True)
                    if price and ('¥' in price or '元' in price or price.isdigit()):
                        phone_info['price'] = price
                        break
            
            # 只返回包含苹果手机信息的结果
            if ('name' in phone_info and 
                ('iPhone' in phone_info['name'] or '苹果' in phone_info['name'] or 'Apple' in phone_info['name']) and
                'detail_url' in phone_info):
                return phone_info
            
            return None
            
        except Exception as e:
            print(f"提取手机信息失败: {e}")
            return None
    
    def get_phone_specs(self, phone_info):
        """获取单个手机的详细规格参数（从参数页面）"""
        try:
            detail_url = phone_info['detail_url']
            print(f"正在处理手机: {phone_info.get('name', 'Unknown')}")
            print(f"详情页URL: {detail_url}")
            
            # 转换为参数页URL
            param_url = self.get_param_url_from_detail_url(detail_url)
            if not param_url:
                print("无法构建参数页URL，尝试从详情页查找参数链接...")
                # 如果无法构建，先访问详情页查找参数链接
                response = self.safe_request(detail_url)
                if response:
                    html_content = self.detect_and_decode(response.content)
                    soup = BeautifulSoup(html_content, 'html.parser')
                    
                    # 查找参数链接
                    param_link = soup.find('a', text=re.compile(r'参数|规格|详细参数'))
                    if param_link:
                        param_href = param_link.get('href', '')
                        if param_href:
                            param_url = param_href if param_href.startswith('http') else urljoin(self.base_url, param_href)
                
                if not param_url:
                    print("无法找到参数页面链接")
                    return None
            
            print(f"参数页URL: {param_url}")
            
            # 访问参数页面
            response = self.safe_request(param_url)
            if not response:
                print("无法访问参数页面")
                return None
                
            html_content = self.detect_and_decode(response.content)
            soup = BeautifulSoup(html_content, 'html.parser')
            
            specs = {}
            specs['产品名称'] = phone_info.get('name', '')
            specs['详情页URL'] = detail_url
            specs['参数页URL'] = param_url
            
            if 'price' in phone_info:
                specs['价格'] = phone_info['price']
            
            # 从参数页面提取详细参数
            print("正在提取详细参数...")
            
            # 查找参数表格 - 参数页面通常有更详细的表格结构
            tables = soup.find_all('table')
            found_specs = False
            
            for i, table in enumerate(tables):
                rows = table.find_all('tr')
                table_specs = 0
                
                for row in rows:
                    cells = row.find_all(['td', 'th'])
                    if len(cells) >= 2:
                        key = cells[0].get_text(strip=True)
                        value = cells[1].get_text(strip=True)
                        
                        if (key and value and 
                            len(key) > 0 and len(value) > 0 and
                            key != value and
                            not key.lower() in ['参数', 'parameter', '项目', 'item', '规格', '属性']):
                            
                            key = re.sub(r'\s+', ' ', key).strip()
                            value = re.sub(r'\s+', ' ', value).strip()
                            
                            # 避免重复的键
                            if key not in specs:
                                specs[key] = value
                                table_specs += 1
                                found_specs = True
                
                print(f"从表格 {i+1} 中提取了 {table_specs} 个参数")
            
            # 如果表格方式获取的参数不多，尝试其他方式
            if len(specs) < 15:  # 参数页面应该有更多参数
                print("表格参数不足，尝试其他解析方式...")
                
                # 查找参数相关的div或section
                param_sections = soup.find_all(['div', 'section', 'dl'], 
                    class_=re.compile(r'param|spec|detail|info|product'))
                
                for section in param_sections:
                    # 查找dt/dd结构（定义列表）
                    dts = section.find_all('dt')
                    dds = section.find_all('dd')
                    
                    if len(dts) == len(dds):
                        for dt, dd in zip(dts, dds):
                            key = dt.get_text(strip=True)
                            value = dd.get_text(strip=True)
                            if key and value and key not in specs:
                                specs[key] = value
                    
                    # 查找其他键值对结构
                    text = section.get_text()
                    lines = text.split('\n')
                    
                    for line in lines:
                        line = line.strip()
                        if ':' in line or '：' in line:
                            for separator in ['：', ':']:
                                if separator in line:
                                    parts = line.split(separator, 1)
                                    if len(parts) == 2:
                                        key = parts[0].strip()
                                        value = parts[1].strip()
                                        
                                        if (key and value and 
                                            len(key) < 50 and len(value) < 500 and
                                            not any(x in key.lower() for x in ['http', 'www', 'copyright']) and
                                            key not in specs):
                                            specs[key] = value
                                    break
            
            specs['爬取时间'] = time.strftime('%Y-%m-%d %H:%M:%S')
            specs['参数总数'] = len(specs) - 4  # 减去元数据
            
            print(f"✅ 成功获取 {specs.get('参数总数', 0)} 个参数")
            return specs
            
        except Exception as e:
            print(f"获取手机规格失败: {e}")
            import traceback
            traceback.print_exc()
            return None
    
    def crawl_all_apple_phones(self, list_url):
        """爬取所有苹果手机的参数"""
        print("开始爬取苹果手机列表...")
        print("已启用反爬虫机制：随机延时、User-Agent轮换、Session轮换")
        
        # 获取手机列表
        phones = self.get_phone_list(list_url)
        
        if not phones:
            print("未能获取到手机列表")
            return []
        
        all_specs = []
        
        # 限制爬取数量，避免过长时间和被封
        begin_index = 28
        max_phones = min(43, len(phones))
        print(f"将爬取前 {max_phones} 个手机的参数")
        
        for i, phone in enumerate(phones[begin_index:max_phones]):
            print(f"\n{'='*60}")
            print(f"处理第 {begin_index+i+1}/{max_phones} 个手机...")
            
            specs = self.get_phone_specs(phone)
            if specs and len(specs) > 8:  # 确保获取到足够的参数
                all_specs.append(specs)
                print(f"✅ 成功获取参数")
            else:
                print("❌ 未能获取到足够的参数")
            
            # 每爬取几个产品后增加更长的延时
            if (i + 1) % 3 == 0:
                extra_delay = random.uniform(5, 10)
                print(f"阶段性休息 {extra_delay:.1f} 秒...")
                time.sleep(extra_delay)
        
        return all_specs
    
    def save_to_json(self, data, filename):
        """保存数据到JSON文件"""
        try:
            with open(filename, 'w', encoding='utf-8') as f:
                json.dump(data, f, ensure_ascii=False, indent=2)
            print(f"数据已保存到: {filename}")
            return True
        except Exception as e:
            print(f"保存文件失败: {e}")
            return False
    
    def print_summary(self, all_specs):
        """打印爬取结果摘要"""
        if not all_specs:
            print("没有获取到任何手机参数")
            return
        
        print(f"\n=== 苹果手机参数爬取摘要 ===")
        print(f"成功爬取的手机数量: {len(all_specs)}")
        
        print(f"\n手机列表:")
        for i, specs in enumerate(all_specs):
            name = specs.get('产品名称', f'手机{i+1}')
            param_count = specs.get('参数总数', len(specs) - 4)
            print(f"  {i+1}. {name} ({param_count}个参数)")
        
        # 显示第一个手机的部分参数作为示例
        if all_specs:
            print(f"\n=== 示例参数 (第一个手机) ===")
            first_phone = all_specs[0]
            shown_count = 0
            for key, value in first_phone.items():
                if key not in ['爬取时间', '详情页URL', '参数页URL', '参数总数'] and shown_count < 10:
                    print(f"  {key}: {value}")
                    shown_count += 1
            if len(first_phone) > 14:
                print(f"  ... 还有更多参数")

def main():
    """主函数"""
    crawler = ZOLApplePhonesCrawler()
    
    print("中关村在线苹果手机列表爬虫 - 增强版")
    print("包含反爬虫机制和详细参数页面爬取")
    print("=" * 60)
    
    # 目标URL
    list_url = "https://detail.zol.com.cn/cell_phone_index/subcate57_544_list_1.html"
    
    # 开始爬取
    all_specs = crawler.crawl_all_apple_phones(list_url)
    
    if all_specs:
        # 显示摘要
        crawler.print_summary(all_specs)
        
        # 保存数据
        filename = 'apple_phones_detailed_specs_3.json'
        if crawler.save_to_json(all_specs, filename):
            print(f"\n✅ 爬取完成！共获取 {len(all_specs)} 个苹果手机的详细参数数据")
            print(f"数据已保存到: {filename}")
        else:
            print(f"\n❌ 爬取成功但保存失败")
    else:
        print("\n❌ 未能获取到任何苹果手机参数")
        print("可能的原因:")
        print("1. 网络连接问题")
        print("2. 网页结构发生变化")
        print("3. 被网站反爬虫机制阻止")
        print("4. 目标页面没有苹果手机产品")

if __name__ == "__main__":
    main()