/**
 * 产品参数获取服务测试脚本
 * 测试 productParamsService.js 中的 getProductParamsByName 功能
 */

// 引入必要的模块
require('dotenv').config({ path: './.env' });
const mongoose = require('mongoose');
const { getProductParamsByName } = require('../src/services/product/getProductParams');

// 数据库连接配置
const connectDB = async () => {
  try {
    const mongoURI = process.env.MONGODB_URI || 'mongodb://localhost:27017/xuanxuan';
    console.log('🔗 连接到 MongoDB:', mongoURI);
    await mongoose.connect(mongoURI, {
      useNewUrlParser: true,
      useUnifiedTopology: true,
    });
    console.log('✅ MongoDB 连接成功');
  } catch (error) {
    console.error('❌ MongoDB 连接失败:', error.message);
    process.exit(1);
  }
};

// 测试函数
const testProductParams = async () => {
  try {
    console.log('🚀 开始测试产品参数获取功能');
    console.log('='.repeat(60));

    // 测试产品名称
    const productName = '华为 Mate 70 RS 非凡大师';
    console.log(`📱 测试产品: ${productName}`);
    console.log('-'.repeat(40));

    // 调用产品参数获取服务
    const result = await getProductParamsByName(productName);

    if (result.success) {
      console.log('✅ 获取产品参数成功!');
      console.log('\n📊 productParamsService.js 返回的原始数据格式:');
      console.log('='.repeat(80));
      
      // 直接输出原始返回数据的完整JSON结构
      console.log(JSON.stringify(result, null, 2));
      
      console.log('='.repeat(80));
      console.log('✅ 以上是 productParamsService.js 的完整原始返回数据');
      
    } else {
      console.log('❌ 获取产品参数失败!');
      console.log('错误信息:', result.error);
    }

  } catch (error) {
    console.error('❌ 测试过程中发生错误:', error);
  }
};

// 测试不存在的产品
const testNonExistentProduct = async () => {
  try {
    console.log('\n🧪 测试不存在的产品');
    console.log('-'.repeat(40));
    
    const nonExistentProduct = '不存在的产品名称';
    console.log(`📱 测试产品: ${nonExistentProduct}`);
    
    const result = await getProductParamsByName(nonExistentProduct);
    
    if (!result.success) {
      console.log('✅ 正确处理了不存在的产品');
      console.log('错误信息:', result.error);
    } else {
      console.log('❌ 意外找到了不存在的产品');
    }
    
  } catch (error) {
    console.error('❌ 测试不存在产品时发生错误:', error);
  }
};

// 主函数
const main = async () => {
  try {
    // 连接数据库
    await connectDB();
    
    // 执行测试
    await testProductParams();
    await testNonExistentProduct();
    
  } catch (error) {
    console.error('❌ 测试执行失败:', error);
  } finally {
    // 关闭数据库连接
    await mongoose.connection.close();
    console.log('\n🔌 数据库连接已关闭');
    process.exit(0);
  }
};

// 启动测试
if (require.main === module) {
  main();
}

module.exports = {
  testProductParams,
  testNonExistentProduct
};
