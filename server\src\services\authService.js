const User = require('../models/User');
const { generateToken } = require('../utils/jwt');
const { generateRandomCode } = require('../utils/random');
const smsService = require('./smsService');
const wechatAPI = require('../utils/wechat');

/**
 * 发送验证码
 * @param {String} phone 手机号
 * @param {String} purpose 验证码类型 login|register|auth
 * @returns {Object} 验证码信息
 */
exports.sendVerifyCode = async (phone, purpose) => {
  try {
    // 发送验证码
    const result = await smsService.sendVerificationCode(
      phone,
      purpose,
      {},
      'server'
    );
    
    return {
      phone: result.account,
      expiresIn: result.expiresIn
    };
  } catch (error) {
    console.error("验证码发送错误:", error);
    throw { 
      statusCode: error.statusCode || 500, 
      message: error.message || '发送验证码失败，请稍后再试' 
    };
  }
};

/**
 * 手机号一键认证（自动登录或注册）
 * @param {Object} authData 认证数据 {phone, verifyCode}
 * @returns {Object} 认证结果
 */
exports.phoneAuth = async (authData) => {
  try {
    const { phone, verifyCode } = authData;

    // 验证短信验证码
    const verifyResult = await smsService.verifyCode(phone, verifyCode, 'auth');
    if (!verifyResult.success) {
      throw { statusCode: 400, message: verifyResult.message };
    }

    // 查找用户是否存在
    let user = await User.findOne({ phone }).select('+refreshToken');

    let isNewUser = false;

    // 如果用户不存在，直接创建新用户
    if (!user) {
      console.log('创建新用户:', phone);
      
      // 生成用户昵称：选选用户 + 手机号后4位
      const phoneSuffix = phone.slice(-4);
      const nickname = `选选用户${phoneSuffix}`;

      user = await User.create({
        nickname: nickname,
        phone: phone,
        avatar: '/assets/images/default-avatar.png',
        gender: 'secret',
        age: 18,
        occupation: '',
        region: '',
        lastLoginAt: new Date()
      });

      isNewUser = true;
      console.log('新用户创建成功:', user._id);
    } else {
      // 用户存在，检查账号状态
      if (!user.isActive) {
        throw { statusCode: 403, message: '您的账号已被禁用' };
      }

      // 更新最后登录时间
      await User.findByIdAndUpdate(user._id, {
        lastLoginAt: new Date()
      });

      console.log('用户登录成功:', user._id);
    }

    // 生成JWT令牌
    const token = generateToken(user._id);
    
    // 更新用户的refreshToken
    await User.findByIdAndUpdate(user._id, { refreshToken: token.refreshToken });

    // 准备返回的用户数据
    const userData = {
      id: user._id,
      nickname: user.nickname,
      avatar: user.avatar,
      phone: phone,
      gender: user.gender,
      age: user.age,
      occupation: user.occupation,
      region: user.region,
      lastLoginAt: new Date()
    };

    return { 
      user: userData, 
      token,
      isNewUser
    };

  } catch (error) {
    console.error("手机号认证错误:", error);
    
    if (error.statusCode) {
      throw error;
    }
    
    throw { 
      statusCode: 500, 
      message: '认证失败，请稍后再试',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    };
  }
};

/**
 * 微信小程序登录
 * @param {String} code 微信授权码
 * @returns {Object} 包含用户和令牌的对象
 */
exports.wxLogin = async (code) => {
  try {
    console.log('开始处理微信登录');

    // 通过code获取openid
    const wxData = await wechatAPI.code2Session(code);
    const { openid, unionid } = wxData;

    console.log('获取到微信openid:', openid.substring(0, 8) + '...');

    // 查找用户是否存在
    let user = await User.findOne({ openId: openid }).select('+refreshToken');

    let isNewUser = false;

    // 如果用户不存在，创建新用户
    if (!user) {
      console.log('创建微信新用户:', openid.substring(0, 8) + '...');
      
      // 生成用户昵称：选选用户 + openid后4位
      const openidSuffix = openid.slice(-4);
      const nickname = `选选用户${openidSuffix}`;

      user = await User.create({
        nickname: nickname,
        openId: openid,
        unionId: unionid,
        avatar: '/assets/images/default-avatar.png',
        gender: 'secret',
        age: 18,
        occupation: '',
        region: '',
        lastLoginAt: new Date()
      });

      isNewUser = true;
      console.log('微信新用户创建成功:', user._id);
    } else {
      // 用户存在，检查账号状态
      if (!user.isActive) {
        throw { statusCode: 403, message: '您的账号已被禁用' };
      }

      // 更新unionid（如果有变化）
      if (unionid && user.unionId !== unionid) {
        await User.findByIdAndUpdate(user._id, { unionId: unionid });
      }

      // 更新最后登录时间
      await User.findByIdAndUpdate(user._id, {
        lastLoginAt: new Date()
      });

      console.log('微信用户登录成功:', user._id);
    }

    // 生成JWT令牌
    const token = generateToken(user._id);
    
    // 更新用户的refreshToken
    await User.findByIdAndUpdate(user._id, { refreshToken: token.refreshToken });

    // 准备返回的用户数据
    const userData = {
      id: user._id,
      nickname: user.nickname,
      avatar: user.avatar,
      phone: user.phone || '', // 微信用户可能没有手机号
      gender: user.gender,
      age: user.age,
      occupation: user.occupation,
      region: user.region,
      lastLoginAt: new Date()
    };

    return { 
      user: userData, 
      token,
      isNewUser
    };

  } catch (error) {
    console.error("微信登录错误:", error);
    
    if (error.statusCode) {
      throw error;
    }
    
    // 如果是微信API错误，保持原始错误信息
    if (error.message && (
      error.message.includes('无效的code') || 
      error.message.includes('微信') ||
      error.message.includes('API调用过于频繁')
    )) {
      throw { 
        statusCode: 400, 
        message: error.message
      };
    }
    
    throw { 
      statusCode: 500, 
      message: '微信登录失败，请稍后再试',
      details: process.env.NODE_ENV === 'development' ? error.message : undefined
    };
  }
};

/**
 * 刷新访问令牌
 * @param {String} refreshToken 刷新令牌
 * @returns {Object} 新的令牌对象
 */
exports.refreshToken = async (refreshToken) => {
  // 查找拥有该刷新令牌的用户
  const user = await User.findOne({ refreshToken }).select('_id');
  
  if (!user) {
    throw { statusCode: 401, message: '刷新令牌无效或已过期，请重新登录' };
  }

  // 生成新的令牌
  const token = generateToken(user._id);
  
  // 更新用户的refreshToken
  await User.findByIdAndUpdate(user._id, { refreshToken: token.refreshToken });

  return token;
};

/**
 * 退出登录
 * @param {String} userId 用户ID
 * @returns {Boolean} 是否成功
 */
exports.logout = async (userId) => {
  // 清除用户的refreshToken
  await User.findByIdAndUpdate(userId, { $unset: { refreshToken: 1 } });
  
  return true;
};

/**
 * 获取当前用户信息
 * @param {String} userId 用户ID
 * @returns {Object} 用户信息
 */
exports.getCurrentUser = async (userId) => {
  const user = await User.findById(userId);
  
  if (!user) {
    throw { statusCode: 404, message: '用户不存在' };
  }

  // 手机号打码处理
  let maskedPhone = '';
  if (user.phone) {
    maskedPhone = user.phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2');
  }

  // 准备返回的用户数据
  return {
    id: user._id,
    nickname: user.nickname,
    avatar: user.avatar,
    phone: maskedPhone,
    gender: user.gender,
    age: user.age,
    occupation: user.occupation,
    region: user.region,
    lastLoginAt: user.lastLoginAt,
    createdAt: user.createdAt
  };
};