#!/bin/bash

# 选选项目 PM2 管理脚本
# 使用方法: ./pm2-scripts.sh [command]

case "$1" in
  start)
    echo "🚀 启动选选服务器..."
    pm2 start ecosystem.config.js --env production
    ;;
  stop)
    echo "⏹️  停止选选服务器..."
    pm2 stop xuanxuan-server
    ;;
  restart)
    echo "🔄 重启选选服务器..."
    pm2 restart xuanxuan-server
    ;;
  reload)
    echo "🔄 重新加载选选服务器（零停机）..."
    pm2 reload xuanxuan-server
    ;;
  delete)
    echo "🗑️  删除选选服务器进程..."
    pm2 delete xuanxuan-server
    ;;
  status)
    echo "📊 查看选选服务器状态..."
    pm2 status xuanxuan-server
    ;;
  logs)
    echo "📋 查看选选服务器日志..."
    pm2 logs xuanxuan-server
    ;;
  monit)
    echo "📊 打开PM2监控面板..."
    pm2 monit
    ;;
  setup)
    echo "🔧 初始化环境..."
    # 创建必要的目录
    mkdir -p logs pids
    echo "✅ 目录创建完成"
    
    # 安装依赖
    echo "📦 安装项目依赖..."
    npm install --production
    echo "✅ 依赖安装完成"
    
    # 启动应用
    pm2 start ecosystem.config.js --env production
    echo "✅ 应用启动完成"
    ;;
  *)
    echo "使用方法: $0 {start|stop|restart|reload|delete|status|logs|monit|setup}"
    echo ""
    echo "命令说明:"
    echo "  start   - 启动应用"
    echo "  stop    - 停止应用"
    echo "  restart - 重启应用"
    echo "  reload  - 重新加载应用（零停机）"
    echo "  delete  - 删除应用进程"
    echo "  status  - 查看应用状态"
    echo "  logs    - 查看应用日志"
    echo "  monit   - 打开监控面板"
    echo "  setup   - 初始化环境并启动"
    exit 1
esac 