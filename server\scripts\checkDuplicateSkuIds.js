#!/usr/bin/env node

/**
 * 检查 PHONES_JS 数据中重复的 skuId
 * 
 * 使用方法:
 * node scripts/checkDuplicateSkuIds.js
 * 
 * 功能:
 * - 扫描所有 JS 文件，提取 skuId
 * - 检测重复的 skuId
 * - 显示重复数据的详细信息
 */

const fs = require('fs');
const path = require('path');

class SkuIdDuplicateChecker {
  constructor() {
    this.dataRoot = path.resolve(__dirname, '../../data/LAPTOPS_JS');
    this.skuIdMap = new Map(); // skuId -> 文件信息
    this.duplicates = [];
    this.stats = {
      totalFiles: 0,
      validFiles: 0,
      errorFiles: 0,
      uniqueSkuIds: 0,
      duplicateSkuIds: 0
    };
  }

  /**
   * 递归扫描目录，找到所有 .js 文件
   */
  scanJsFiles(dirPath) {
    const files = [];
    
    try {
      const items = fs.readdirSync(dirPath);
      
      for (const item of items) {
        const itemPath = path.join(dirPath, item);
        const stat = fs.statSync(itemPath);
        
        if (stat.isDirectory()) {
          // 如果是目录，递归扫描
          const subFiles = this.scanJsFiles(itemPath);
          files.push(...subFiles);
        } else if (stat.isFile() && item.endsWith('.js')) {
          // 排除一些工具脚本文件
          const excludeFiles = [
            'brand_name_replacer.js',
            'changxiang_test.js',
            'convert_iphone_data - 副本.js',
            'update_brand_keywords.js'
          ];
          
          if (!excludeFiles.includes(item)) {
            files.push({
              filePath: itemPath,
              fileName: item,
              relativePath: path.relative(this.dataRoot, itemPath)
            });
          }
        }
      }
    } catch (error) {
      console.error(`❌ 扫描目录失败: ${dirPath}`, error.message);
    }
    
    return files;
  }

  /**
   * 加载单个 JS 文件并提取 skuId
   */
  loadSkuIdFromFile(fileInfo) {
    try {
      // 清除 require 缓存，确保重新加载
      delete require.cache[require.resolve(fileInfo.filePath)];
      
      const productData = require(fileInfo.filePath);
      
      // 检查数据格式
      if (!productData) {
        throw new Error('文件没有导出数据');
      }
      
      // 如果是数组，取第一个元素
      const data = Array.isArray(productData) ? productData[0] : productData;
      
      if (!data || typeof data !== 'object') {
        throw new Error('数据格式不正确');
      }
      
      if (!data.skuId) {
        throw new Error('缺少 skuId 字段');
      }
      
      return {
        skuId: data.skuId,
        skuName: data.skuName || '未知产品名称',
        brandName: data.brandName || '未知品牌',
        productType: data.productType || '未知类型'
      };
      
    } catch (error) {
      throw new Error(`加载文件失败: ${error.message}`);
    }
  }

  /**
   * 检查单个文件
   */
  checkFile(fileInfo) {
    try {
      this.stats.totalFiles++;
      
      // 加载产品数据
      const productInfo = this.loadSkuIdFromFile(fileInfo);
      
      // 检查是否已存在相同的 skuId
      if (this.skuIdMap.has(productInfo.skuId)) {
        // 发现重复
        const existingFile = this.skuIdMap.get(productInfo.skuId);
        
        this.duplicates.push({
          skuId: productInfo.skuId,
          skuName: productInfo.skuName,
          brandName: productInfo.brandName,
          firstFile: existingFile,
          duplicateFile: fileInfo
        });
        
        this.stats.duplicateSkuIds++;
        console.log(`🔍 发现重复 skuId: ${productInfo.skuId} (${productInfo.skuName})`);
        console.log(`   首次出现: ${existingFile.relativePath}`);
        console.log(`   重复文件: ${fileInfo.relativePath}\n`);
      } else {
        // 首次出现，记录到 Map 中
        this.skuIdMap.set(productInfo.skuId, fileInfo);
        this.stats.uniqueSkuIds++;
      }
      
      this.stats.validFiles++;
      
    } catch (error) {
      console.error(`❌ 处理文件失败: ${fileInfo.relativePath}`, error.message);
      this.stats.errorFiles++;
    }
  }

  /**
   * 检查所有文件
   */
  checkAll() {
    console.log('🔍 开始检查重复的 skuId...\n');
    console.log(`📂 数据目录: ${this.dataRoot}\n`);
    
    // 扫描所有 JS 文件
    console.log('📂 扫描文件...');
    const jsFiles = this.scanJsFiles(this.dataRoot);
    
    console.log(`📊 找到 ${jsFiles.length} 个 JS 文件\n`);
    
    if (jsFiles.length === 0) {
      console.log('❌ 没有找到任何 JS 文件');
      return;
    }
    
    // 检查每个文件
    console.log('🔄 开始检查文件...\n');
    
    for (let i = 0; i < jsFiles.length; i++) {
      const fileInfo = jsFiles[i];
      
      // 每处理 50 个文件显示一次进度
      if (i % 50 === 0) {
        console.log(`📈 进度: ${i + 1}/${jsFiles.length} (${Math.round((i + 1) / jsFiles.length * 100)}%)`);
      }
      
      this.checkFile(fileInfo);
    }
    
    this.printSummary();
  }

  /**
   * 打印检查结果统计
   */
  printSummary() {
    console.log('\n' + '='.repeat(60));
    console.log('📊 重复检查统计结果');
    console.log('='.repeat(60));
    console.log(`📁 总文件数: ${this.stats.totalFiles}`);
    console.log(`✅ 有效文件: ${this.stats.validFiles}`);
    console.log(`❌ 错误文件: ${this.stats.errorFiles}`);
    console.log(`🆔 唯一 skuId 数量: ${this.stats.uniqueSkuIds}`);
    console.log(`🔍 重复 skuId 数量: ${this.stats.duplicateSkuIds}`);
    console.log(`📊 重复率: ${this.stats.totalFiles > 0 ? ((this.stats.duplicateSkuIds / this.stats.totalFiles) * 100).toFixed(2) : 0}%`);
    
    if (this.duplicates.length > 0) {
      console.log('\n' + '='.repeat(60));
      console.log('🔍 重复 skuId 详细信息');
      console.log('='.repeat(60));
      
      // 按 skuId 分组显示
      const groupedDuplicates = new Map();
      this.duplicates.forEach(dup => {
        if (!groupedDuplicates.has(dup.skuId)) {
          groupedDuplicates.set(dup.skuId, []);
        }
        groupedDuplicates.get(dup.skuId).push(dup);
      });
      
      let index = 1;
      groupedDuplicates.forEach((dups, skuId) => {
        const firstDup = dups[0];
        console.log(`${index}. skuId: ${skuId}`);
        console.log(`   产品名称: ${firstDup.skuName}`);
        console.log(`   品牌: ${firstDup.brandName}`);
        console.log(`   首次出现: ${firstDup.firstFile.relativePath}`);
        
        dups.forEach((dup, dupIndex) => {
          console.log(`   重复文件 ${dupIndex + 1}: ${dup.duplicateFile.relativePath}`);
        });
        
        console.log(''); // 空行分隔
        index++;
      });
      
      console.log('💡 建议操作:');
      console.log('1. 检查重复文件的内容是否完全相同');
      console.log('2. 如果内容相同，可以删除重复的文件');
      console.log('3. 如果内容不同，需要修改其中一个的 skuId');
      console.log('4. 重新运行导入脚本');
      
    } else {
      console.log('\n✅ 没有发现重复的 skuId！');
      console.log('📈 预期导入结果: 1146 个文件 = 1146 条数据库记录');
    }
    
    console.log('\n🎉 检查完成!');
  }

  /**
   * 生成重复数据报告 (JSON 格式)
   */
  generateReport() {
    const reportPath = path.resolve(__dirname, 'duplicate_skuids_report.json');
    
    const report = {
      timestamp: new Date().toISOString(),
      dataRoot: this.dataRoot,
      stats: this.stats,
      duplicates: this.duplicates.map(dup => ({
        skuId: dup.skuId,
        skuName: dup.skuName,
        brandName: dup.brandName,
        firstFile: dup.firstFile.relativePath,
        duplicateFile: dup.duplicateFile.relativePath
      }))
    };
    
    try {
      fs.writeFileSync(reportPath, JSON.stringify(report, null, 2), 'utf8');
      console.log(`\n📄 详细报告已保存至: ${reportPath}`);
    } catch (error) {
      console.error(`❌ 保存报告失败: ${error.message}`);
    }
  }
}

// 命令行参数处理
async function main() {
  const args = process.argv.slice(2);
  const checker = new SkuIdDuplicateChecker();
  
  if (args.includes('--help') || args.includes('-h')) {
    console.log(`
使用方法: node scripts/checkDuplicateSkuIds.js [选项]

选项:
  --report, -r    生成详细的 JSON 报告文件
  --help, -h      显示帮助信息

示例:
  node scripts/checkDuplicateSkuIds.js           # 检查重复 skuId
  node scripts/checkDuplicateSkuIds.js --report  # 检查并生成报告
    `);
    return;
  }
  
  // 执行检查
  checker.checkAll();
  
  // 如果指定了生成报告
  if (args.includes('--report') || args.includes('-r')) {
    checker.generateReport();
  }
}

// 处理未捕获的异常
process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ 未处理的 Promise 拒绝:', reason);
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  console.error('❌ 未捕获的异常:', error);
  process.exit(1);
});

// 处理 Ctrl+C
process.on('SIGINT', () => {
  console.log('\n\n⚠️  收到中断信号，正在退出...');
  process.exit(0);
});

// 运行主程序
if (require.main === module) {
  main().catch(error => {
    console.error('❌ 程序执行失败:', error.message);
    process.exit(1);
  });
}

module.exports = SkuIdDuplicateChecker;
