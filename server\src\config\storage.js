// 确保环境变量正确加载
const path = require('path');
const dotenv = require('dotenv');
dotenv.config({ path: path.join(__dirname, '../../.env') });

const OSS = require('ali-oss');

/**
 * 存储配置管理
 * 统一管理本地存储和阿里云OSS存储配置
 */
class StorageConfig {
  constructor() {
    this._ossClient = null;
    this._initialized = false;
  }

  /**
   * 获取存储类型
   * @returns {string} 'local' | 'oss'
   */
  getStorageType() {
    return process.env.STORAGE_TYPE || 'local';
  }

  /**
   * 获取本地存储配置
   * @returns {object} 本地存储配置
   */
  getLocalConfig() {
    return {
      uploadDir: 'uploads',
      baseUrl: process.env.BASE_URL || 'http://localhost:3000'
    };
  }

  /**
   * 获取OSS配置
   * @returns {object} OSS配置
   */
  getOSSConfig() {
    return {
      region: process.env.ALIYUN_OSS_REGION || 'oss-cn-hangzhou',
      accessKeyId: process.env.ALIYUN_OSS_ACCESS_KEY_ID,
      accessKeySecret: process.env.ALIYUN_OSS_ACCESS_KEY_SECRET,
      bucket: process.env.ALIYUN_OSS_BUCKET,
      endpoint: process.env.ALIYUN_OSS_ENDPOINT,
      domain: process.env.ALIYUN_OSS_DOMAIN,
      folder: process.env.ALIYUN_OSS_FOLDER || 'xuanxuan-app'
    };
  }

  /**
   * 检查OSS是否已启用
   * @returns {boolean} OSS是否可用
   */
  isOSSEnabled() {
    const config = this.getOSSConfig();
    return !!(config.accessKeyId && config.accessKeySecret && config.bucket);
  }

  /**
   * 获取OSS客户端实例
   * @returns {OSS|null} OSS客户端或null
   */
  getOSSClient() {
    if (!this.isOSSEnabled()) {
      return null;
    }

    if (!this._ossClient || !this._initialized) {
      try {
        const config = this.getOSSConfig();
        console.log('🔧 正在初始化OSS客户端...');
        console.log('OSS配置:', {
          region: config.region,
          bucket: config.bucket,
          endpoint: config.endpoint,
          accessKeyId: config.accessKeyId ? '已设置' : '未设置',
          accessKeySecret: config.accessKeySecret ? '已设置' : '未设置'
        });

        this._ossClient = new OSS({
          region: config.region,
          accessKeyId: config.accessKeyId,
          accessKeySecret: config.accessKeySecret,
          bucket: config.bucket,
          endpoint: config.endpoint
        });

        this._initialized = true;
        console.log('✅ OSS客户端初始化成功');
      } catch (error) {
        console.error('❌ OSS客户端初始化失败:', error);
        this._ossClient = null;
      }
    }

    return this._ossClient;
  }

  /**
   * 获取文件类型和大小限制配置
   * @returns {object} 文件限制配置
   */
  getFileLimits() {
    return {
      image: {
        maxSize: 10 * 1024 * 1024, // 10MB
        allowedTypes: ['image/jpeg', 'image/png', 'image/gif', 'image/webp']
      },
      video: {
        maxSize: 500 * 1024 * 1024, // 500MB
        allowedTypes: ['video/mp4', 'video/quicktime', 'video/x-msvideo']
      },
      audio: {
        maxSize: 50 * 1024 * 1024, // 50MB
        allowedTypes: ['audio/mpeg', 'audio/wav', 'audio/aac']
      },
      document: {
        maxSize: 20 * 1024 * 1024, // 20MB
        allowedTypes: [
          'application/pdf',
          'application/msword',
          'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
          'application/vnd.ms-excel',
          'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
          'application/vnd.ms-powerpoint',
          'application/vnd.openxmlformats-officedocument.presentationml.presentation'
        ]
      }
    };
  }

  /**
   * 获取完整的配置信息
   * @returns {object} 完整配置
   */
  getFullConfig() {
    return {
      storageType: this.getStorageType(),
      local: this.getLocalConfig(),
      oss: {
        enabled: this.isOSSEnabled(),
        ...this.getOSSConfig()
      },
      limits: this.getFileLimits()
    };
  }

  /**
   * 重置OSS客户端（用于配置更新后重新初始化）
   */
  resetOSSClient() {
    this._ossClient = null;
    this._initialized = false;
    console.log('🔄 OSS客户端已重置');
  }
}

// 创建单例实例
const storageConfig = new StorageConfig();

module.exports = storageConfig; 