/**
 * 通知页面样式
 * Notifications Page Styles
 * 使用统一样式系统重构 - 精简版
 */

/* ==================== 页面布局 Page Layout ==================== */

/* 页面容器 */
.container {
  display: flex;
  flex-direction: column;
  min-height: 100vh;
  background-color: #f8f8f8;
}

/* ==================== 未登录状态 Not Logged In State ==================== */

/* 未登录状态 - 使用全局空状态样式 */
.not-logged-in {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 100rpx;
}

.not-logged-in .empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.not-logged-in .empty-icon image {
  width: 100%;
  height: 100%;
}

.not-logged-in .empty-text {
  font-size: 32rpx;
  color: #999999;
  margin-bottom: 40rpx;
}

/* 继承全局按钮样式 */
.not-logged-in .login-btn {
  width: 240rpx;
}

/* ==================== 页面头部 Page Header ==================== */

.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #f0f0f0;
}

.header .title {
  font-size: 34rpx;
  font-weight: 500;
  color: #333333;
}

.header .action {
  font-size: 28rpx;
  color: #3B7ADB;
  transition: opacity 0.3s ease;
}

.header .action:active {
  opacity: 0.8;
}

/* ==================== 通知列表 Notification List ==================== */

.notification-list {
  flex: 1;
  width: 100%;
}

/* 通知列表项 - 继承通用列表项样式 */
.notification-item {
  display: flex;
  align-items: flex-start;
  padding: 30rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #f2f2f2;
  position: relative;
  transition: background-color 0.3s ease;
}

.notification-item:active {
  background-color: #f8f9fa;
}

.notification-item.read {
  opacity: 0.8;
}

/* 通知图标 - 继承通用图标样式 */
.notification-icon {
  width: 80rpx;
  height: 80rpx;
  border-radius: 50%;
  margin-right: 20rpx;
  background-color: #f0f5ff;
  display: flex;
  justify-content: center;
  align-items: center;
  flex-shrink: 0;
}

/* 图标样式 - 使用统一的SVG图标 */
.notification-icon::before {
  content: '';
  display: block;
  width: 40rpx;
  height: 40rpx;
  background-size: contain;
  background-repeat: no-repeat;
  background-position: center;
}

/* 不同类型通知图标 */
.icon-vote::before {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%233B7ADB"><path d="M12 21.35l-1.45-1.32C5.4 15.36 2 12.28 2 8.5 2 5.42 4.42 3 7.5 3c1.74 0 3.41.81 4.5 2.09C13.09 3.81 14.76 3 16.5 3 19.58 3 22 5.42 22 8.5c0 3.78-3.4 6.86-8.55 11.54L12 21.35z"/></svg>');
}

.icon-comment::before {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%233B7ADB"><path d="M21 6h-2v9H6v2c0 .55.45 1 1 1h11l4 4V7c0-.55-.45-1-1-1zm-4 6V3c0-.55-.45-1-1-1H3c-.55 0-1 .45-1 1v14l4-4h10c.55 0 1-.45 1-1z"/></svg>');
}

.icon-reply::before {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%233B7ADB"><path d="M10 9V5l-7 7 7 7v-4.1c5 0 8.5 1.6 11 5.1-1-5-4-10-11-11z"/></svg>');
}

.icon-like::before {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%233B7ADB"><path d="M1 21h4V9H1v12zm22-11c0-1.1-.9-2-2-2h-6.31l.95-4.57.03-.32c0-.41-.17-.79-.44-1.06L14.17 1 7.59 7.59C7.22 7.95 7 8.45 7 9v10c0 1.1.9 2 2 2h9c.83 0 1.54-.5 1.84-1.22l3.02-7.05c.09-.23.14-.47.14-.73v-1.91l-.01-.01L23 10z"/></svg>');
}

.icon-compare::before {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%233B7ADB"><path d="M9 11H7l3-3 3 3h-2v4h4v-2l3 3-3 3v-2H9v-6zm0 4h6v4h2V9h-8v6z"/></svg>');
}

.icon-notification::before {
  background-image: url('data:image/svg+xml;utf8,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="%233B7ADB"><path d="M12 22c1.1 0 2-.9 2-2h-4c0 1.1.89 2 2 2zm6-6v-5c0-3.07-1.64-5.64-4.5-6.32V4c0-.83-.67-1.5-1.5-1.5s-1.5.67-1.5 1.5v.68C7.63 5.36 6 7.92 6 11v5l-2 2v1h16v-1l-2-2z"/></svg>');
}

/* 通知内容区域 */
.notification-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  justify-content: center;
  min-width: 0;
}

.notification-text {
  font-size: 28rpx;
  color: #333333;
  line-height: 1.5;
  margin-bottom: 10rpx;
  word-wrap: break-word;
  word-break: break-all;
}

.notification-time {
  font-size: 24rpx;
  color: #999999;
}

/* 回复按钮 */
.reply-btn {
  display: inline-block;
  font-size: 24rpx;
  color: #3B7ADB;
  padding: 6rpx 16rpx;
  background-color: #f0f5ff;
  border-radius: 20rpx;
  margin-top: 10rpx;
  align-self: flex-start;
  transition: all 0.3s ease;
}

.reply-btn:active {
  background-color: #e6efff;
  transform: scale(0.98);
}

/* 未读标记 */
.unread-dot {
  position: absolute;
  width: 16rpx;
  height: 16rpx;
  background-color: #ff4d4f;
  border-radius: 50%;
  top: 30rpx;
  right: 30rpx;
}

/* ==================== 加载状态 Loading States ==================== */
/* 使用全局加载样式，此处仅保留页面特有的样式 */

.loading-more {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 30rpx 0;
  color: #999999;
  font-size: 24rpx;
}

.loading-more .loading-spinner {
  margin-right: 10rpx;
}

/* ==================== 空状态 Empty States ==================== */
/* 使用全局空状态样式，此处仅保留页面特有的样式 */

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  margin-top: 100rpx;
}

.empty-state .empty-icon {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-state .empty-icon image {
  width: 100%;
  height: 100%;
}

.empty-state .empty-text {
  font-size: 32rpx;
  color: #999999;
  margin-bottom: 10rpx;
}

.empty-state .empty-desc {
  font-size: 26rpx;
  color: #999999;
}

/* ==================== 回复弹窗 Reply Modal ==================== */

.reply-modal {
  position: fixed;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  z-index: 999;
  display: flex;
  align-items: center;
  justify-content: center;
}

.reply-modal-mask {
  position: absolute;
  top: 0;
  right: 0;
  bottom: 0;
  left: 0;
  background-color: rgba(0, 0, 0, 0.5);
}

.reply-modal-content {
  position: relative;
  width: 85%;
  max-width: 600rpx;
  background-color: #ffffff;
  border-radius: 12rpx;
  overflow: hidden;
  z-index: 1000;
  box-shadow: 0 8rpx 32rpx rgba(0, 0, 0, 0.15);
}

.reply-modal-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f0f0f0;
  background-color: #ffffff;
}

.reply-modal-title {
  font-size: 32rpx;
  font-weight: 500;
  color: #333333;
}

.reply-modal-close {
  font-size: 40rpx;
  color: #999999;
  line-height: 1;
  transition: color 0.3s ease;
}

.reply-modal-close:active {
  color: #666666;
}

.reply-modal-body {
  padding: 30rpx;
  background-color: #ffffff;
}

/* 回复输入框 */
.reply-textarea {
  width: 100%;
  height: 200rpx;
  padding: 20rpx;
  border: 1rpx solid #e0e0e0;
  border-radius: 8rpx;
  font-size: 28rpx;
  box-sizing: border-box;
  margin-bottom: 20rpx;
  background-color: #f8f9fa;
  transition: border-color 0.3s ease, background-color 0.3s ease;
}

.reply-textarea:focus {
  border-color: #3B7ADB;
  background-color: #ffffff;
  outline: none;
}

/* 匿名选项 */
.anonymous-option {
  display: flex;
  align-items: center;
  margin-bottom: 30rpx;
  transition: opacity 0.3s ease;
}

.anonymous-option:active {
  opacity: 0.8;
}

.checkbox {
  width: 32rpx;
  height: 32rpx;
  border: 1rpx solid #d9d9d9;
  border-radius: 4rpx;
  margin-right: 10rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.checkbox.checked {
  background-color: #3B7ADB;
  border-color: #3B7ADB;
}

.checkbox-inner {
  width: 20rpx;
  height: 20rpx;
  background-color: #ffffff;
  border-radius: 2rpx;
}

.anonymous-text {
  font-size: 26rpx;
  color: #666666;
}

/* 按钮组 */
.button-group {
  display: flex;
  justify-content: flex-end;
  gap: 20rpx;
}

/* 按钮样式继承全局样式，此处仅定义特殊调整 */
.cancel-btn,
.submit-btn {
  min-width: 120rpx;
  height: 70rpx;
  line-height: 70rpx;
  font-size: 28rpx;
  border-radius: 35rpx;
  text-align: center;
  transition: all 0.3s ease;
  border: none;
}

.cancel-btn {
  color: #666666;
  background-color: #f5f5f5;
}

.cancel-btn:active {
  background-color: #e8e8e8;
  transform: scale(0.98);
}

.submit-btn {
  color: #ffffff;
  background-color: #3B7ADB;
}

.submit-btn:active {
  background-color: #2E63B8;
  transform: scale(0.98);
}

/* ==================== 响应式适配 Responsive ==================== */

@media screen and (max-width: 375px) {
  .notification-item {
    padding: 20rpx;
  }
  
  .notification-icon {
    width: 60rpx;
    height: 60rpx;
    margin-right: 15rpx;
  }
  
  .notification-icon::before {
    width: 30rpx;
    height: 30rpx;
  }
  
  .notification-text {
    font-size: 26rpx;
  }
  
  .reply-modal-content {
    width: 95%;
  }
} 