<!--pages/user/about/about.wxml-->
<view class="page">
  <view class="container-fluid py-30">
    
    <!-- 应用信息 -->
    <view class="card mb-30">
      <view class="card-body text-center">
        <view class="app-logo mb-30 mx-auto">
          <image src="/assets/images/logo.png" mode="aspectFit"></image>
        </view>
        <view class="font-size-large font-weight-bold text-dark mb-10">{{appInfo.name}}</view>
        <view class="font-size-small text-secondary mb-30">版本 {{appInfo.version}}</view>
        <view class="font-size-medium text-secondary" style="text-align: left;">{{appInfo.description}}</view>
      </view>
    </view>
    
    <!-- 功能特色 -->
    <view class="card mb-30">
      <view class="card-header">
        <view class="card-title">功能特色</view>
      </view>
      <view class="card-body">
        <view class="feature-item flex items-center mb-20" wx:for="{{appInfo.features}}" wx:key="index" wx:if="{{index < appInfo.features.length - 1}}">
          <view class="feature-icon mr-20">✓</view>
          <view class="feature-text font-size-medium text-dark">{{item}}</view>
        </view>
        <view class="feature-item flex items-center" wx:if="{{appInfo.features.length > 0}}">
          <view class="feature-icon mr-20">✓</view>
          <view class="feature-text font-size-medium text-dark">{{appInfo.features[appInfo.features.length - 1]}}</view>
        </view>
      </view>
    </view>
    
    <!-- 联系我们 -->
    <view class="card mb-30">
      <view class="card-header">
        <view class="card-title">联系我们</view>
      </view>
      <view class="card-body">
        <view class="list">
          <view class="list-item" 
                bindtap="copyContact" 
                data-type="邮箱" 
                data-content="{{contactInfo.email}}">
            <view class="list-item-content">
              <view class="list-item-title">邮箱</view>
              <view class="list-item-desc">{{contactInfo.email}}</view>
            </view>
            <view class="text-primary font-size-small">复制</view>
          </view>
          
          <view class="list-item" bindtap="callService">
            <view class="list-item-content">
              <view class="list-item-title">客服电话</view>
              <view class="list-item-desc">{{contactInfo.phone}}</view>
            </view>
            <view class="text-primary font-size-small">拨打</view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 团队信息 -->
    <view class="card mb-30">
      <view class="card-header">
        <view class="card-title">团队信息</view>
      </view>
      <view class="card-body">
        <view class="list">
          <view class="list-item">
            <view class="list-item-content">
              <view class="list-item-title">公司</view>
              <view class="list-item-desc">{{teamInfo.company}}</view>
            </view>
          </view>
          <view class="list-item">
            <view class="list-item-content">
              <view class="list-item-title">地址</view>
              <view class="list-item-desc">{{teamInfo.address}}</view>
            </view>
          </view>
        </view>
      </view>
    </view>
    
    <!-- 版权信息 -->
    <view class="text-center py-20">
      <view class="font-size-xs text-light mb-10">© 2024 {{teamInfo.company}}</view>
      <view class="font-size-xs text-light">专注于电子消费产品智能决策</view>
    </view>
    
  </view>
</view> 