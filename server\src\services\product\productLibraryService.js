const NewProduct = require('../../models/NewProduct');

/**
 * 根据产品类型和品牌查询产品列表
 * @param {Object} queryParams 查询参数
 * @param {string} queryParams.productType 产品类型 (如: phone, laptop, tablet)
 * @param {string} queryParams.brandName 品牌名称 (如: 华为, 小米, 苹果)
 * @param {string} queryParams.releaseYear 上市年份 (如: "2023", "2024", "2023年以前")
 * @param {number} queryParams.page 页码，默认为1
 * @param {number} queryParams.limit 每页数量，默认为20
 * @returns {Promise<Object>} 查询结果
 */
async function queryProducts(queryParams) {
  try {
    const { productType, brandName, releaseYear, page = 1, limit = 20 } = queryParams;
    
    // 构建查询条件
    const query = {};
    
    if (productType) {
      query.productType = productType;
    }
    
    if (brandName) {
      query.brandName = brandName;
    }
    
    // 计算跳过的记录数
    const skip = (page - 1) * limit;
    
    // 执行查询，只返回需要的字段（包含上市日期、国内发布日期和价格范围）
    const products = await NewProduct.find(query)
      .select('skuName imageUrl commonSpecs.基本信息.上市日期 commonSpecs.基本信息.上市时间 commonSpecs.基本信息.国内发布日期 commonSpecs.基本信息.国内发布时间 productType brandName priceRange')
      .lean(); // 使用 lean() 提高查询性能
    
    // 辅助函数：从日期字符串中提取年份
    function extractYearFromDate(dateStr) {
      if (!dateStr || dateStr === '未知') return null;
      
      // 处理中文格式的年份
      const yearMatch = dateStr.match(/(\d{4})年?/);
      if (yearMatch) {
        return parseInt(yearMatch[1]);
      }
      
      // 尝试直接解析日期
      const parsed = new Date(dateStr);
      if (!isNaN(parsed.getTime())) {
        return parsed.getFullYear();
      }
      
      return null;
    }
    
    // 根据上市年份筛选产品
    let filteredProducts = products;
    if (releaseYear) {
      filteredProducts = products.filter(product => {
        const releaseDate = getProductReleaseDate(product);
        const productYear = extractYearFromDate(releaseDate);
        
        if (!productYear) return false; // 无法提取年份的产品不包含
        
        if (releaseYear === '2023年以前') {
          return productYear < 2023;
        } else {
          // 假设是具体年份，如 "2023", "2024"
          const targetYear = parseInt(releaseYear);
          return productYear === targetYear;
        }
      });
    }
    
    // 获取总数用于分页
    const total = await NewProduct.countDocuments(query);
    
    // 辅助函数：获取产品的上市日期
    function getProductReleaseDate(product) {
      const basicInfo = product.commonSpecs?.基本信息;
      if (!basicInfo) return null;
      
      // 优先使用上市日期，然后是国内发布日期，最后是国内发布时间
      return basicInfo.上市日期 || basicInfo.上市时间 || basicInfo.国内发布日期 || basicInfo.国内发布时间 || null;
    }
    
    // 辅助函数：将日期字符串转换为可排序的日期对象
    function parseReleaseDate(dateStr) {
      if (!dateStr || dateStr === '未知') return new Date(0); // 未知日期排在最后
      
      // 处理不同的日期格式
      if (dateStr.includes('年') && dateStr.includes('月')) {
        // 格式如 "2024年9月10日"
        const match = dateStr.match(/(\d{4})年(\d{1,2})月(?:(\d{1,2})日)?/);
        if (match) {
          const year = parseInt(match[1]);
          const month = parseInt(match[2]) - 1; // JavaScript月份从0开始
          const day = match[3] ? parseInt(match[3]) : 1;
          return new Date(year, month, day);
        }
      }
      
      // 尝试直接解析日期
      const parsed = new Date(dateStr);
      return isNaN(parsed.getTime()) ? new Date(0) : parsed;
    }
    
    // 格式化返回数据
    const formattedProducts = filteredProducts.map(product => {
      const releaseDate = getProductReleaseDate(product);
      return {
        skuName: product.skuName,
        imageUrl: product.imageUrl,
        上市日期: releaseDate || '未知',
        productType: product.productType,
        brandName: product.brandName,
        priceRange: product.priceRange || { min: 0, max: 0 }, // 添加价格范围
        _sortDate: parseReleaseDate(releaseDate) // 用于排序的日期对象
      };
    });
    
    // 按照上市日期排序（最新的在前面）
    formattedProducts.sort((a, b) => b._sortDate.getTime() - a._sortDate.getTime());
    
    // 移除排序用的临时字段
    formattedProducts.forEach(product => delete product._sortDate);
    
    // 分页处理
    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + parseInt(limit);
    const paginatedProducts = formattedProducts.slice(startIndex, endIndex);
    
    return {
      success: true,
      data: {
        products: paginatedProducts,
        pagination: {
          page: parseInt(page),
          limit: parseInt(limit),
          total: formattedProducts.length,
          totalPages: Math.ceil(formattedProducts.length / limit),
          hasNextPage: page * limit < formattedProducts.length,
          hasPrevPage: page > 1
        }
      },
      message: `成功查询到 ${paginatedProducts.length} 个产品，按上市日期排序`
    };
    
  } catch (error) {
    console.error('产品库查询失败:', error);
    return {
      success: false,
      error: error.message,
      message: '产品库查询失败'
    };
  }
}

module.exports = {
  queryProducts
};
