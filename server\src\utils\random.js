/**
 * 生成指定长度的随机数字验证码
 * @param {Number} length 验证码长度
 * @returns {String} 生成的验证码
 */
exports.generateRandomCode = (length = 6) => {
  let code = '';
  for (let i = 0; i < length; i++) {
    code += Math.floor(Math.random() * 10);
  }
  return code;
};

/**
 * 生成指定范围内的随机整数
 * @param {Number} min 最小值(包含)
 * @param {Number} max 最大值(包含)
 * @returns {Number} 生成的随机整数
 */
exports.getRandomInt = (min, max) => {
  min = Math.ceil(min);
  max = Math.floor(max);
  return Math.floor(Math.random() * (max - min + 1)) + min;
}; 