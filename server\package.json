{"name": "pet-social-server", "version": "1.0.0", "description": "选选后端api", "main": "src/index.js", "scripts": {"start": "node src/index.js", "dev": "nodemon src/index.js", "test": "echo \"Error: no test specified\" && exit 1", "db:check": "node src/utils/databaseCleanup.js --check-health", "db:list-indexes": "node src/utils/databaseCleanup.js --list-indexes", "db:reset": "node src/utils/databaseCleanup.js --full-reset", "db:clear": "node src/utils/databaseCleanup.js --clear-data", "db:rebuild-indexes": "node src/utils/databaseCleanup.js --rebuild-indexes", "data:import": "node src/utils/importSampleData.js", "data:import-force": "npm run db:reset && npm run data:import", "phones:import": "node scripts/importPhonesData.js", "phones:test": "node scripts/testImport.js", "phones:query": "node scripts/queryNewProducts.js", "phones:clear": "node scripts/importPhonesData.js --clear"}, "dependencies": {"ali-oss": "^6.23.0", "axios": "^1.9.0", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "dotenv": "^16.3.1", "express": "^4.18.2", "express-mongo-sanitize": "^2.2.0", "express-rate-limit": "^7.1.4", "helmet": "^7.1.0", "jimp": "^0.22.12", "joi": "^17.11.0", "jsonwebtoken": "^9.0.2", "mongoose": "^8.0.1", "morgan": "^1.10.0", "multer": "^1.4.5-lts.1", "node-cron": "^4.1.0", "ws": "^8.16.0", "xss-clean": "^0.1.4"}, "devDependencies": {"nodemon": "^3.0.1"}, "engines": {"node": ">=18.0.0"}}