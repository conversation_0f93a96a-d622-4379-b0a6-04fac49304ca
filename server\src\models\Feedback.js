const mongoose = require('mongoose');

const FeedbackSchema = new mongoose.Schema(
  {
    // 基本信息
    userId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: [true, '用户ID不能为空']
    },
    content: {
      type: String,
      required: [true, '反馈内容不能为空'],
      trim: true,
      maxlength: [500, '反馈内容不能超过500个字符']
    },
    
    // 反馈类型
    type: {
      type: String,
      enum: ['bug', 'suggestion', 'question', 'other'],
      default: 'other'
    },
    
    // 状态
    status: {
      type: String,
      enum: ['pending', 'processing', 'resolved', 'rejected'],
      default: 'pending'
    },
    
    // 图片信息
    images: [{
      type: String
    }],
    
    // 设备信息
    deviceInfo: {
      type: Object,
      default: {}
    },
    
    // 管理员回复
    adminReply: {
      content: {
        type: String,
        default: ''
      },
      replyAt: {
        type: Date
      },
      adminId: {
        type: mongoose.Schema.Types.ObjectId,
        ref: 'User'
      }
    }
  },
  {
    timestamps: true
  }
);

// 索引设置
// 移除单独的 createdAt 索引，timestamps: true 已自动创建
// 保留复合索引，用于特定的查询场景
FeedbackSchema.index({ userId: 1, createdAt: -1 });
FeedbackSchema.index({ status: 1, createdAt: -1 });

module.exports = mongoose.model('Feedback', FeedbackSchema); 