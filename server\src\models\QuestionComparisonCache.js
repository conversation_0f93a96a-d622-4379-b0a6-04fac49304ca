const mongoose = require('mongoose');

/**
 * 问题产品对比缓存模型
 * 专门用于缓存问题选项的产品对比结果
 * 生命周期与问题绑定，问题删除时一并删除
 */
const QuestionComparisonCacheSchema = new mongoose.Schema(
  {
    // 关联的问题ID
    questionId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'Question',
      required: true,
      unique: true
    },
    
    // 参与对比的产品信息
    productInfo: {
      productNames: [{
        type: String,
        required: true
      }],
      productType: {
        type: String,
        required: true,
        enum: ['phone', 'laptop', 'tablet', 'headphones', 'smartwatch', 'other']
      }
    },
    
    // 缓存的对比数据
    cachedData: {
      // 对比表格数据
      comparisonTable: [{
        parameter: { type: String, required: true },
        category: { type: String, required: true },
        values: [{
          productId: { type: mongoose.Schema.Types.ObjectId, required: true },
          productName: { type: String, required: true },
          value: { type: String, required: true }
        }]
      }],
      
      // AI分析结果
      aiAnalysis: [{
        parameter: { type: String, required: true },
        analysis: { type: String, required: true }
      }],
      
      // 格式化的产品基本信息
      formattedProducts: [{
        id: { type: mongoose.Schema.Types.ObjectId, required: true },
        skuId: { type: String, required: true },
        name: { type: String, required: true },
        price: { type: Number },
        image: { type: String },
        brand: { type: String },
        productType: { type: String, required: true }
      }],
      
      // 未找到的产品列表
      notFoundProducts: [{
        type: String
      }]
    },
    
    // 缓存元数据
    cacheMetadata: {
      // 数据大小（字节）
      cacheSize: {
        type: Number,
        default: 0
      },
      
      // 缓存状态
      status: {
        type: String,
        enum: ['active', 'expired'],
        default: 'active'
      },
      
      // AI分析生成时间
      aiGeneratedAt: {
        type: Date,
        default: Date.now
      },
      
      // 访问次数统计
      accessCount: {
        type: Number,
        default: 1
      },
      
      // 最后访问时间
      lastAccessTime: {
        type: Date,
        default: Date.now
      }
    }
  },
  {
    timestamps: true
  }
);

// 索引优化
// 移除 questionId 单独索引，unique: true 已自动创建唯一索引
QuestionComparisonCacheSchema.index({ 'cacheMetadata.status': 1 }); // 状态索引
QuestionComparisonCacheSchema.index({ 'productInfo.productType': 1 }); // 产品类型索引

// 实例方法：更新访问统计
QuestionComparisonCacheSchema.methods.updateAccessStats = function() {
  this.cacheMetadata.accessCount += 1;
  this.cacheMetadata.lastAccessTime = new Date();
  return this.save();
};

// 实例方法：检查缓存是否有效
QuestionComparisonCacheSchema.methods.isValid = function() {
  return this.cacheMetadata.status === 'active';
};

// 静态方法：根据问题ID删除缓存
QuestionComparisonCacheSchema.statics.deleteByQuestionId = async function(questionId) {
  try {
    const result = await this.deleteOne({ questionId });
    console.log(`删除问题${questionId}的对比缓存，影响记录数: ${result.deletedCount}`);
    return result;
  } catch (error) {
    console.error(`删除问题${questionId}对比缓存失败:`, error);
    throw error;
  }
};

module.exports = mongoose.model('QuestionComparisonCache', QuestionComparisonCacheSchema); 