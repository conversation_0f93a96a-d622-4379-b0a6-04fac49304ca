const mongoose = require('mongoose');
const fs = require('fs');
const path = require('path');
require('dotenv').config();

// 导入 NewProduct 模型
const NewProduct = require('../src/models/NewProduct');

/**
 * 生成产品类型和品牌配置文件的脚本
 * 用于获取数据库中所有的 productType 以及对应的 brandName
 */
async function generateProductTypesBrands() {
  try {
    console.log('📊 开始分析数据库中的产品类型和品牌...');
    
    // 连接数据库
    console.log('🔗 连接数据库中...');
    await mongoose.connect(process.env.MONGODB_URI || 'mongodb://localhost:27017/xuanxuan_test');
    console.log('✅ 数据库连接成功');
    
    // 获取所有产品的 productType 和 brandName
    console.log('🔍 查询产品数据...');
    const products = await NewProduct.find({}, { productType: 1, brandName: 1, category: 1 });
    
    if (products.length === 0) {
      console.log('⚠️  数据库中没有找到产品数据');
      return;
    }
    
    console.log(`📈 找到 ${products.length} 个产品记录`);
    
    // 统计数据
    const productTypesMap = new Map();
    const categoriesMap = new Map();
    
    products.forEach(product => {
      const { productType, brandName, category } = product;
      
      // 统计 productType 和对应的品牌
      if (!productTypesMap.has(productType)) {
        productTypesMap.set(productType, new Set());
      }
      productTypesMap.get(productType).add(brandName);
      
      // 统计 category 和对应的品牌
      if (category) {
        if (!categoriesMap.has(category)) {
          categoriesMap.set(category, new Set());
        }
        categoriesMap.get(category).add(brandName);
      }
    });
    
    // 转换为对象格式
    const productTypesData = {};
    const categoriesData = {};
    
    // 处理 productType 数据
    for (const [productType, brandsSet] of productTypesMap) {
      const brands = Array.from(brandsSet).sort();
      productTypesData[productType] = {
        total: brands.length,
        brands: brands
      };
    }
    
    // 处理 category 数据
    for (const [category, brandsSet] of categoriesMap) {
      const brands = Array.from(brandsSet).sort();
      categoriesData[category] = {
        total: brands.length,
        brands: brands
      };
    }
    
    // 生成统计摘要
    const summary = {
      totalProducts: products.length,
      totalProductTypes: productTypesMap.size,
      totalCategories: categoriesMap.size,
      totalUniqueBrands: new Set(products.map(p => p.brandName)).size,
      generatedAt: new Date().toISOString()
    };
    
    // 生成最终的配置数据
    const configData = {
      summary,
      productTypes: productTypesData,
      categories: categoriesData,
      // 所有品牌的统一列表
      allBrands: Array.from(new Set(products.map(p => p.brandName))).sort()
    };
    
    // 确保配置目录存在
    const configDir = path.join(__dirname, '..', 'src', 'config');
    if (!fs.existsSync(configDir)) {
      fs.mkdirSync(configDir, { recursive: true });
    }
    
    // 保存为 JSON 文件
    const jsonFilePath = path.join(configDir, 'productTypesBrands.json');
    fs.writeFileSync(jsonFilePath, JSON.stringify(configData, null, 2), 'utf8');
    
    // 保存为 JS 模块文件
    const jsContent = `// 产品类型和品牌配置文件
// 自动生成于: ${new Date().toLocaleString('zh-CN')}
// 数据来源: NewProduct 数据库集合

module.exports = ${JSON.stringify(configData, null, 2)};
`;
    
    const jsFilePath = path.join(configDir, 'productTypesBrands.js');
    fs.writeFileSync(jsFilePath, jsContent, 'utf8');
    
    // 打印统计结果
    console.log('\n📊 数据统计结果:');
    console.log(`   总产品数量: ${summary.totalProducts}`);
    console.log(`   产品类型数量: ${summary.totalProductTypes}`);
    console.log(`   产品分类数量: ${summary.totalCategories}`);
    console.log(`   独特品牌数量: ${summary.totalUniqueBrands}`);
    
    console.log('\n📋 产品类型详情:');
    for (const [productType, data] of Object.entries(productTypesData)) {
      console.log(`   ${productType}: ${data.total} 个品牌 (${data.brands.slice(0, 3).join(', ')}${data.brands.length > 3 ? '...' : ''})`);
    }
    
    if (Object.keys(categoriesData).length > 0) {
      console.log('\n🏷️  产品分类详情:');
      for (const [category, data] of Object.entries(categoriesData)) {
        console.log(`   ${category}: ${data.total} 个品牌 (${data.brands.slice(0, 3).join(', ')}${data.brands.length > 3 ? '...' : ''})`);
      }
    }
    
    console.log('\n📁 文件生成成功:');
    console.log(`   JSON 配置文件: ${jsonFilePath}`);
    console.log(`   JS 模块文件: ${jsFilePath}`);
    
  } catch (error) {
    console.error('❌ 生成配置文件时发生错误:', error);
  } finally {
    // 关闭数据库连接
    if (mongoose.connection.readyState === 1) {
      await mongoose.connection.close();
      console.log('🔐 数据库连接已关闭');
    }
  }
}

// 如果直接运行此脚本
if (require.main === module) {
  generateProductTypesBrands()
    .then(() => {
      console.log('\n✅ 脚本执行完成');
      process.exit(0);
    })
    .catch((error) => {
      console.error('❌ 脚本执行失败:', error);
      process.exit(1);
    });
}

module.exports = generateProductTypesBrands;
