const commentService = require('../services/commentService');
const validator = require('../utils/communityvalidator');
const { success, error } = require('../utils/response');

/**
 * @desc    添加评论
 * @route   POST /api/v1/community/answers/:answerId/comments
 * @access  Private
 */
const createComment = async (req, res) => {
  try {
    // 验证请求数据
    const { error: validationError, value } = validator.createCommentSchema.validate(req.body);
    
    if (validationError) {
      const errors = validationError.details.map(detail => ({
        field: detail.path[0],
        message: detail.message
      }));
      
      return error(res, 400, '请求参数错误', errors[0].message);
    }

    const { answerId } = req.params;
    const userId = req.user.id;

    // 创建评论
    console.log('创建评论:', value, value.parentId);
    const comment = await commentService.createComment(
      answerId,
      value.content,
      value.isAnonymous,
      value.parentId,
      userId
    );

    return success(res, 201, '评论发表成功', {
      id: comment._id
    });
  } catch (err) {
    // 处理特定错误
    if (err.message === '回答不存在' || err.message === '问题不存在' || 
        err.message === '无效的回答ID' || err.message === '父评论不存在' ||
        err.message === '无效的父评论ID') {
      return error(res, 404, err.message);
    }
    if (err.message === '只有问题发布者和回答者可以参与评论' || 
        err.message === '回答者只能回复问题发布者的评论') {
      return error(res, 403, err.message);
    }
    
    // 处理内容审核错误
    if (err.message.includes('内容审核失败') || err.message === '评论内容不能为空') {
      return error(res, 400, err.message);
    }
    
    return error(res, 500, err.message);
  }
};

/**
 * @desc    获取评论列表
 * @route   GET /api/v1/community/answers/:answerId/comments
 * @access  Public (支持未登录用户)
 */
const getComments = async (req, res) => {
  try {
    // 验证请求数据
    const { error: validationError, value } = validator.getCommentsSchema.validate(req.query);
    
    if (validationError) {
      const errors = validationError.details.map(detail => ({
        field: detail.path[0],
        message: detail.message
      }));
      
      return error(res, 400, '请求参数错误', errors[0].message);
    }

    const { answerId } = req.params;

    // 获取评论列表
    const result = await commentService.getComments(
      answerId,
      value.page,
      value.limit
    );

    return success(res, 200, '获取评论列表成功', result);
  } catch (err) {
    // 处理特定错误
    if (err.message === '无效的回答ID') {
      return error(res, 404, err.message);
    }
    return error(res, 500, err.message);
  }
};

/**
 * @desc    删除评论
 * @route   DELETE /api/v1/community/comments/:id
 * @access  Private
 */
const deleteComment = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;

    // 删除评论
    await commentService.deleteComment(id, userId);
    return success(res, 200, '评论删除成功');
  } catch (err) {
    // 处理特定错误
    if (err.message === '评论不存在' || err.message === '无效的评论ID') {
      return error(res, 404, err.message);
    }
    if (err.message === '没有权限删除此评论') {
      return error(res, 403, err.message);
    }
    return error(res, 500, err.message);
  }
};

/**
 * @desc    获取评论的回复列表
 * @route   GET /api/v1/community/comments/:commentId/replies
 * @access  Public (支持未登录用户)
 */
const getReplies = async (req, res) => {
  try {
    // 验证请求数据
    const { error: validationError, value } = validator.getRepliesSchema.validate(req.query);
    
    if (validationError) {
      const errors = validationError.details.map(detail => ({
        field: detail.path[0],
        message: detail.message
      }));
      
      return error(res, 400, '请求参数错误', errors[0].message);
    }

    const { commentId } = req.params;
    const { isTopLevel } = value; // 是否获取顶级评论的所有回复

    // 获取回复列表
    const result = await commentService.getReplies(
      commentId,
      value.page,
      value.limit,
      isTopLevel
    );

    return success(res, 200, '获取回复列表成功', result);
  } catch (err) {
    // 处理特定错误
    if (err.message === '评论不存在' || err.message === '无效的评论ID') {
      return error(res, 404, err.message);
    }
    return error(res, 500, err.message);
  }
};

/**
 * @desc    获取评论信息
 * @route   GET /api/v1/community/comments/:id
 * @access  Private
 */
const getCommentInfo = async (req, res) => {
  try {
    const { id } = req.params;
    
    // 使用服务获取评论信息
    const commentInfo = await commentService.getCommentInfo(id);
    
    return success(res, 200, '获取评论信息成功', commentInfo);
  } catch (err) {
    // 处理特定错误
    if (err.message === '评论不存在' || err.message === '无效的评论ID') {
      return error(res, 404, err.message);
    }
    return error(res, 500, err.message);
  }
};

module.exports = {
  createComment,
  getComments,
  deleteComment,
  getReplies,
  getCommentInfo
}; 