/**
 * 选选小程序 - 组件样式文件
 * Components Styles for XuanXuan Mini Program
 * 依赖：variables.wxss, base.wxss
 */

/* ==================== 按钮组件 Button Components ==================== */

/* 基础按钮 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: auto;
  min-width: 160rpx;
  height: 72rpx;
  border-radius: 36rpx;
  font-size: 30rpx;
  font-weight: normal;
  text-align: center;
  padding: 0 40rpx;
  transition: all 0.3s ease;
  border: none;
  outline: none;
  box-sizing: border-box;
  box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.1);
}

.btn::after {
  border: none;
}

/* 主要按钮 */
.btn-primary {
  background: linear-gradient(135deg, #3B7ADB 0%, #5A67D8 100%);
  color: #ffffff;
  box-shadow: 0 4rpx 12rpx rgba(59, 122, 219, 0.25);
}

.btn-primary:active {
  background: linear-gradient(135deg, #2E63B8 0%, #4C51BF 100%);
  transform: scale(0.98);
  box-shadow: 0 2rpx 6rpx rgba(59, 122, 219, 0.3);
}

.btn-primary[disabled],
.btn-primary.disabled {
  background: #cccccc !important;
  color: #ffffff !important;
  pointer-events: none;
  transform: none !important;
  box-shadow: none !important;
}

/* 次要按钮 */
.btn-secondary {
  background-color: #f0f4ff;
  color: #3B7ADB;
  border: 2rpx solid #3B7ADB;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.08);
}

.btn-secondary:active {
  background-color: #e6efff;
  transform: scale(0.98);
  box-shadow: 0 1rpx 3rpx rgba(0, 0, 0, 0.1);
}

/* 危险按钮 */
.btn-danger {
  background-color: #ff5151;
  color: #ffffff;
}

.btn-danger:active {
  background-color: #e04545;
  transform: scale(0.98);
}

/* 禁用按钮 */
.btn[disabled],
.btn.disabled {
  background-color: #cccccc !important;
  color: #ffffff !important;
  pointer-events: none;
  transform: none !important;
}

/* 按钮尺寸 */
.btn-small {
  height: 64rpx;
  font-size: 24rpx;
  border-radius: 32rpx;
}

.btn-medium {
  height: 80rpx;
  font-size: 30rpx;
  border-radius: 40rpx;
}

.btn-large {
  height: 96rpx;
  font-size: 36rpx;
  border-radius: 48rpx;
}

/* 全宽按钮 */
.btn-full {
  width: 100%;
  min-width: auto;
}

/* 圆形按钮 */
.btn-circle {
  width: 88rpx;
  height: 88rpx;
  border-radius: 50%;
}

/* 微信登录按钮 */
.btn-wechat {
  background-color: #07C160;
  color: #ffffff;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 10rpx;
}

.btn-wechat:active {
  background-color: #06AD56;
  transform: scale(0.98);
}

.btn-wechat .icon {
  width: 36rpx;
  height: 36rpx;
}

/* ==================== 表单组件 Form Components ==================== */

/* 表单组 */
.form-group {
  width: 100%;
  margin-bottom: 30rpx;
}

/* 表单标签 */
.form-label {
  display: block;
  font-size: 28rpx;
  color: #333333;
  margin-bottom: 10rpx;
  font-weight: normal;
}

.form-label.required::after {
  content: '*';
  color: #ff5151;
  margin-left: 4rpx;
}

/* 表单输入框包装器 */
.form-input-wrap {
  position: relative;
  width: 100%;
  background-color: #f8f9fa;
  border: 2rpx solid #d9d9d9;
  border-radius: 8rpx;
  transition: all 0.3s ease;
  box-sizing: border-box;
}

.form-input-wrap:focus-within {
  background-color: #ffffff;
  border-color: #3B7ADB;
  box-shadow: 0 0 0 4rpx rgba(59, 122, 219, 0.1);
}

/* 表单输入框 */
.form-input {
  width: 100%;
  height: 72rpx;
  background-color: transparent;
  border: none;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333333;
  box-sizing: border-box;
  transition: none;
}

.form-input:focus {
  border: none;
  outline: none;
}

.form-input.error {
  border-color: #ff5151;
}

/* 独立输入框样式（向后兼容） */
.form-input.standalone {
  background-color: #f8f9fa;
  border: 2rpx solid #d9d9d9;
  transition: all 0.3s ease;
}

.form-input.standalone:focus {
  background-color: #ffffff;
  border-color: #3B7ADB;
  box-shadow: 0 0 0 4rpx rgba(59, 122, 219, 0.1);
}

/* 表单文本域 */
.form-textarea {
  width: 100%;
  min-height: 160rpx;
  background-color: #ffffff;
  border: 2rpx solid #d0d0d0;
  border-radius: 8rpx;
  padding: 20rpx;
  font-size: 28rpx;
  color: #333333;
  box-sizing: border-box;
  resize: none;
  transition: border-color 0.3s ease;
}

.form-textarea:focus {
  border-color: #3B7ADB;
}

/* 表单选择器 */
.form-picker {
  width: 100%;
  height: 72rpx;
  background-color: #ffffff;
  border: 2rpx solid #d0d0d0;
  border-radius: 8rpx;
  padding: 0 20rpx;
  font-size: 28rpx;
  color: #333333;
  display: flex;
  align-items: center;
  justify-content: space-between;
  box-sizing: border-box;
}

.form-picker.placeholder {
  color: #999999;
}

/* 表单错误提示 */
.form-error {
  font-size: 24rpx;
  color: #ff5151;
  margin-top: 8rpx;
  line-height: 1.4;
}

/* 验证码输入组合 */
.code-input-group {
  display: flex;
  align-items: center;
  gap: 20rpx;
}

.code-input-group .form-input {
  flex: 1;
}

.code-btn {
  width: auto;
  min-width: 140rpx;
  height: 72rpx;
  background-color: #f0f4ff;
  color: #3B7ADB;
  border: 2rpx solid #3B7ADB;
  border-radius: 36rpx;
  font-size: 24rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 20rpx;
  transition: all 0.3s ease;
  white-space: nowrap;
  box-shadow: 0 2rpx 6rpx rgba(0, 0, 0, 0.08);
}

.code-btn:active {
  background-color: #e6efff;
  transform: scale(0.98);
}

.code-btn.disabled {
  color: #888888;
  border-color: #cccccc;
  background-color: #f0f0f0;
  pointer-events: none;
}

/* ==================== 卡片组件 Card Components ==================== */

/* 基础卡片 */
.card {
  background-color: #ffffff;
  border-radius: 12rpx;
  box-shadow: 0 2rpx 10rpx rgba(0, 0, 0, 0.05);
  overflow: hidden;
  margin-bottom: 20rpx;
}

/* 卡片头部 */
.card-header {
  padding: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
}

.card-title {
  font-size: 32rpx;
  font-weight: bold;
  color: #333333;
  margin: 0;
}

/* 卡片内容 */
.card-body {
  padding: 30rpx;
}

/* 卡片底部 */
.card-footer {
  padding: 20rpx 30rpx;
  border-top: 1rpx solid #f5f5f5;
  background-color: #fafafa;
}

/* ==================== 列表组件 List Components ==================== */

/* 基础列表 */
.list {
  background-color: #ffffff;
  border-radius: 12rpx;
  overflow: hidden;
}

/* 列表项 */
.list-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  border-bottom: 1rpx solid #f5f5f5;
  min-height: 88rpx;
  box-sizing: border-box;
  transition: background-color 0.2s ease;
}

.list-item:last-child {
  border-bottom: none;
}

.list-item:active {
  background-color: #f8f9fa;
}

/* 列表项内容 */
.list-item-content {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.list-item-title {
  font-size: 30rpx;
  color: #333333;
  margin-bottom: 8rpx;
}

.list-item-desc {
  font-size: 24rpx;
  color: #666666;
}

/* 列表项图标 */
.list-item-icon {
  width: 40rpx;
  height: 40rpx;
  margin-right: 20rpx;
}

/* 列表项箭头 */
.list-item-arrow {
  width: 32rpx;
  height: 32rpx;
  margin-left: 20rpx;
  color: #cccccc;
}

/* ==================== 弹窗组件 Modal Components ==================== */

/* 弹窗遮罩 */
.modal-mask {
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.6);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

/* 弹窗容器 */
.modal-container {
  width: 580rpx;
  max-width: 90%;
  background-color: #ffffff;
  border-radius: 20rpx;
  overflow: hidden;
  animation: modalShow 0.3s ease-out;
}

/* 确认弹窗容器 */
.modal-confirm {
  width: 520rpx;
  max-width: 90%;
  background-color: #ffffff;
  border-radius: 20rpx;
  overflow: hidden;
  animation: modalShow 0.3s ease-out;
}

@keyframes modalShow {
  from {
    opacity: 0;
    transform: scale(0.8) translateY(-100rpx);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

/* 弹窗头部 */
.modal-header {
  padding: 50rpx 40rpx 30rpx;
  text-align: center;
  border-bottom: 1rpx solid #f0f0f0;
  position: relative;
}

.modal-title {
  font-size: 36rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 10rpx;
}

.modal-subtitle {
  font-size: 28rpx;
  color: #666666;
}

/* 弹窗关闭按钮 */
.modal-close {
  position: absolute;
  top: 30rpx;
  right: 30rpx;
  width: 50rpx;
  height: 50rpx;
  background: #e8e8e8;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #666666;
  font-size: 32rpx;
  font-weight: bold;
  transition: all 0.2s ease;
  line-height: 1;
}

.modal-close text {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

.modal-close:active {
  background: #d0d0d0;
  transform: scale(0.95);
}

/* 弹窗内容 */
.modal-content {
  padding: 40rpx;
}

/* 弹窗按钮组 */
.modal-buttons {
  margin-top: 40rpx;
  display: flex;
  justify-content: center;
}

.modal-buttons-flex {
  display: flex;
  gap: 20rpx;
}

.modal-buttons-flex .btn {
  flex: 1;
}

/* 弹窗提示框 */
.modal-tip {
  margin-top: 30rpx;
  padding: 20rpx;
  background: #f0f4ff;
  border-radius: 12rpx;
  border-left: 4rpx solid #3B7ADB;
  border: 1rpx solid #e1e8ff;
}

.modal-tip-text {
  font-size: 24rpx;
  color: #555555;
  line-height: 1.4;
}

/* ==================== Logo展示组件 Logo Display ==================== */

.logo-display {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  margin-bottom: 80rpx;
}

.logo-display .logo-image {
  width: 160rpx;
  height: 160rpx;
  margin-bottom: 20rpx;
  border-radius: 20rpx;
}

.logo-display .logo-title {
  font-size: 48rpx;
  font-weight: bold;
  color: #333333;
  margin-bottom: 10rpx;
}

.logo-display .logo-subtitle {
  font-size: 28rpx;
  color: #999999;
}

/* ==================== 头像组件 Avatar Components ==================== */

.avatar {
  border-radius: 50%;
  overflow: hidden;
  background-color: #f5f5f5;
  display: flex;
  align-items: center;
  justify-content: center;
}

.avatar image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

/* ==================== 状态组件 Status Components ==================== */

/* 加载旋转器 */
.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 2rpx solid #f3f3f3;
  border-top: 2rpx solid #3B7ADB;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-spinner.small {
  width: 32rpx;
  height: 32rpx;
}

.loading-spinner.large {
  width: 60rpx;
  height: 60rpx;
  border-width: 3rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* 加载状态容器 */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60rpx 30rpx;
}

.loading-container.inline {
  flex-direction: row;
  padding: 30rpx;
}

.loading-container.small {
  padding: 30rpx 20rpx;
}

.loading-text {
  font-size: 28rpx;
  color: #666666;
  margin-top: 20rpx;
}

.loading-container.inline .loading-text {
  margin-top: 0;
  margin-left: 20rpx;
}

/* 空状态容器 */
.empty-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 120rpx 30rpx;
}

.empty-container.small {
  padding: 80rpx 30rpx;
}

.empty-icon {
  width: 120rpx;
  height: 120rpx;
  margin-bottom: 30rpx;
  opacity: 0.3;
}

.empty-icon.large {
  width: 200rpx;
  height: 200rpx;
}

.empty-icon image {
  width: 100%;
  height: 100%;
}

.empty-text {
  font-size: 28rpx;
  color: #999999;
  text-align: center;
  margin-bottom: 10rpx;
}

.empty-desc {
  font-size: 24rpx;
  color: #cccccc;
  text-align: center;
}

/* 没有更多数据提示 */
.no-more {
  text-align: center;
  padding: 30rpx 0;
  color: #999999;
  font-size: 24rpx;
}

.no-more.small {
  padding: 20rpx 0;
  font-size: 22rpx;
}

/* ==================== 徽章组件 Badge Components ==================== */

.badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 32rpx;
  height: 32rpx;
  padding: 0 8rpx;
  background-color: #ff5151;
  color: #ffffff;
  font-size: 20rpx;
  line-height: 1;
  border-radius: 16rpx;
  box-sizing: border-box;
}

.badge-dot {
  width: 16rpx;
  height: 16rpx;
  min-width: 16rpx;
  padding: 0;
  border-radius: 50%;
}

/* 状态徽章 */
.badge-success {
  background-color: #52c41a;
}

.badge-warning {
  background-color: #faad14;
}

.badge-danger {
  background-color: #ff4d4f;
}

.badge-info {
  background-color: #1890ff;
}

/* ==================== 列表组件 List Components ==================== */

/* 通用列表项 */
.list-item {
  display: flex;
  align-items: center;
  padding: 30rpx;
  background-color: #ffffff;
  border-bottom: 1rpx solid #f2f2f2;
  transition: background-color 0.3s ease;
}

.list-item:active {
  background-color: #f8f9fa;
}

.list-item:last-child {
  border-bottom: none;
}

.list-item-icon {
  width: 80rpx;
  height: 80rpx;
  margin-right: 20rpx;
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background-color: #f0f5ff;
}

.list-item-content {
  flex: 1;
  min-width: 0;
}

.list-item-title {
  font-size: 28rpx;
  color: #333333;
  line-height: 1.5;
  margin-bottom: 8rpx;
  word-wrap: break-word;
}

.list-item-desc {
  font-size: 24rpx;
  color: #999999;
  line-height: 1.4;
}

.list-item-arrow {
  width: 24rpx;
  height: 24rpx;
  margin-left: 20rpx;
  opacity: 0.3;
}

/* ==================== 反馈状态组件 Feedback Components ==================== */

/* 错误状态 */
.error-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 30rpx;
}

.error-icon {
  width: 100rpx;
  height: 100rpx;
  margin-bottom: 30rpx;
  color: #ff4d4f;
  font-size: 100rpx;
}

.error-text {
  font-size: 28rpx;
  color: #333333;
  text-align: center;
  margin-bottom: 10rpx;
}

.error-desc {
  font-size: 24rpx;
  color: #999999;
  text-align: center;
  margin-bottom: 40rpx;
}

/* 成功状态 */
.success-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 30rpx;
}

.success-icon {
  width: 100rpx;
  height: 100rpx;
  margin-bottom: 30rpx;
  color: #52c41a;
  font-size: 100rpx;
}

/* ==================== 删除按钮组件 Delete Button Component ==================== */

/* 统一的删除按钮 - 红色圆形背景 + 白色叉号 */
.delete-btn {
  width: 48rpx;
  height: 48rpx;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, #f87171 0%, #ef4444 100%);
  border-radius: 50%;
  color: #ffffff;
  font-size: 24rpx;
  transition: all 0.3s ease;
  box-shadow: 0 2rpx 8rpx rgba(248, 113, 113, 0.3);
  position: relative;
  overflow: hidden;
}

/* 删除按钮激活状态 */
.delete-btn:active {
  background: linear-gradient(135deg, #dc2626 0%, #b91c1c 100%);
  transform: scale(0.95);
  box-shadow: 0 1rpx 4rpx rgba(220, 38, 38, 0.4);
}

/* 删除按钮悬停效果（适配某些设备） */
.delete-btn:hover {
  box-shadow: 0 4rpx 12rpx rgba(248, 113, 113, 0.4);
}

/* 删除按钮图标样式 */
.delete-btn .iconfont {
  font-size: 24rpx;
  color: #ffffff;
  font-weight: bold;
}

/* 删除按钮容器 - 用于定位和间距控制 */
.delete-btn-wrap {
  flex-shrink: 0;
  width: 60rpx;
  height: 60rpx;
  display: flex;
  align-items: center;
  justify-content: center;
}

/* 大尺寸删除按钮（适用于需要更大点击区域的场景） */
.delete-btn.large {
  width: 56rpx;
  height: 56rpx;
  font-size: 28rpx;
}

.delete-btn.large .iconfont {
  font-size: 28rpx;
}

/* 小尺寸删除按钮（适用于紧凑布局） */
.delete-btn.small {
  width: 40rpx;
  height: 40rpx;
  font-size: 20rpx;
  box-shadow: 0 1rpx 4rpx rgba(248, 113, 113, 0.3);
}

.delete-btn.small .iconfont {
  font-size: 20rpx;
}

/* 禁用状态的删除按钮 */
.delete-btn.disabled {
  background: #d1d5db;
  color: #9ca3af;
  pointer-events: none;
  box-shadow: none;
}

.delete-btn.disabled .iconfont {
  color: #9ca3af;
}

/* 删除按钮在暗色主题下的适配 */
@media (prefers-color-scheme: dark) {
  .delete-btn {
    box-shadow: 0 2rpx 8rpx rgba(0, 0, 0, 0.3);
  }
  
  .delete-btn:active {
    box-shadow: 0 1rpx 4rpx rgba(0, 0, 0, 0.4);
  }
  
  .delete-btn:hover {
    box-shadow: 0 4rpx 12rpx rgba(0, 0, 0, 0.4);
  }
}
