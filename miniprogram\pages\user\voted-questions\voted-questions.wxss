/* pages/user/voted-questions/voted-questions.wxss */
.page {
  min-height: 100vh;
  background: #F6F7FB;
}

.container {
  padding: 20rpx;
  width: 100%;
  box-sizing: border-box;
}

.question-list {
  padding-bottom: 30rpx;
  width: 100%;
}

/* 用户页面的问题项样式覆盖 */
.user-page-question-item {
  width: 100% !important;
  margin-left: 0 !important;
  margin-right: 0 !important;
}

/* 空状态样式 */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80rpx 0;
}

.empty-state image {
  width: 200rpx;
  height: 200rpx;
  margin-bottom: 30rpx;
}

.empty-state text {
  font-size: 28rpx;
  color: #999999;
}

/* 加载状态样式 */
.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 30rpx 0;
}

.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-right: 10rpx;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-state text {
  font-size: 26rpx;
  color: #999999;
}

/* 没有更多样式 */
.no-more {
  text-align: center;
  padding: 30rpx 0;
}

.no-more text {
  font-size: 26rpx;
  color: #999999;
}

/* 空状态图片样式 */
.empty-image {
  width: 200rpx;
  height: 200rpx;
}

/* 加载动画样式 */
.loading-spinner {
  width: 40rpx;
  height: 40rpx;
  border: 4rpx solid #f3f3f3;
  border-top: 4rpx solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
} 