const mongoose = require('mongoose');

/**
 * 通知模型
 * 用于存储用户的通知信息
 */
const NotificationSchema = new mongoose.Schema(
  {
    // 通知接收者
    recipient: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: true,
      index: true
    },
    // 通知发送者
    sender: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User'
    },
    // 通知类型
    type: {
      type: String,
      enum: [
        'question_voted',
        'answer_commented',
        'comment_replied',
        'answer_liked',
        // 新增反馈相关通知类型
        'new_feedback',
        'feedback_status_updated',
        'feedback_replied',
        // 新增产品对比相关通知类型
        'product_comparison_completed'
      ],
      required: true
    },
    // 通知内容
    content: {
      type: String,
      required: true
    },
    // 相关联的内容
    relatedItem: {
      // 内容ID
      itemId: {
        type: mongoose.Schema.Types.ObjectId,
        required: true
      },
      // 内容类型
      itemType: {
        type: String,
        enum: ['question', 'answer', 'comment', 'feedback', 'product_comparison'],
        required: true
      }
    },
    // 额外元数据
    metadata: {
      type: mongoose.Schema.Types.Mixed,
      default: {}
    },
    // 是否已读
    isRead: {
      type: Boolean,
      default: false
    }
  },
  {
    timestamps: true
  }
);

// 设置索引以提高查询性能
NotificationSchema.index({ recipient: 1, createdAt: -1 });
NotificationSchema.index({ isRead: 1 });

module.exports = mongoose.model('Notification', NotificationSchema); 