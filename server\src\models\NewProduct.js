const mongoose = require('mongoose');

// NewProduct 模型 - 用于存储JS文件导入的产品数据
const NewProductSchema = new mongoose.Schema(
  {
    // 基本产品信息
    skuId: {
      type: String,
      required: true,
      unique: true,
      index: true
    },
    skuName: {
      type: String,
      required: true,
      trim: true,
      index: true
    },
    productType: {
      type: String,
      required: true,
      index: true
    },
    brandName: {
      type: String,
      required: true,
      index: true
    },
    imageUrl: {
      type: String,
      required: true
    },
    
    // 产品配置 - 直接保存JS文件中的配置结构
    configurations: [{
      name: { type: String, required: true },
      price: { type: Number, required: true },
      available: { type: Boolean, default: true },
      specs: {
        type: mongoose.Schema.Types.Mixed, // 灵活的规格存储
        default: {}
      },
      _id: false
    }],
    
    // 默认配置
    defaultConfiguration: {
      type: String,
      required: true
    },
    
    // 价格范围
    priceRange: {
      min: { type: Number, required: true },
      max: { type: Number, required: true }
    },
    
    // 是否支持对比
    supportsComparison: {
      type: Boolean,
      default: true
    },
    
    // 通用规格 - 灵活存储结构
    commonSpecs: {
      type: mongoose.Schema.Types.Mixed,
      default: {}
    },
    
    // 转换信息 - 记录数据来源和转换详情
    conversionInfo: {
      originalFile: { type: String },
      convertedAt: { type: Date },
      totalConfigurations: { type: Number },
      availableConfigurations: { type: Number },
      excludedFields: [{ type: String }],
      varyingParams: [{ type: String }],
      commonParamsCount: { type: Number },
      varyingParamsCount: { type: Number }
    },
    
    // 数据分类信息
    category: {
      type: String,
      index: true
    },
    
    // 智能搜索匹配字段
    searchMatch: {
      type: String,
      index: true
    },
    
    // 产品评分统计
    ratingStats: {
      averageRating: {
        type: Number,
        default: 0,
        min: 0,
        max: 5
      },
      totalRatings: {
        type: Number,
        default: 0,
        min: 0
      },
      lastUpdated: {
        type: Date,
        default: Date.now
      }
    }
  },
  {
    timestamps: true
  }
);

// 保存前处理中间件
NewProductSchema.pre('save', function() {
  // 生成搜索匹配字段
  this.generateSearchMatch();
  
  // 自动设置分类
  this.setCategory();
});

// 生成搜索匹配字段
NewProductSchema.methods.generateSearchMatch = function() {
  const skuName = this.skuName || '';
  const brand = this.brandName || '';
  
  // 拼接品牌和产品名称，去除空格和标点符号
  const combined = (brand + skuName)
    .replace(/[\s.,!?;:()\[\]{}'""`''""、。，！？；：（）【】《》\-_+=]/g, '')
    .toLowerCase();
  
  this.searchMatch = combined;
  return this;
};



// 设置产品分类
NewProductSchema.methods.setCategory = function() {
  // 根据 productType 设置分类（无论category是否已存在都重新设置）
  const categoryMap = {
    '手机': '手机',
    '笔记本电脑': '笔记本电脑',
    '平板电脑': '平板电脑',
    'headphones': '耳机',
    'smartwatch': '智能手表'
  };
  
  this.category = categoryMap[this.productType] || '电子产品';
  
  return this;
};

// 索引设置
NewProductSchema.index({ skuName: 'text', brandName: 'text' }, {
  weights: {
    skuName: 10,
    brandName: 8
  },
  name: 'newproduct_text_search'
});

// 实例方法：更新评分统计信息
NewProductSchema.methods.updateRatingStats = async function(averageRating, totalRatings) {
  this.ratingStats.averageRating = averageRating;
  this.ratingStats.totalRatings = totalRatings;
  this.ratingStats.lastUpdated = new Date();
  return await this.save();
};

// 复合索引
NewProductSchema.index({ productType: 1, brandName: 1, category: 1 });
NewProductSchema.index({ supportsComparison: 1, productType: 1 });
NewProductSchema.index({ 'priceRange.min': 1, 'priceRange.max': 1 });
NewProductSchema.index({ searchMatch: 1 });

module.exports = mongoose.model('NewProduct', NewProductSchema);
