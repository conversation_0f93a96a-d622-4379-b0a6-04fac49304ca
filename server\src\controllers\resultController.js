const resultService = require('../services/resultService');
const { success, error } = require('../utils/response');
const DEFAULT_AI_PROVIDER = process.env.DEFAULT_AI_PROVIDER || 'deepseek';
const DEFAULT_SUMMARY_TYPE = process.env.DEFAULT_SUMMARY_TYPE || 'rule';

console.log('DEFAULT_SUMMARY_TYPE', DEFAULT_SUMMARY_TYPE);
console.log('DEFAULT_AI_PROVIDER', DEFAULT_AI_PROVIDER);

/**
 * @desc    获取问题结果分析
 * @route   GET /api/v1/community/questions/:id/result
 * @query   {String} type - 总结类型：rule(规则总结)或ai(AI总结)，默认为rule
 * @query   {String} provider - AI提供商：huggingface、azure或deepseek，默认为deepseek
 * @query   {Boolean} async - 是否异步处理，默认为false
 * @access  Private
 */
const getQuestionResult = async (req, res) => {
  try {
    const { id } = req.params;
    const { 
      type = DEFAULT_SUMMARY_TYPE, 
      provider = DEFAULT_AI_PROVIDER,
      async = false 
    } = req.query;

    console.log("provider", provider);
    
    // 检查是否有缓存的结果
    const cachedResult = await resultService.getCachedQuestionResult(id, type);
    
    // 如果有缓存，直接返回
    if (cachedResult) {
      return success(res, 200, '获取问题结果分析成功', cachedResult);
    }
    
    // 如果没有缓存且请求AI分析结果
    if (type === 'ai') {
      // 如果是异步模式，先启动后台生成，返回处理中状态
      if (async === 'true' || async === true) {
        // 检查任务是否已经在处理中
        const isProcessing = resultService.isTaskProcessing(id);
        
        // 只有当任务不在处理中时才启动新任务
        if (!isProcessing) {
          // 启动后台生成过程但不等待完成
          resultService.generateAIResultAsync(id, provider);
        }
        
        // 返回处理中状态
        return success(res, 202, '问题结果分析正在生成中', { 
          status: 'processing',
          questionId: id,
          summaryType: type,
          alreadyProcessing: isProcessing
        });
      }
      
      // 如果是同步模式，等待生成完成
      const result = await resultService.getQuestionResultWithAI(id, provider);
      return success(res, 200, '获取问题结果分析成功', result);
    } else {
      // 获取规则分析结果
      const result = await resultService.getQuestionResult(id);
      return success(res, 200, '获取问题结果分析成功', result);
    }
  } catch (err) {
    // 处理特定错误
    if (err.message === '问题不存在' || err.message === '无效的问题ID') {
      return error(res, 404, err.message);
    }
    if (err.message.includes('AI总结生成失败')) {
      return error(res, 503, err.message);
    }
    return error(res, 500, err.message);
  }
};

/**
 * @desc    检查问题结果分析状态
 * @route   GET /api/v1/community/questions/:id/result/status
 * @query   {String} type - 总结类型：rule(规则总结)或ai(AI总结)，默认为rule
 * @access  Private
 */
const checkResultStatus = async (req, res) => {
  try {
    const { id } = req.params;
    const { type = DEFAULT_SUMMARY_TYPE } = req.query;
    
    // 检查是否有缓存的结果
    const cachedResult = await resultService.getCachedQuestionResult(id, type);
    
    if (cachedResult) {
      // 如果有缓存，表示已经生成完成
      return success(res, 200, '问题结果分析已生成', { 
        status: 'completed',
        questionId: id,
        summaryType: type
      });
    } else {
      // 检查任务是否正在处理中
      const isProcessing = resultService.isTaskProcessing(id);
      
      // 如果没有缓存，表示正在处理中或尚未开始处理
      return success(res, 200, '问题结果分析状态', { 
        status: isProcessing ? 'processing' : 'pending',
        questionId: id,
        summaryType: type
      });
    }
  } catch (err) {
    if (err.message === '无效的问题ID') {
      return error(res, 404, err.message);
    }
    return error(res, 500, err.message);
  }
};

/**
 * @desc    清除问题结果缓存
 * @route   DELETE /api/v1/community/questions/:id/result/cache
 * @query   {String} type - 缓存类型：rule, ai 或 all，默认为all
 * @access  Private Admin
 */
const clearResultCache = async (req, res) => {
  try {
    const { id } = req.params;
    const { type = 'all' } = req.query;
    
    // 验证类型参数
    if (!['rule', 'ai', 'all'].includes(type)) {
      return error(res, 400, '无效的缓存类型，必须是rule、ai或all');
    }
    
    // 清除缓存
    await resultService.clearQuestionResultCache(id, type);
    
    return success(res, 200, `问题结果缓存已清除 (类型: ${type})`);
  } catch (err) {
    if (err.message === '无效的问题ID') {
      return error(res, 404, err.message);
    }
    return error(res, 500, err.message);
  }
};


module.exports = {
  getQuestionResult,
  checkResultStatus,
  clearResultCache
}; 