const mongoose = require('mongoose');
const Schema = mongoose.Schema;

const AnswerSchema = new Schema(
  {
    // 基本信息
    questionId: {
      type: Schema.Types.ObjectId,
      ref: 'Question',
      required: true,
      index: true
    },
    optionId: {
      type: Schema.Types.ObjectId,
      required: true
    },
    userId: {
      type: Schema.Types.ObjectId,
      ref: 'User',
      required: true,
      index: true
    },
    content: {
      type: String,
      maxlength: [500, '理由内容最多500个字符'],
      default: ''
    },
    isAnonymous: {
      type: Boolean,
      default: false
    },

    // 互动数据
    likes: {
      type: Number,
      default: 0,
      index: true
    },
    likedBy: [{
      type: Schema.Types.ObjectId,
      ref: 'User'
    }],
    commentCount: {
      type: Number,
      default: 0
    }
  },
  {
    timestamps: true
  }
);

// 复合索引
AnswerSchema.index({ questionId: 1, optionId: 1 });
AnswerSchema.index({ questionId: 1, userId: 1 }, { unique: true });

// 验证用户在同一问题下只能回答一次
AnswerSchema.pre('save', async function(next) {
  // if (this.isNew) {
  if(true){
    const Answer = this.constructor;
    const existingAnswer = await Answer.findOne({
      questionId: this.questionId,
      userId: this.userId
    });
    
    if (existingAnswer) {
      const error = new Error('用户对同一问题只能回答一次');
      return next(error);
    }
  }
  next();
});

// 更新问题的选项投票计数和总投票数
AnswerSchema.post('save', async function() {
  // console.log("进来了", this.isNew)
  // if (this.isNew) {
  if(true){
    const Question = mongoose.model('Question');
    
    await Question.findByIdAndUpdate(
      this.questionId,
      {
        $inc: {
          totalVotes: 1,
          'options.$[elem].voteCount': 1
        }
      },
      {
        arrayFilters: [{ 'elem._id': this.optionId }]
      }
    );
  }
});

module.exports = mongoose.model('Answer', AnswerSchema); 