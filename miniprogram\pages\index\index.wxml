<!--pages/index/index.wxml-->
<view class="page">
  <view class="container p-30 pb-40">
    <!-- 主要操作区域 - 移到上方 -->
    <view class="main-actions-section mb-30">
      <!-- 操作按钮组 - 并列排布 -->
      <view class="action-buttons-group">
        <!-- 发布问题按钮 -->
        <button class="btn btn-primary action-btn publish-btn" bindtap="goToCreateQuestion">
          <text class="iconfont icon-add mr-10"></text>
          <text>发布问题</text>
        </button>
        
        <!-- AI推荐按钮 -->
        <button class="btn btn-secondary action-btn ai-recommend-btn" bindtap="goToAiRecommend">
          <text class="iconfont icon-ai mr-10">🤖</text>
          <text>AI推荐</text>
        </button>
        
        <!-- 产品对比按钮 -->
        <button class="btn btn-primary action-btn compare-btn" bindtap="goToProductCompare">
          <text class="iconfont icon-compare mr-10"></text>
          <text>产品对比</text>
        </button>
      </view>
    </view>
    
    <!-- 搜索区域 -->
    <view class="search-section mb-30">
      <view class="search-bar">
        <view class="form-input-wrap search-input-wrap">
          <input 
            class="form-input search-input" 
            type="text" 
            placeholder="搜索问题..."
            value="{{searchKeyword}}"
            bindinput="onSearchInput"
            bindconfirm="onSearchConfirm"
            confirm-type="search"
            focus="{{searchFocus}}"
          />
          <text class="search-icon iconfont icon-search"></text>
          <view 
            class="clear-btn {{searchKeyword ? 'show' : ''}}" 
            bindtap="clearSearch">
            <text class="iconfont icon-close"></text>
          </view>
        </view>
      </view>
      
      <!-- 搜索状态提示 -->
      <view class="search-status text-center pt-20" wx:if="{{searchKeyword}}">
        <text class="text-secondary" wx:if="{{searching}}">正在搜索 "{{searchKeyword}}"...</text>
        <text class="text-secondary" wx:elif="{{searchResults.total !== undefined}}">
          在{{questionStatus === 'open' ? '进行中' : '已结束'}}的{{currentTag ? currentTag : '全部'}}问题中找到 {{searchResults.total}} 个结果
        </text>
      </view>
    </view>
    
    <!-- 筛选和内容区域 -->
    <view class="content-section">
      <!-- 问题筛选控制区域 -->
      <view class="filter-section mb-30">
        <!-- 状态切换 -->
        <view class="tab-switch status-switch mb-20">
          <view 
            class="tab-item {{questionStatus === 'open' ? 'active' : ''}}" 
            data-status="open"
            bindtap="switchStatus">
            进行中
          </view>
          <view 
            class="tab-item {{questionStatus === 'closed' ? 'active' : ''}}" 
            data-status="closed"
            bindtap="switchStatus">
            已结束
          </view>
        </view>
        
        <!-- 分类标签导航 -->
        <view class="category-tabs" wx:if="{{!isSearchMode}}">
          <scroll-view class="tab-scroll" scroll-x="true" show-scrollbar="false">
            <view class="tab-list">
              <view 
                class="tab-item {{activeTab === tab.id ? 'active' : ''}}" 
                wx:for="{{tabs}}" 
                wx:for-item="tab" 
                wx:key="id"
                data-id="{{tab.id}}"
                bindtap="switchTab">
                {{tab.name}}
              </view>
            </view>
          </scroll-view>
        </view>
      </view>
      
      <!-- 问题列表区域 -->
      <view class="question-list-section">
        <view class="question-list">
          <block wx:if="{{questionList.length > 0}}">
            <question-item 
              wx:for="{{questionList}}" 
              wx:key="id" 
              question="{{item}}" 
              bind:questiontap="goToQuestionDetail">
            </question-item>
          </block>
          
          <!-- 空状态 -->
          <view class="empty-state text-center py-20" wx:if="{{!loading && questionList.length === 0}}">
            <view class="empty-content">
              <image src="/assets/images/empty.png" mode="aspectFit"></image>
              <text class="empty-text text-secondary mt-30" wx:if="{{isSearchMode}}">
                没有找到与 "{{searchKeyword}}" 相关的问题
              </text>
              <text class="empty-text text-secondary mt-30" wx:else>
                暂无{{questionStatus === 'open' ? '进行中' : '已结束'}}的问题
              </text>
            </view>
          </view>
          
          <!-- 加载状态 -->
          <view class="loading-state flex-center flex-column py-40" wx:if="{{loading && !refreshing}}">
            <view class="loading-spinner mb-20"></view>
            <text class="loading-text text-secondary">{{searching ? '搜索中...' : '正在加载...'}}</text>
          </view>
          
          <!-- 没有更多数据 -->
          <view class="no-more-state text-center py-30 mt-20" wx:if="{{!hasMore && questionList.length > 0}}">
            <text class="no-more-text text-light">— 已经到底了 —</text>
          </view>
        </view>
      </view>
    </view>
  </view>
</view>