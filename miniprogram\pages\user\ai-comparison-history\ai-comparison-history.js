const app = getApp();
const { user } = require('../../../utils/api');
const util = require('../../../utils/util');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    historyList: [],
    loading: false,
    hasMore: true,
    page: 1,
    limit: 20,
    isEmpty: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    this.loadComparisonHistory();
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh: function () {
    this.refreshData();
  },

  /**
   * 上拉加载更多
   */
  onReachBottom: function () {
    if (this.data.hasMore && !this.data.loading) {
      this.loadMoreData();
    }
  },

  /**
   * 加载对比历史数据
   */
  loadComparisonHistory: function(isLoadMore = false) {
    if (this.data.loading) return;

    this.setData({
      loading: true
    });

    const page = isLoadMore ? this.data.page : 1;
    const params = {
      page: page,
      limit: this.data.limit
    };

    user.getComparisonHistory(params)
      .then(res => {
        console.log('获取AI对比历史成功:', res);
        
        // 修正：后端返回的是 histories，不是 list
        const newList = res.data.histories || [];
        
        // 格式化数据以匹配前端模板需求
        const formattedList = newList.map(item => ({
          id: item.id,
          cacheId: item.comparisonCacheId, // 映射字段名
          productNames: item.productNames || [],
          productCount: (item.productNames || []).length,
          queryText: this.generateQueryText(item.productNames), // 生成查询文本
          status: 'completed', // 默认状态为完成
          createdAt: item.createdAt,
          updatedAt: item.updatedAt
        }));
        
        const currentList = isLoadMore ? this.data.historyList : [];
        const updatedList = [...currentList, ...formattedList];

        this.setData({
          historyList: updatedList,
          loading: false,
          hasMore: newList.length >= this.data.limit,
          page: isLoadMore ? this.data.page + 1 : 2,
          isEmpty: updatedList.length === 0
        });

        console.log('设置页面数据:', {
          historyCount: updatedList.length,
          hasMore: this.data.hasMore,
          isEmpty: this.data.isEmpty,
          firstItem: updatedList[0]
        });

        // 停止下拉刷新
        if (wx.stopPullDownRefresh) {
          wx.stopPullDownRefresh();
        }
      })
      .catch(err => {
        console.error('获取AI对比历史失败:', err);
        this.setData({
          loading: false,
          isEmpty: !isLoadMore && this.data.historyList.length === 0
        });

        // 停止下拉刷新
        if (wx.stopPullDownRefresh) {
          wx.stopPullDownRefresh();
        }

        util.showToast(err.message || '获取历史记录失败');
      });
  },

  /**
   * 刷新数据
   */
  refreshData: function() {
    this.setData({
      page: 1,
      hasMore: true,
      isEmpty: false
    });
    this.loadComparisonHistory();
  },

  /**
   * 加载更多数据
   */
  loadMoreData: function() {
    this.loadComparisonHistory(true);
  },

  /**
   * 查看对比详情
   */
  onViewDetail: function(event) {
    const item = event.detail.item;
    console.log('点击查看对比详情，历史记录项:', item);
    
    if (!item) {
      util.showToast('对比记录无效');
      return;
    }

    // 检查是否有产品名称数据
    if (!item.productNames || !Array.isArray(item.productNames) || item.productNames.length < 2) {
      console.error('产品名称数据不完整:', item.productNames);
      util.showToast('产品信息不完整，无法进行对比');
      return;
    }

    console.log('准备跳转到产品对比页面，产品名称:', item.productNames);

    // 跳转到产品对比V4页面，传递产品名称
    const productNamesParam = encodeURIComponent(JSON.stringify(item.productNames));
    wx.navigateTo({
      url: `/pages/product/product_compare_v4/product_compare_v4?productNames=${productNamesParam}`,
      success: () => {
        console.log('成功跳转到产品对比页面');
      },
      fail: (err) => {
        console.error('跳转失败:', err);
        util.showToast('页面跳转失败');
      }
    });
  },

  /**
   * 去体验对比功能
   */
  onGoToCompare: function() {
    wx.navigateTo({
      url: '/pages/index/index'
    });
  },

  /**
   * 生成查询文本（根据产品名称）
   */
  generateQueryText: function(productNames) {
    if (!productNames || !Array.isArray(productNames) || productNames.length === 0) {
      return '产品对比查询';
    }
    
    if (productNames.length <= 2) {
      return `${productNames.join(' VS ')} 对比`;
    } else {
      return `${productNames.slice(0, 2).join(' VS ')} 等${productNames.length}个产品对比`;
    }
  }
})
