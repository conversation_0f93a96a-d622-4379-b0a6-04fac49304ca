const mongoose = require('mongoose');

/**
 * 短信验证码记录模型
 */
const SmsVerificationSchema = new mongoose.Schema({
  phone: { 
    type: String, 
    required: true,
    index: true
  },
  verifyCode: { 
    type: String, 
    required: true 
  },
  purpose: {
    type: String, 
    enum: ['register', 'login', 'auth', 'reset_password', 'change_phone', 'withdraw'],
    required: true
  },
  status: {
    type: String,
    enum: ['pending', 'verified', 'expired'],
    default: 'pending'
  },
  createdAt: { 
    type: Date, 
    default: Date.now, 
    expires: 600  // 10分钟自动过期
  },
  verifiedAt: { 
    type: Date 
  },
  attempts: { 
    type: Number, 
    default: 0 
  },
  ip: { 
    type: String 
  },
  deviceInfo: { 
    type: Object 
  }
});

// 索引，用于提高查询效率
SmsVerificationSchema.index({ phone: 1, purpose: 1, status: 1 });

const SmsVerification = mongoose.model('SmsVerification', SmsVerificationSchema);

module.exports = SmsVerification; 