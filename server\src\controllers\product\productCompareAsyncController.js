const { success, error } = require('../../utils/response');
const { checkComparisonCacheStatus } = require('../../services/product/productCompareAsync');

/**
 * 产品对比缓存状态检查控制器
 * 处理产品对比缓存状态检查请求
 */

/**
 * 检查产品对比缓存状态接口
 * @param {Object} req - 请求对象
 * @param {Object} res - 响应对象
 */
const checkComparisonCacheHandler = async (req, res) => {
  try {
    console.log('📥 收到产品对比缓存状态检查请求');

    // 1. 获取请求参数
    const { productNames } = req.body;
    const userId = req.user?.id || req.user?._id;

    console.log('请求参数:', { productNames, userId });

    // 2. 验证必要参数
    if (!productNames || !Array.isArray(productNames)) {
      console.error('❌ 产品名称列表无效');
      return error(res, 400, '产品名称列表不能为空且必须是数组格式');
    }

    if (!userId) {
      console.error('❌ 用户未登录');
      return error(res, 401, '用户未登录，无法进行产品对比');
    }

    // 3. 调用缓存状态检查服务
    const result = await checkComparisonCacheStatus(productNames, userId);

    if (!result.success) {
      console.error('❌ 缓存状态检查失败:', result.error);
      return error(res, 400, result.error, result.data);
    }

    console.log('✅ 缓存状态检查完成');

    // 4. 构建响应数据
    const responseData = {
      ...result.data,
      meta: {
        requestedProducts: productNames,
        description: result.data.hasCache ? '找到缓存结果' : '启动异步处理'
      }
    };

    const message = result.data.hasCache ?
      '找到缓存结果，可以直接获取对比数据' :
      '已启动异步产品对比分析';

    return success(res, 200, message, responseData);

  } catch (err) {
    console.error('❌ 缓存状态检查控制器异常:', err);
    return error(res, 500, '服务器内部错误', {
      message: err.message,
      stack: process.env.NODE_ENV === 'development' ? err.stack : undefined
    });
  }
};

module.exports = {
  checkComparisonCacheHandler
};
