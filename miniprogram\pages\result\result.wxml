<view class="container">
  <!-- 加载状态 -->
  <view class="loading-container" wx:if="{{loading}}">
    <view class="loading-spinner"></view>
    <text class="loading-text">加载中...</text>
  </view>

  <!-- 处理中状态 -->
  <view class="processing-container" wx:elif="{{processing}}">
    <view class="processing-animation">
      <view class="processing-icon">
        <view class="processing-spinner"></view>
      </view>
    </view>
    <text class="processing-title">AI正在分析投票结果</text>
    <text class="processing-desc">请稍候，分析结果生成中...</text>
    <view class="processing-tips">
      <text>首次生成可能需要30秒-1分钟</text>
      <text>页面会自动刷新，请勿关闭</text>
    </view>
  </view>

  <!-- 错误提示 -->
  <view class="error-container" wx:elif="{{error}}">
    <icon type="warn" size="64" color="#ff4d4f"></icon>
    <text class="error-text">{{error}}</text>
    <button class="btn btn-primary retry-btn" bindtap="fetchResultData">重试</button>
  </view>

  <!-- 结果内容 -->
  <block wx:elif="{{result}}">
    <!-- 可被截图的容器区域 -->
    <view id="result-container" class="result-container">
      <!-- 问题头部信息 -->
      <view class="result-header">
        <view class="result-title">{{result.title}}</view>
        <view class="result-stats">
          <view class="stat-item">
            <text class="stat-label">参与人数</text>
            <text class="stat-value">{{result.baseData.totalVotes}}</text>
          </view>
          <view class="stat-item">
            <text class="stat-label">发起时间</text>
            <text class="stat-value">{{result.createdAt}}</text>
          </view>
          <view class="stat-item">
            <text class="stat-label">结束时间</text>
            <text class="stat-value">{{result.expiryTime || '未设置'}}</text>
          </view>
        </view>
      </view>

      <!-- 选项结果 -->
      <view class="options-result">
        <view class="section-title">投票结果</view>
        <view class="options-list">
          <view class="option-item {{item.id === result.baseData.winningOption.id ? 'winning-option' : ''}}" 
            wx:for="{{result.baseData.votingTrend}}" 
            wx:key="id">
            <view class="option-header">
              <text class="option-rank">{{index + 1}}</text>
              <text class="option-content">{{item.content}}</text>
              <text class="option-count">{{item.voteCount}}票</text>
            </view>
            <view class="option-progress">
              <view class="progress-bar" style="width: {{item.percentage}}%"></view>
              <text class="progress-text">{{item.percentage}}%</text>
            </view>
          </view>
        </view>
      </view>

      <!-- 商品购买链接 -->
      <view class="product-links" wx:if="{{showProductLinks}}">
        <view class="section-title">购买渠道</view>
        <view class="links-list">
          <view class="link-item" wx:for="{{productLinks.links}}" wx:key="*this" wx:for-index="platform" wx:for-item="link">
            <view class="link-platform">
              <view id="platform-{{index}}" class="platform-icon platform-common" style="background-color: {{platform === '京东' ? '#e33333' : platform === '天猫' || platform === '淘宝' ? '#ff6a00' : platform === '拼多多' ? '#e02e24' : platform === '苏宁' ? '#ffaa00' : platform === '唯品会' ? '#f10180' : '#1890ff'}};">
                {{platform.substr(0,1)}}
              </view>
              <text>{{platform}}</text>
            </view>
            <button class="copy-btn" data-platform="{{platform}}" data-link="{{link}}" bindtap="copyProductLink">复制链接</button>
          </view>
        </view>
        <view class="link-tips">复制链接后可前往对应平台购买</view>
      </view>

      <!-- AI分析结果/规则分析结果 -->
      <view class="analysis-result">
        <view class="section-title">投票分析</view>
        <!-- 当parsedSummary存在时显示解析后的内容 -->
        <towxml wx:if="{{parsedSummary}}" nodes="{{parsedSummary}}"/>
        <!-- 当parsedSummary不存在但result.summary存在时显示原始文本 -->
        <view wx:elif="{{result.summary}}" class="raw-summary">
          <text>{{result.summary}}</text>
        </view>
        <!-- 当没有任何分析内容时的默认显示 -->
        <view wx:else class="no-analysis">
          <text>暂无投票分析内容</text>
        </view>
      </view>

      <!-- 水印 -->
      <view class="watermark">
        <text>—— 来自「选选」——</text>
        <text class="watermark-desc">大众帮忙选择电子消费产品的小程序</text>
      </view>
    </view>

    <!-- 分享操作区 -->
    <view class="action-bar">
      <button class="action-btn save-btn" bindtap="shareAsImage">
        <text class="iconfont icon-save"></text>
        <text>保存图片</text>
      </button>
      <button class="action-btn share-btn" open-type="share">
        <text class="iconfont icon-share"></text>
        <text>分享给好友</text>
      </button>
    </view>
  </block>

  <!-- Canvas用于生成分享图片 -->
  <canvas type="2d" id="share-canvas" class="share-canvas"></canvas>
</view> 