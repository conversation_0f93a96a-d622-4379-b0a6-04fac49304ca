<!--pages/product/ai-recommend/ai-recommend.wxml-->
<view class="page-container">
  <view class="page-header">
    <text class="page-title">AI智能推荐</text>
    <text class="page-desc">基于您的需求，AI为您推荐最适合的产品</text>
  </view>

  <view class="form-container">
    <!-- 第一步：核心需求 -->
    <view class="step-container step-1">
      <view class="step-header">
        <view class="step-number">1</view>
        <view class="step-info">
          <text class="step-title">描述您的需求</text>
          <text class="step-desc">告诉AI您想要什么样的产品</text>
        </view>
      </view>

      <!-- 需求描述 -->
      <view class="form-item primary">
        <text class="form-label">需求描述<text class="required-tip">*</text></text>
        <input
          class="title-input"
          placeholder="如：想买一款拍照好的手机、需要一台轻薄的笔记本"
          maxlength="50"
          bindinput="inputTitle"
          value="{{title}}"
        ></input>
        <text class="input-counter">{{title.length}}/50</text>
      </view>

      <!-- 产品类别选择 -->
      <view class="form-item primary">
        <text class="form-label">产品类别<text class="required-tip">*</text></text>
        <view class="tags-container">
          <view
            class="tag-item {{tagStatus[tag] ? 'active' : ''}}"
            wx:for="{{tagOptions}}"
            wx:for-item="tag"
            wx:key="*this"
            bindtap="selectTag"
            data-tag="{{tag}}"
            hover-class="tag-hover"
          >
            <text>{{tag}}</text>
            <text class="tag-check" wx:if="{{tagStatus[tag]}}">✓</text>
          </view>
        </view>
        <text class="tip-text">选择您要购买的产品类型</text>
      </view>
    </view>

    <!-- 第二步：品牌偏好（仅在选择产品类别后显示） -->
    <view class="step-container step-2" wx:if="{{selectedCategory}}">
      <view class="step-header">
        <view class="step-number">2</view>
        <view class="step-info">
          <text class="step-title">品牌偏好</text>
          <text class="step-desc">选择您喜欢的品牌（可跳过）</text>
        </view>
      </view>

      <!-- 选中的产品类别显示 -->
      <view class="selected-category-display">
        <text class="category-icon">📱</text>
        <text class="category-name">{{selectedCategory}}</text>
        <text class="category-count">{{availableBrands.length}}个品牌可选</text>
      </view>

      <!-- 品牌选择网格 -->
      <view class="brand-selection-area">
        <view class="brand-grid">
          <view class="brand-item {{brandStatus[brand] ? 'selected' : ''}}"
                wx:for="{{availableBrands}}" wx:key="*this" wx:for-item="brand"
                bindtap="onToggleBrand" data-brand="{{brand}}"
                hover-class="brand-hover">
            <text class="brand-text">{{brand}}</text>
            <text class="brand-check" wx:if="{{brandStatus[brand]}}">✓</text>
          </view>
        </view>

        <!-- 已选择品牌提示 -->
        <view class="selected-brands-summary" wx:if="{{selectedBrands.length > 0}}">
          <text class="summary-text">已选择 {{selectedBrands.length}} 个品牌</text>
          <text class="summary-brands">{{selectedBrands.join('、')}}</text>
        </view>

        <!-- 品牌选择提示 -->
        <view class="brand-tip">
          <text class="tip-icon">💡</text>
          <text class="tip-text">不选择品牌，AI将推荐该类别下的所有优质产品</text>
        </view>
      </view>
    </view>

    <!-- 第三步：详细偏好（可折叠的高级选项） -->
    <view class="step-container step-3">
      <view class="step-header">
        <view class="step-number">3</view>
        <view class="step-info">
          <text class="step-title">详细偏好</text>
          <text class="step-desc">提供更多信息，获得更精准推荐（可选）</text>
        </view>
      </view>

      <!-- 使用场景 -->
      <view class="form-item secondary">
        <text class="form-label">使用场景</text>
        <textarea
          class="scene-input"
          placeholder="如：日常办公、游戏娱乐、商务出差等"
          maxlength="200"
          bindinput="inputScene"
          value="{{scene}}"
        ></textarea>
        <text class="input-counter">{{scene.length}}/200</text>
      </view>

      <!-- 关键考量因素 -->
      <view class="form-item secondary">
        <text class="form-label">关键考量因素</text>
        <textarea
          class="factor-input"
          placeholder="如：续航能力、拍照效果、运行速度、外观设计等"
          maxlength="200"
          bindinput="inputKeyFactors"
          value="{{keyFactors}}"
        ></textarea>
        <text class="input-counter">{{keyFactors.length}}/200</text>
      </view>

      <!-- 预算区间 -->
      <view class="form-item secondary">
        <text class="form-label">预算区间</text>
        <view class="budget-container">
          <input
            class="budget-input"
            type="number"
            placeholder="最低预算"
            bindinput="inputBudgetMin"
            value="{{budget.min}}"
          ></input>
          <text class="budget-separator">-</text>
          <input
            class="budget-input"
            type="number"
            placeholder="最高预算"
            bindinput="inputBudgetMax"
            value="{{budget.max}}"
          ></input>
          <text class="budget-currency">元</text>
        </view>
      </view>
    </view>



    <!-- 获取推荐按钮 -->
    <view class="recommend-container">
      <button
        class="recommend-btn {{!canRecommend ? 'disabled' : ''}}"
        bindtap="getAiRecommendation"
        disabled="{{!canRecommend}}"
        hover-class="btn-hover"
      >
        <text class="btn-icon">🚀</text>
        <text>获取AI推荐</text>
      </button>
    </view>

  </view>



  <!-- 加载提示 -->
  <view class="loading-overlay {{loading || processing ? 'show' : ''}}" wx:if="{{loading || processing}}">
    <view class="loading-content">
      <view class="loading-spinner"></view>
      <text class="loading-text" wx:if="{{loading}}">AI正在为您分析推荐...</text>
      <text class="loading-text" wx:if="{{processing}}">{{processingMessage}}</text>
      <text class="loading-tip">这通常需要5-15秒，请耐心等待</text>
    </view>
  </view>
</view>