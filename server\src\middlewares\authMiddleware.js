const jwt = require('jsonwebtoken');
const { errorResponse } = require('../utils/apiResponse');

// 验证令牌
exports.protect = async (req, res, next) => {
  try {
    let token;
    
    // 从请求头获取token
    if (
      req.headers.authorization && 
      req.headers.authorization.startsWith('Bearer')
    ) {
      token = req.headers.authorization.split(' ')[1];
    }
    
    // 检查是否存在token
    if (!token) {
      return errorResponse(res, 401, '未授权，请登录');
    }
    
    try {
      // 验证token
      const decoded = jwt.verify(token, process.env.JWT_SECRET);
      
      // 将用户ID附加到请求对象
      req.userId = decoded.id;
      next();
    } catch (error) {
      return errorResponse(res, 401, '令牌无效或已过期');
    }
  } catch (error) {
    console.error('认证中间件错误:', error);
    return errorResponse(res, 500, '服务器错误');
  }
};

// 验证用户角色权限
exports.restrictTo = (...roles) => {
  return (req, res, next) => {
    // 注意：此处假设用户角色已由前一个中间件加载
    // 在实际实现中，您可能需要从数据库加载用户信息
    if (!roles.includes(req.user.role)) {
      return errorResponse(res, 403, '没有权限执行此操作');
    }
    
    next();
  };
}; 