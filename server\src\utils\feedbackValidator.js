const Joi = require('joi');

/**
 * 创建反馈验证模式
 */
exports.createFeedbackSchema = Joi.object({
  content: Joi.string()
    .max(500)
    .required()
    .messages({
      'string.empty': '反馈内容不能为空',
      'string.max': '反馈内容最多500个字符',
      'any.required': '请输入反馈内容'
    }),
  type: Joi.string()
    .valid('bug', 'suggestion', 'question', 'other')
    .default('other')
    .messages({
      'any.only': '反馈类型必须是"bug"、"suggestion"、"question"或"other"'
    }),
  images: Joi.array()
    .items(Joi.string())
    .max(3)
    .optional()
    .messages({
      'array.max': '最多上传3张图片'
    }),
  deviceInfo: Joi.object().optional()
});

/**
 * 更新反馈状态验证模式
 */
exports.updateFeedbackStatusSchema = Joi.object({
  status: Joi.string()
    .valid('pending', 'processing', 'resolved', 'rejected')
    .required()
    .messages({
      'string.empty': '状态不能为空',
      'any.only': '无效的状态值',
      'any.required': '请提供状态'
    })
});

/**
 * 管理员回复反馈验证模式
 */
exports.replyFeedbackSchema = Joi.object({
  content: Joi.string()
    .max(500)
    .required()
    .messages({
      'string.empty': '回复内容不能为空',
      'string.max': '回复内容最多500个字符',
      'any.required': '请输入回复内容'
    })
});

/**
 * 获取反馈列表验证模式
 */
exports.getFeedbacksSchema = Joi.object({
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(50).default(10),
  status: Joi.string()
    .valid('all', 'pending', 'processing', 'resolved', 'rejected')
    .default('all'),
  type: Joi.string()
    .valid('all', 'bug', 'suggestion', 'question', 'other')
    .default('all')
}); 