#!/usr/bin/env node

/**
 * 批量导入 TABLETS_JS 数据到 NewProduct 集合
 * 
 * 使用方法:
 * node scripts/importPhonesData.js
 * 
 * 环境变量:
 * MONGODB_URI - MongoDB 连接字符串 (默认从 .env 读取)
 */

const fs = require('fs');
const path = require('path');
const mongoose = require('mongoose');
require('dotenv').config();

// 导入模型
const NewProduct = require('../src/models/NewProduct');

class PhonesDataImporter {
  constructor() {
    this.mongoUri = process.env.MONGODB_URI || 'mongodb://localhost:27017/xuanxuan_test';
    this.dataRoot = path.resolve(__dirname, '../../data/TABLETS_JS');
    this.stats = {
      totalFiles: 0,
      successfulImports: 0,
      failedImports: 0,
      skippedFiles: 0,
      errors: []
    };
  }

  async connect() {
    try {
      await mongoose.connect(this.mongoUri);
      console.log('✅ 已连接到 MongoDB:', this.mongoUri);
    } catch (error) {
      console.error('❌ MongoDB 连接失败:', error.message);
      throw error;
    }
  }

  async disconnect() {
    try {
      await mongoose.disconnect();
      console.log('✅ 已断开 MongoDB 连接');
    } catch (error) {
      console.error('❌ 断开 MongoDB 连接失败:', error.message);
    }
  }

  /**
   * 递归扫描目录，找到所有 .js 文件
   */
  scanJsFiles(dirPath) {
    const files = [];
    
    try {
      const items = fs.readdirSync(dirPath);
      
      for (const item of items) {
        const itemPath = path.join(dirPath, item);
        const stat = fs.statSync(itemPath);
        
        if (stat.isDirectory()) {
          // 如果是目录，递归扫描
          const subFiles = this.scanJsFiles(itemPath);
          files.push(...subFiles);
        } else if (stat.isFile() && item.endsWith('.js')) {
          // 排除一些工具脚本文件
          const excludeFiles = [
            'brand_name_replacer.js',
            'changxiang_test.js',
            'convert_iphone_data - 副本.js',
            'update_brand_keywords.js'
          ];
          
          if (!excludeFiles.includes(item)) {
            files.push({
              filePath: itemPath,
              fileName: item,
              relativePath: path.relative(this.dataRoot, itemPath)
            });
          }
        }
      }
    } catch (error) {
      console.error(`❌ 扫描目录失败: ${dirPath}`, error.message);
    }
    
    return files;
  }

  /**
   * 加载 JS 文件并提取产品数据
   */
  loadProductData(filePath) {
    try {
      // 清除 require 缓存，确保重新加载
      delete require.cache[require.resolve(filePath)];
      
      const productData = require(filePath);
      
      // 检查数据格式
      if (!productData) {
        throw new Error('文件没有导出数据');
      }
      
      // 如果是数组，取第一个元素
      const data = Array.isArray(productData) ? productData[0] : productData;
      
      if (!data || typeof data !== 'object') {
        throw new Error('数据格式不正确');
      }
      
      // 验证必需字段
      const requiredFields = ['skuId', 'skuName', 'productType', 'brandName'];
      for (const field of requiredFields) {
        if (!data[field]) {
          throw new Error(`缺少必需字段: ${field}`);
        }
      }
      
      return data;
    } catch (error) {
      throw new Error(`加载文件失败: ${error.message}`);
    }
  }

  /**
   * 转换数据格式以适配 NewProduct 模型
   */
  transformProductData(rawData, fileInfo) {
    try {
      const transformed = {
        skuId: rawData.skuId,
        skuName: rawData.skuName,
        productType: rawData.productType,
        brandName: rawData.brandName,
        imageUrl: rawData.imageUrl || 'https://example.com/default-phone.jpg',
        configurations: rawData.configurations || [],
        defaultConfiguration: rawData.defaultConfiguration || '',
        priceRange: rawData.priceRange || { min: 0, max: 0 },
        supportsComparison: rawData.supportsComparison !== undefined ? rawData.supportsComparison : true,
        commonSpecs: rawData.commonSpecs || {},
        conversionInfo: rawData.conversionInfo || {}
      };
      
      // 根据 productType 设置正确的 category
      const categoryMap = {
        '手机': '手机',
        '笔记本电脑': '笔记本电脑',
        '平板电脑': '平板电脑',
        'headphones': '耳机',
        'smartwatch': '智能手表'
      };
      transformed.category = categoryMap[rawData.productType] || '电子产品';
      
      // 确保价格范围正确
      if (transformed.configurations && transformed.configurations.length > 0) {
        const prices = transformed.configurations
          .map(config => config.price)
          .filter(price => typeof price === 'number' && price > 0);
        
        if (prices.length > 0) {
          transformed.priceRange = {
            min: Math.min(...prices),
            max: Math.max(...prices)
          };
        }
      }
      
      // 确保默认配置存在
      if (!transformed.defaultConfiguration && transformed.configurations.length > 0) {
        transformed.defaultConfiguration = transformed.configurations[0].name;
      }
      
      return transformed;
    } catch (error) {
      throw new Error(`数据转换失败: ${error.message}`);
    }
  }

  /**
   * 导入单个产品
   */
  async importProduct(productData) {
    try {
      // 检查是否已存在
      const existing = await NewProduct.findOne({ skuId: productData.skuId });
      
      if (existing) {
        // 更新现有记录
        Object.assign(existing, productData);
        await existing.save();
        return { action: 'updated', product: existing };
      } else {
        // 创建新记录
        const newProduct = new NewProduct(productData);
        await newProduct.save();
        return { action: 'created', product: newProduct };
      }
    } catch (error) {
      throw new Error(`保存产品失败: ${error.message}`);
    }
  }

  /**
   * 处理单个文件
   */
  async processFile(fileInfo) {
    try {
      console.log(`📁 处理文件: ${fileInfo.relativePath}`);
      
      // 加载产品数据
      const rawData = this.loadProductData(fileInfo.filePath);
      
      // 转换数据格式
      const productData = this.transformProductData(rawData, fileInfo);
      
      // 导入到数据库
      const result = await this.importProduct(productData);
      
      console.log(`✅ ${result.action === 'created' ? '新增' : '更新'}: ${productData.skuName} (类型: ${productData.productType}, 分类: ${productData.category})`);
      
      this.stats.successfulImports++;
      return result;
      
    } catch (error) {
      console.error(`❌ 处理文件失败: ${fileInfo.relativePath}`, error.message);
      this.stats.failedImports++;
      this.stats.errors.push({
        file: fileInfo.relativePath,
        error: error.message
      });
      return null;
    }
  }

  /**
   * 批量导入所有文件
   */
  async importAll() {
    console.log('🚀 开始批量导入 TABLETS_JS 数据...\n');
    
    try {
      // 连接数据库
      await this.connect();
      
      // 扫描所有 JS 文件
      console.log('📂 扫描文件...');
      const jsFiles = this.scanJsFiles(this.dataRoot);
      this.stats.totalFiles = jsFiles.length;
      
      console.log(`📊 找到 ${jsFiles.length} 个 JS 文件\n`);
      
      if (jsFiles.length === 0) {
        console.log('❌ 没有找到任何 JS 文件');
        return;
      }
      
      // 处理每个文件
      console.log('🔄 开始处理文件...\n');
      
      for (let i = 0; i < jsFiles.length; i++) {
        const fileInfo = jsFiles[i];
        console.log(`[${i + 1}/${jsFiles.length}]`);
        
        await this.processFile(fileInfo);
        
        // 每处理 10 个文件显示一次进度
        if ((i + 1) % 10 === 0) {
          console.log(`\n📈 进度: ${i + 1}/${jsFiles.length} (${Math.round((i + 1) / jsFiles.length * 100)}%)\n`);
        }
      }
      
    } catch (error) {
      console.error('❌ 导入过程中发生错误:', error.message);
    } finally {
      await this.disconnect();
      this.printSummary();
    }
  }

  /**
   * 打印导入统计信息
   */
  printSummary() {
    console.log('\n' + '='.repeat(50));
    console.log('📊 导入统计信息');
    console.log('='.repeat(50));
    console.log(`📁 总文件数: ${this.stats.totalFiles}`);
    console.log(`✅ 成功导入: ${this.stats.successfulImports}`);
    console.log(`❌ 导入失败: ${this.stats.failedImports}`);
    console.log(`⏭️  跳过文件: ${this.stats.skippedFiles}`);
    console.log(`📈 成功率: ${this.stats.totalFiles > 0 ? Math.round(this.stats.successfulImports / this.stats.totalFiles * 100) : 0}%`);
    
    if (this.stats.errors.length > 0) {
      console.log('\n❌ 错误详情:');
      this.stats.errors.forEach((error, index) => {
        console.log(`${index + 1}. ${error.file}: ${error.error}`);
      });
    }
    
    console.log('\n🎉 导入完成!');
  }

  /**
   * 清空 NewProduct 集合
   */
  async clearCollection() {
    try {
      await this.connect();
      const result = await NewProduct.deleteMany({});
      console.log(`🗑️  已清空 NewProduct 集合，删除了 ${result.deletedCount} 条记录`);
    } catch (error) {
      console.error('❌ 清空集合失败:', error.message);
    } finally {
      await this.disconnect();
    }
  }
}

// 命令行参数处理
async function main() {
  const args = process.argv.slice(2);
  const importer = new PhonesDataImporter();
  
  if (args.includes('--clear') || args.includes('-c')) {
    console.log('⚠️  清空模式：将删除 NewProduct 集合中的所有数据');
    console.log('按 Ctrl+C 取消，或等待 5 秒后自动继续...\n');
    
    await new Promise(resolve => setTimeout(resolve, 5000));
    await importer.clearCollection();
    return;
  }
  
  if (args.includes('--help') || args.includes('-h')) {
    console.log(`
使用方法: node scripts/importPhonesData.js [选项]

选项:
  --clear, -c     清空 NewProduct 集合
  --help, -h      显示帮助信息

环境变量:
  MONGODB_URI     MongoDB 连接字符串 (默认: mongodb://localhost:27017/xuanxuan_test)

示例:
  node scripts/importPhonesData.js           # 导入所有数据
  node scripts/importPhonesData.js --clear   # 清空集合
    `);
    return;
  }
  
  // 默认执行导入
  await importer.importAll();
}

// 处理未捕获的异常
process.on('unhandledRejection', (reason, promise) => {
  console.error('❌ 未处理的 Promise 拒绝:', reason);
  process.exit(1);
});

process.on('uncaughtException', (error) => {
  console.error('❌ 未捕获的异常:', error);
  process.exit(1);
});

// 处理 Ctrl+C
process.on('SIGINT', () => {
  console.log('\n\n⚠️  收到中断信号，正在退出...');
  process.exit(0);
});

// 运行主程序
if (require.main === module) {
  main().catch(error => {
    console.error('❌ 程序执行失败:', error.message);
    process.exit(1);
  });
}

module.exports = PhonesDataImporter;
