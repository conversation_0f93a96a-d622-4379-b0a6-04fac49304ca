/**
 * 商品链接数据
 * 用于手动添加热门产品的购买链接
 * 
 * 数据结构:
 * {
 *   "产品名称": {
 *     "platform1": "链接1",
 *     "platform2": "链接2",
 *     ...
 *   }
 * }
 */

const PRODUCT_LINKS = {
  // 手机示例
  "华为mate70": {
    "京东": "https://item.jd.com/example_huawei_mate70.html",
    "天猫": "https://detail.tmall.com/item.htm?id=example_huawei_mate70",
    "拼多多": "https://mobile.yangkeduo.com/goods.html?goods_id=example_huawei_mate70"
  },
  "小米14": {
    "京东": "https://item.jd.com/example_xiaomi14.html",
    "天猫": "https://detail.tmall.com/item.htm?id=example_xiaomi14",
    "拼多多": "https://mobile.yangkeduo.com/goods.html?goods_id=example_xiaomi14"
  },
  "iPhone15": {
    "京东": "https://item.jd.com/example_iphone15.html",
    "天猫": "https://detail.tmall.com/item.htm?id=example_iphone15",
    "拼多多": "https://mobile.yangkeduo.com/goods.html?goods_id=example_iphone15"
  },
  
  // 笔记本电脑示例
  "联想小新": {
    "京东": "https://item.jd.com/example_lenovo_xiaoxin.html",
    "天猫": "https://detail.tmall.com/item.htm?id=example_lenovo_xiaoxin"
  },
  
  // 耳机示例
  "苹果AirPods Pro": {
    "京东": "https://item.jd.com/example_airpods_pro.html",
    "天猫": "https://detail.tmall.com/item.htm?id=example_airpods_pro"
  }
};

module.exports = {
  PRODUCT_LINKS
}; 