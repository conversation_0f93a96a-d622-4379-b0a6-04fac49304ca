/**
 * 产品搜索匹配字段更新脚本
 * 用于为现有的产品数据添加智能搜索匹配字段 (searchMatch)
 * 
 * 使用方法：
 * 1. node updateProductSearchMatch.js
 */

const mongoose = require('mongoose');
const dotenv = require('dotenv');
const path = require('path');

// 加载环境变量
dotenv.config({ path: path.resolve(__dirname, '../../../.env') });

// 数据库连接配置
const mongoUrl = process.env.MONGODB_URI || 'mongodb://localhost:27017/xuanxuan';

// 加载产品模型
const Product = require('../models/Product');

/**
 * 生成搜索匹配字段
 * @param {String} skuName - 产品SKU名称
 * @param {String} brand - 产品品牌 (来自productSpecs.general.brand)
 * @returns {String} 处理后的搜索匹配字符串
 */
const generateSearchMatch = (skuName, brand) => {
  const sku = skuName || '';
  const brandName = brand || '';
  
  // 拼接skuName和productSpecs.general.brand，去除空格和标点符号，保留中文字符
  const combined = (brandName + sku)
    .replace(/[\s.,!?;:()\[\]{}'""`''""、。，！？；：（）【】《》\-_+=]/g, '') // 只移除空格和常见标点符号
    .toLowerCase(); // 转为小写便于搜索
  
  return combined;
};

/**
 * 更新产品的搜索匹配字段
 */
const updateProductSearchMatch = async () => {
  try {
    console.log('🚀 开始更新产品搜索匹配字段...');
    
    // 🔌 连接数据库
    console.log('🔌 连接数据库...');
    await mongoose.connect(mongoUrl, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
    console.log('✅ 数据库连接成功');
    
    // 获取所有产品
    const products = await Product.find({});
    
    console.log(`📦 找到 ${products.length} 个产品`);
    
    let updatedCount = 0;
    let errorCount = 0;
    
    // 批量处理产品
    for (const product of products) {
      try {
        // 获取productSpecs.general.brand字段
        const brandName = product.productSpecs?.general?.brand || '';
        
        // 生成搜索匹配字段 (拼接skuName和productSpecs.general.brand)
        const searchMatch = generateSearchMatch(product.skuName, brandName);
        
        // 更新产品
        await Product.findByIdAndUpdate(product._id, {
          searchMatch: searchMatch
        });
        
        console.log(`✅ 更新产品 ${product.skuId} (${product.skuName}) - 搜索匹配: ${searchMatch}`);
        updatedCount++;
        
      } catch (error) {
        console.error(`❌ 更新产品 ${product.skuId} 失败:`, error.message);
        errorCount++;
      }
    }
    
    console.log('\n=== 📊 更新结果统计 ===');
    console.log(`总产品数: ${products.length}`);
    console.log(`成功更新: ${updatedCount}`);
    console.log(`更新失败: ${errorCount}`);
    console.log('🎉 产品搜索匹配字段更新完成');
    
  } catch (error) {
    console.error('❌ 更新产品搜索匹配字段时出错:', error);
    console.error('💡 可能的解决方案:');
    console.error('   1. 检查MongoDB服务是否启动');
    console.error('   2. 检查.env文件中MONGODB_URI是否正确');
    console.error('   3. 确认数据库连接权限');
  } finally {
    // 🔌 确保关闭数据库连接
    if (mongoose.connection.readyState !== 0) {
      await mongoose.disconnect();
      console.log('🔌 数据库连接已关闭');
    }
  }
};

// 执行更新
updateProductSearchMatch(); 