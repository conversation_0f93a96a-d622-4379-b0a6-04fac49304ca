const feedbackService = require('../services/feedbackService');
const fileService = require('../services/fileService');
const validator = require('../utils/feedbackValidator');
const { success, error } = require('../utils/response');
const multer = require('multer');

// 使用内存存储来处理上传
const upload = multer({
  storage: multer.memoryStorage(),
  limits: {
    fileSize: 5 * 1024 * 1024, // 反馈图片最大5MB
  }
});

/**
 * @desc    提交反馈
 * @route   POST /api/v1/feedback
 * @access  Private
 */
const createFeedback = async (req, res) => {
  try {
    // 验证请求数据
    const { error: validationError, value } = validator.createFeedbackSchema.validate(req.body);
    
    if (validationError) {
      const errors = validationError.details.map(detail => ({
        field: detail.path[0],
        message: detail.message
      }));
      
      return error(res, 400, '请求参数错误', errors[0].message);
    }

    const userId = req.user.id;
    
    // 获取设备信息（从请求头或请求体中）
    const deviceInfo = {
      ...req.body.deviceInfo || {},
      userAgent: req.headers['user-agent'],
      ip: req.ip
    };

    // 创建反馈
    const feedback = await feedbackService.createFeedback(
      userId,
      value.content,
      value.type,
      value.images || [],
      deviceInfo
    );

    return success(res, 201, '反馈提交成功', {
      id: feedback._id
    });
  } catch (err) {
    return error(res, 500, '提交反馈失败', err.message);
  }
};

/**
 * @desc    获取用户的反馈列表
 * @route   GET /api/v1/feedback/my
 * @access  Private
 */
const getUserFeedbacks = async (req, res) => {
  try {
    // 验证请求数据
    const { error: validationError, value } = validator.getFeedbacksSchema.validate(req.query);
    
    if (validationError) {
      const errors = validationError.details.map(detail => ({
        field: detail.path[0],
        message: detail.message
      }));
      
      return error(res, 400, '请求参数错误', errors[0].message);
    }

    const userId = req.user.id;

    // 获取反馈列表
    const result = await feedbackService.getUserFeedbacks(
      userId,
      value.page,
      value.limit
    );

    return success(res, 200, '获取反馈列表成功', result);
  } catch (err) {
    return error(res, 500, '获取反馈列表失败', err.message);
  }
};

/**
 * @desc    获取所有反馈（管理员用）
 * @route   GET /api/v1/feedback/all
 * @access  Admin
 */
const getAllFeedbacks = async (req, res) => {
  try {
    // 验证请求数据
    const { error: validationError, value } = validator.getFeedbacksSchema.validate(req.query);
    
    if (validationError) {
      const errors = validationError.details.map(detail => ({
        field: detail.path[0],
        message: detail.message
      }));
      
      return error(res, 400, '请求参数错误', errors[0].message);
    }

    // 检查权限（确保是管理员）
    if (!req.user.isAdmin) {
      return error(res, 403, '没有权限执行此操作');
    }

    // 获取反馈列表
    const result = await feedbackService.getAllFeedbacks(
      value.page,
      value.limit,
      value.status,
      value.type
    );

    return success(res, 200, '获取反馈列表成功', result);
  } catch (err) {
    return error(res, 500, '获取反馈列表失败', err.message);
  }
};

/**
 * @desc    获取反馈详情
 * @route   GET /api/v1/feedback/:id
 * @access  Private/Admin
 */
const getFeedbackDetail = async (req, res) => {
  try {
    const { id } = req.params;
    const userId = req.user.id;
    
    // 获取反馈详情
    const feedback = await feedbackService.getFeedbackDetail(id);
    
    // 检查权限（用户只能查看自己的反馈，管理员可以查看所有）
    if (feedback.userId._id.toString() !== userId && !req.user.isAdmin) {
      return error(res, 403, '没有权限查看此反馈');
    }
    
    return success(res, 200, '获取反馈详情成功', feedback);
  } catch (err) {
    // 处理特定错误
    if (err.message === '反馈不存在' || err.message === '无效的反馈ID') {
      return error(res, 404, err.message);
    }
    return error(res, 500, '获取反馈详情失败', err.message);
  }
};

/**
 * @desc    更新反馈状态（管理员用）
 * @route   PATCH /api/v1/feedback/:id/status
 * @access  Admin
 */
const updateFeedbackStatus = async (req, res) => {
  try {
    // 验证请求数据
    const { error: validationError, value } = validator.updateFeedbackStatusSchema.validate(req.body);
    
    if (validationError) {
      const errors = validationError.details.map(detail => ({
        field: detail.path[0],
        message: detail.message
      }));
      
      return error(res, 400, '请求参数错误', errors[0].message);
    }

    const { id } = req.params;
    const adminId = req.user.id;
    
    // 检查权限（确保是管理员）
    if (!req.user.isAdmin) {
      return error(res, 403, '没有权限执行此操作');
    }
    
    // 更新状态
    await feedbackService.updateFeedbackStatus(id, value.status, adminId);
    
    return success(res, 200, '反馈状态更新成功');
  } catch (err) {
    // 处理特定错误
    if (err.message === '反馈不存在' || err.message === '无效的反馈ID') {
      return error(res, 404, err.message);
    }
    return error(res, 500, '更新反馈状态失败', err.message);
  }
};

/**
 * @desc    管理员回复反馈
 * @route   POST /api/v1/feedback/:id/reply
 * @access  Admin
 */
const replyFeedback = async (req, res) => {
  try {
    // 验证请求数据
    const { error: validationError, value } = validator.replyFeedbackSchema.validate(req.body);
    
    if (validationError) {
      const errors = validationError.details.map(detail => ({
        field: detail.path[0],
        message: detail.message
      }));
      
      return error(res, 400, '请求参数错误', errors[0].message);
    }

    const { id } = req.params;
    const adminId = req.user.id;
    
    // 检查权限（确保是管理员）
    if (!req.user.isAdmin) {
      return error(res, 403, '没有权限执行此操作');
    }
    
    // 回复反馈
    await feedbackService.replyFeedback(id, value.content, adminId);
    
    return success(res, 200, '反馈回复成功');
  } catch (err) {
    // 处理特定错误
    if (err.message === '反馈不存在' || err.message === '无效的反馈ID') {
      return error(res, 404, err.message);
    }
    return error(res, 500, '回复反馈失败', err.message);
  }
};

/**
 * @desc    上传反馈图片
 * @route   POST /api/v1/feedback/upload-images
 * @access  Private
 */
const uploadFeedbackImage = [
  upload.array('images', 6), // 最多上传6张图片
  async (req, res) => {
    try {
      if (!req.files || req.files.length === 0) {
        return error(res, 400, '请选择要上传的图片');
      }
      
      // 使用通用文件上传处理服务，带有特定的验证
      const uploadedFiles = await fileService.processFileUpload(req.files, {
        userId: req.user.id,
        module: 'feedback',
        isTemp: false, // 直接存为永久文件
        fileTypeValidation: (mimetype) => mimetype.startsWith('image/'),
        maxFileSize: 5 * 1024 * 1024 // 5MB限制
      });
      
      // 格式化响应数据（只需要返回必要信息）
      const images = uploadedFiles.map(file => ({
        id: file.id,
        file_url: file.file_url,
        thumbnail_url: file.thumbnail_url,
        width: file.width,
        height: file.height
      }));
      
      return success(res, 201, '反馈图片上传成功', {
        images,
        total: images.length,
        failed: req.files.length - images.length
      });
    } catch (err) {
      return error(res, 500, '上传反馈图片失败', err.message);
    }
  }
];

module.exports = {
  createFeedback,
  getUserFeedbacks,
  getAllFeedbacks,
  getFeedbackDetail,
  updateFeedbackStatus,
  replyFeedback,
  uploadFeedbackImage
}; 