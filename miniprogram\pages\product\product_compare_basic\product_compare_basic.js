// pages/product/product_compare_basic/product_compare_basic.js
const api = require('../../../utils/api');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    // 加载状态
    loading: false,
    // 错误信息
    error: null,
    // 对比结果
    compareResult: null,
    // 展开状态控制
    expandedCategories: {},
    // 产品名称列表
    productNames: []
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad(options) {
    console.log('传统对比页面加载参数:', options);
    
    if (options.productNames) {
      try {
        const productNames = JSON.parse(decodeURIComponent(options.productNames));
        console.log('解析的产品名称:', productNames);
        
        this.setData({
          productNames: productNames
        });
        
        // 开始对比
        this.startCompare(productNames);
      } catch (error) {
        console.error('解析产品名称失败:', error);
        this.setData({
          error: '参数解析失败，请重新选择产品进行对比'
        });
      }
    } else {
      this.setData({
        error: '缺少产品参数，请返回重新选择产品'
      });
    }
  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow() {
    // 启用分享功能
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });
  },

  /**
   * 开始产品对比
   */
  async startCompare(productNames) {
    if (!productNames || productNames.length < 2) {
      this.setData({
        error: '至少需要2个产品进行对比'
      });
      return;
    }

    this.setData({
      loading: true,
      error: null
    });

    try {
      console.log('调用传统对比API:', productNames);
      const result = await api.product.compareProductsBasic(productNames);
      console.log('传统对比结果:', result);

      if (result.success && result.data) {
        // 初始化展开状态 - 默认展开第一个分类
        const expandedCategories = {};
        const categories = Object.keys(result.data.rawComparisonData || {});
        if (categories.length > 0) {
          expandedCategories[categories[0]] = true;
        }

        this.setData({
          compareResult: result.data,
          expandedCategories: expandedCategories,
          loading: false
        });
      } else {
        throw new Error(result.message || '对比失败');
      }
    } catch (error) {
      console.error('产品对比失败:', error);
      this.setData({
        loading: false,
        error: error.message || '对比失败，请稍后重试'
      });
    }
  },

  /**
   * 切换分类展开状态
   */
  toggleCategory(e) {
    const { category } = e.currentTarget.dataset;
    const expandedCategories = { ...this.data.expandedCategories };
    expandedCategories[category] = !expandedCategories[category];
    
    this.setData({
      expandedCategories: expandedCategories
    });
  },

  /**
   * 重新加载
   */
  onRetry() {
    if (this.data.productNames.length > 0) {
      this.startCompare(this.data.productNames);
    }
  },

  /**
   * 分享对比结果
   */
  shareCompareResult() {
    const { productNames } = this.data;
    if (!productNames || productNames.length === 0) {
      wx.showToast({
        title: '暂无可分享的对比内容',
        icon: 'none'
      });
      return;
    }

    // 启用分享功能
    wx.showShareMenu({
      withShareTicket: true,
      menus: ['shareAppMessage', 'shareTimeline']
    });
    
    // 提示用户点击右上角分享
    wx.showToast({
      title: '请点击右上角菜单进行分享',
      icon: 'none',
      duration: 2500
    });
  },

  /**
   * 预览产品图片
   */
  previewProductImage(e) {
    const { imageUrl } = e.currentTarget.dataset;
    if (imageUrl) {
      wx.previewImage({
        urls: [imageUrl],
        current: imageUrl
      });
    }
  },

  /**
   * 查看产品详情
   */
  viewProductDetail(e) {
    const { productName } = e.currentTarget.dataset;
    if (productName) {
      wx.navigateTo({
        url: `/pages/product/product_detail/product_detail?productName=${encodeURIComponent(productName)}`
      });
    }
  },

  /**
   * 页面分享
   */
  onShareAppMessage() {
    const { productNames, compareResult } = this.data;
    
    if (!productNames || productNames.length === 0) {
      return {};
    }

    // 构建分享标题
    const title = productNames.length === 2 
      ? `${productNames[0]} VS ${productNames[1]} 传统对比`
      : `${productNames.length}款产品传统对比`;

    // 构建分享路径，包含产品参数
    const path = `/pages/product/product_compare_basic/product_compare_basic?productNames=${encodeURIComponent(JSON.stringify(productNames))}`;

    return {
      title: title,
      path: path,
      imageUrl: compareResult?.products?.[0]?.imageUrl || '', // 使用第一个产品的图片作为分享图
      success: () => {
        wx.showToast({
          title: '分享成功',
          icon: 'success'
        });
      },
      fail: (err) => {
        console.error('分享失败:', err);
        wx.showToast({
          title: '分享失败',
          icon: 'none'
        });
      }
    };
  },

  /**
   * 分享到朋友圈
   */
  onShareTimeline() {
    const { productNames, compareResult } = this.data;
    
    if (!productNames || productNames.length === 0) {
      return {};
    }

    // 构建分享标题
    const title = productNames.length === 2 
      ? `${productNames[0]} VS ${productNames[1]} 传统对比 - 选选助手`
      : `${productNames.length}款产品传统对比 - 选选助手`;

    // 构建分享路径
    const query = `productNames=${encodeURIComponent(JSON.stringify(productNames))}`;

    return {
      title: title,
      query: query,
      imageUrl: compareResult?.products?.[0]?.imageUrl || '', // 使用第一个产品的图片作为分享图
      success: () => {
        wx.showToast({
          title: '分享到朋友圈成功',
          icon: 'success'
        });
      },
      fail: (err) => {
        console.error('分享到朋友圈失败:', err);
        wx.showToast({
          title: '分享到朋友圈失败',
          icon: 'none'
        });
      }
    };
  },

  /**
   * 下拉刷新
   */
  onPullDownRefresh() {
    if (this.data.productNames.length > 0) {
      this.startCompare(this.data.productNames).finally(() => {
        wx.stopPullDownRefresh();
      });
    } else {
      wx.stopPullDownRefresh();
    }
  }
});
