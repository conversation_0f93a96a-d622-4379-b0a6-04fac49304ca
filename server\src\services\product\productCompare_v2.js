const NewProduct = require('../models/NewProduct');
const axios = require('axios');

/**
 * 产品服务 V2 - 基于 NewProduct 数据结构和 AI 智能对比
 * 支持全新的产品对比功能，使用 AI 生成详细的对比报告
 */

// DeepSeek API配置
const DEEPSEEK_API_KEY = process.env.DEEPSEEK_API_KEY || '***********************************';
const DEEPSEEK_API_BASE = process.env.DEEPSEEK_API_BASE || 'https://api.deepseek.com';
const DEEPSEEK_MODEL = process.env.DEEPSEEK_MODEL || 'deepseek-chat';

// 默认配置
const DEFAULT_CONFIG = {
  temperature: 0.4,
  maxTokens: 4000,
  timeout: 150000
};

/**
 * 调用DeepSeek API
 * @param {String} userPrompt 用户提示
 * @param {String} systemPrompt 系统提示
 * @param {Object} config 配置选项
 * @returns {Promise<String>} API响应内容
 */
async function callDeepSeekAPI(userPrompt, systemPrompt = null, config = {}) {
  try {
    const aiConfig = { ...DEFAULT_CONFIG, ...config };
    
    const baseEndpoint = DEEPSEEK_API_BASE.endsWith('/') 
      ? DEEPSEEK_API_BASE.slice(0, -1) 
      : DEEPSEEK_API_BASE;
    
    const url = `${baseEndpoint}/v1/chat/completions`;
    
    console.log(`🤖 正在调用DeepSeek AI进行智能产品分析...`);
    
    const messages = [];
    if (systemPrompt) {
      messages.push({ role: "system", content: systemPrompt });
    }
    messages.push({ role: "user", content: userPrompt });
    
    const response = await axios({
      url: url,
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${DEEPSEEK_API_KEY}`,
        'Content-Type': 'application/json',
      },
      data: {
        model: DEEPSEEK_MODEL,
        messages: messages,
        temperature: aiConfig.temperature,
        max_tokens: aiConfig.maxTokens,
        stream: false
      },
      timeout: aiConfig.timeout,
    });
    
    if (response.status === 200) {
      console.log(`✅ DeepSeek AI分析完成`);
      return response.data.choices[0].message.content;
    } else {
      throw new Error(`API请求失败: ${response.status} ${response.statusText}`);
    }
  } catch (error) {
    console.error('❌ DeepSeek API调用失败:', error.message);
    throw new Error(`DeepSeek AI分析失败: ${error.message}`);
  }
}

/**
 * 基于数据库产品数据生成智能对比报告
 * @param {Array} formattedProducts 格式化后的产品数据
 * @returns {Promise<String>} 对比分析报告
 */
async function generateProductComparisonReport(formattedProducts) {
  const systemPrompt = `你是一个资深的产品评测专家，擅长进行深入的产品对比分析。

请根据提供的产品信息，生成一份专业、全面的产品对比报告。

报告要求：
1. **技术规格对比**: 根据产品参数，详细分析各产品在不同方面的表现
   - 对于每个技术参数对比，要提供具体的分析和解释
   - 解释参数差异对实际使用体验的影响
   - 说明哪些参数更优以及为什么
   - 避免仅仅罗列参数，要深入分析参数背后的意义
2. **优缺点分析**: 客观评价每个产品的优势和不足
3. **使用场景推荐**: 根据不同用户需求推荐合适的产品
4. **购买建议**: 提供具体的购买建议和注意事项

报告风格：
- 专业客观，基于技术参数分析
- 语言通俗易懂，避免过于技术化
- 结构清晰，便于阅读
- 提供实用的建议
- 重点强调：每个技术规格对比部分都必须包含详细的文字解释和分析`;

  const userPrompt = `请基于以下从数据库获取的产品信息生成详细的对比分析报告：

产品数据：
${JSON.stringify(formattedProducts, null, 2)}

请生成一份完整的产品对比报告。`;
  return await callDeepSeekAPI(userPrompt, systemPrompt);
}

/**
 * 根据产品名称列表获取产品参数对比数据（V2版本 - AI智能对比）
 * @param {Array<String>} productNames 产品名称列表
 * @returns {Promise<Object>} 对比结果
 */
const compareProductsByNamesV2 = async (productNames) => {
  try {
    console.log('🔍 开始产品对比 V2 - AI智能分析');
    console.log('待对比产品:', productNames);

    // 1. 验证输入参数
    if (!Array.isArray(productNames) || productNames.length < 2) {
      return {
        success: false,
        error: '至少需要提供2个产品名称进行对比',
        data: null
      };
    }

    if (productNames.length > 6) {
      return {
        success: false,
        error: '最多支持6个产品同时对比',
        data: null
      };
    }

    // 2. 从 NewProduct 数据库中查找产品
    const findResult = await findProductsByNamesV2(productNames);
    
    if (!findResult.success) {
      return findResult;
    }

    const { products, notFoundProducts } = findResult.data;

    console.log(`✅ 找到 ${products.length} 个产品用于对比`);
    if (notFoundProducts.length > 0) {
      console.log(`⚠️ 未找到的产品: ${notFoundProducts.join(', ')}`);
    }

    // 3. 格式化产品数据用于 AI 分析
    const formattedProductData = products.map(product => formatProductForAI(product));

    // 4. 使用 AI 进行产品对比分析（直接基于数据库数据）
    console.log('🤖 调用 AI 进行智能产品对比分析...');
    console.log('格式化后的产品数据:', formattedProductData);
    const comparisonReport = await generateProductComparisonReport(formattedProductData);
    // const comparisonReport = 'AI 分析结果...';

    if (!comparisonReport) {
      return {
        success: false,
        error: 'AI 分析失败：未能生成对比报告',
        data: null
      };
    }

    // 5. 分析产品类别信息
    const productTypes = [...new Set(products.map(p => p.productType))];
    const productCategory = productTypes.length === 1 ? productTypes[0] : '混合类别';
    const isSameCategory = productTypes.length === 1;
    const crossCategoryNote = !isSameCategory ? 
      `检测到不同类型的产品: ${productTypes.join(', ')}，已进行跨类别对比分析` : null;

    // 6. 构建最终返回结果
    const result = {
      success: true,
      data: {
        // 基本信息
        requestedProducts: productNames,
        foundProducts: products.map(p => p.skuName),
        notFoundProducts: notFoundProducts,
        productCount: products.length,
        
        // 产品详细信息
        products: formattedProductData,
        
        // AI 对比分析结果
        aiAnalysis: {
          productCategory: productCategory,
          isSameCategory: isSameCategory,
          crossCategoryNote: crossCategoryNote,
          comparisonReport: comparisonReport,
          analysisTimestamp: new Date().toISOString(),
          dataSource: '数据库',
          aiModel: DEEPSEEK_MODEL
        },
        
        // 版本信息
        version: 'v2',
        analysisMethod: 'AI智能对比（基于数据库数据）'
      }
    };

    console.log('✅ 产品对比 V2 完成');
    return result;

  } catch (error) {
    console.error('❌ 产品对比 V2 失败:', error);
    return {
      success: false,
      error: `产品对比失败: ${error.message}`,
      data: null
    };
  }
};

/**
 * 从 NewProduct 数据库中查找产品（V2版本）
 * @param {Array<String>} productNames 产品名称列表
 * @returns {Promise<Object>} 查找结果
 */
const findProductsByNamesV2 = async (productNames) => {
  try {
    const products = [];
    const notFoundProducts = [];

    for (const productName of productNames) {
      console.log(`🔍 搜索产品: ${productName}`);
      
      // 使用智能搜索匹配产品
      const product = await findSingleProductByName(productName);
      
      if (product) {
        products.push(product);
        console.log(`✅ 找到产品: ${product.skuName}`);
      } else {
        notFoundProducts.push(productName);
        console.log(`❌ 未找到产品: ${productName}`);
      }
    }

    if (products.length < 2) {
      return {
        success: false,
        error: `找到的产品数量不足，无法进行对比。找到 ${products.length} 个，需要至少 2 个产品`,
        data: null
      };
    }

    // 检查产品类型是否一致（可选警告，不强制要求）
    const productTypes = [...new Set(products.map(p => p.productType))];
    let categoryWarning = null;
    
    if (productTypes.length > 1) {
      categoryWarning = `检测到不同类型的产品: ${productTypes.join(', ')}，AI 将进行跨类别对比分析`;
      console.log(`⚠️ ${categoryWarning}`);
    }

    return {
      success: true,
      data: {
        products,
        notFoundProducts,
        categoryWarning
      }
    };

  } catch (error) {
    console.error('查找产品失败:', error);
    return {
      success: false,
      error: `查找产品失败: ${error.message}`,
      data: null
    };
  }
};

/**
 * 根据产品名称查找单个产品（精确匹配 skuName）
 * @param {String} productName 产品名称
 * @returns {Promise<Object|null>} 产品对象或null
 */
const findSingleProductByName = async (productName) => {
  try {
    // 直接通过 skuName 精确匹配
    const product = await NewProduct.findOne({
      skuName: productName.trim()
    });
    
    return product;

  } catch (error) {
    console.error(`查找单个产品失败 (${productName}):`, error);
    return null;
  }
};

/**
 * 格式化产品数据用于 AI 分析
 * @param {Object} product NewProduct 对象
 * @returns {Object} 格式化后的产品数据
 */
const formatProductForAI = (product) => {
  // 获取默认配置
  const defaultConfig = product.configurations?.find(
    config => config.name === product.defaultConfiguration
  ) || product.configurations?.[0];

  // 构建显示名称
  let displayName = product.skuName;
  if (defaultConfig && product.configurations?.length > 1) {
    // 如果有多个配置，在名称中包含默认配置信息
    const configInfo = [];
    if (defaultConfig.specs?.存储扩展?.ROM容量) {
      configInfo.push(defaultConfig.specs.存储扩展.ROM容量);
    }
    if (configInfo.length > 0) {
      displayName = `${product.skuName}(${configInfo.join('/')})`;
    }
  }

  // 确定显示价格
  let displayPrice = null;
  let priceRange = null;
  
  if (product.configurations && product.configurations.length > 0) {
    const prices = product.configurations
      .filter(config => config.price && config.price > 0)
      .map(config => config.price);
    
    if (prices.length > 0) {
      const minPrice = Math.min(...prices);
      const maxPrice = Math.max(...prices);
      
      if (minPrice === maxPrice) {
        displayPrice = minPrice;
      } else {
        priceRange = `¥${minPrice} - ¥${maxPrice}`;
        displayPrice = defaultConfig?.price > 0 ? defaultConfig.price : minPrice;
      }
    }
  }

  return {
    // 基本信息
    id: product._id,
    skuId: product.skuId,
    name: product.skuName,
    displayName: displayName,
    brand: product.brandName,
    productType: product.productType,
    category: product.category,
    image: product.imageUrl,
    
    // 价格信息
    price: displayPrice,
    priceRange: priceRange,
    
    // 配置信息
    configurations: product.configurations || [],
    defaultConfiguration: product.defaultConfiguration,
    defaultConfigDetails: defaultConfig ? {
      name: defaultConfig.name,
      price: defaultConfig.price,
      available: defaultConfig.available,
      specs: defaultConfig.specs
    } : null,
    
    // 规格参数
    commonSpecs: product.commonSpecs || {},
    
    // 支持对比
    supportsComparison: product.supportsComparison,
    
    // 数据转换信息
    conversionInfo: product.conversionInfo
  };
};

/**
 * 转义正则表达式特殊字符
 * @param {String} str 要转义的字符串
 * @returns {String} 转义后的字符串
 */
const escapeRegex = (str) => {
  if (!str) return '';
  return str.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
};

module.exports = {
  compareProductsByNamesV2,
  findProductsByNamesV2,
  formatProductForAI
};