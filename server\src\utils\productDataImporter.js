const mongoose = require('mongoose');
const Product = require('../models/Product');

/**
 * 产品数据导入工具
 * 用于快速录入手机和电脑的产品参数
 */
class ProductDataImporter {
  constructor() {
    this.successCount = 0;
    this.errorCount = 0;
    this.errors = [];
  }

  /**
   * 导入手机产品数据
   * @param {Array} phoneData - 手机数据数组
   */
  async importPhones(phoneData) {
    console.log(`开始导入 ${phoneData.length} 个手机产品...`);
    
    for (const phone of phoneData) {
      try {
        await this.createPhoneProduct(phone);
        this.successCount++;
        console.log(`✅ 成功导入: ${phone.skuName}`);
      } catch (error) {
        this.errorCount++;
        this.errors.push({ product: phone.skuName, error: error.message });
        console.log(`❌ 导入失败: ${phone.skuName} - ${error.message}`);
      }
    }
  }

  /**
   * 导入笔记本电脑产品数据
   * @param {Array} laptopData - 笔记本数据数组
   */
  async importLaptops(laptopData) {
    console.log(`开始导入 ${laptopData.length} 个笔记本产品...`);
    
    for (const laptop of laptopData) {
      try {
        await this.createLaptopProduct(laptop);
        this.successCount++;
        console.log(`✅ 成功导入: ${laptop.skuName}`);
      } catch (error) {
        this.errorCount++;
        this.errors.push({ product: laptop.skuName, error: error.message });
        console.log(`❌ 导入失败: ${laptop.skuName} - ${error.message}`);
      }
    }
  }

  /**
   * 创建手机产品
   * @param {Object} phoneData - 手机数据
   */
  async createPhoneProduct(phoneData) {
    const productData = {
      skuId: phoneData.skuId,
      skuName: phoneData.skuName,
      productType: 'phone',
      price: phoneData.price,
      priceWan: phoneData.priceWan || '',
      imageUrl: phoneData.imageUrl,
      materialUrl: phoneData.materialUrl,
      couponUrl: phoneData.couponUrl || '',
      brandName: phoneData.brandName || '',
      brandCode: phoneData.brandCode || '',
      supportsComparison: true,

      // 产品参数规格
      productSpecs: {
        general: {
          brand: phoneData.brand,
          model: phoneData.model,
          color: phoneData.color,
          weight: phoneData.weight,
          dimensions: phoneData.dimensions
        },
        phone: {
          screenSize: phoneData.screenSize,
          screenResolution: phoneData.screenResolution,
          processor: phoneData.processor,
          ram: phoneData.ram,
          storage: phoneData.storage,
          battery: phoneData.battery,
          camera: {
            rear: phoneData.rearCamera,
            front: phoneData.frontCamera
          },
          operatingSystem: phoneData.operatingSystem,
          network: phoneData.network
        }
      },

      // 分类信息
      categoryInfo: phoneData.categoryInfo || {
        cid1: 9987,
        cid1Name: '手机',
        cid2: 653,
        cid2Name: '手机通讯',
        cid3: 655,
        cid3Name: '手机'
      },

      // 店铺信息
      shopInfo: phoneData.shopInfo || {}
    };

    const product = new Product(productData);
    return await product.save();
  }

  /**
   * 创建笔记本产品
   * @param {Object} laptopData - 笔记本数据
   */
  async createLaptopProduct(laptopData) {
    const productData = {
      skuId: laptopData.skuId,
      skuName: laptopData.skuName,
      productType: 'laptop',
      price: laptopData.price,
      priceWan: laptopData.priceWan || '',
      imageUrl: laptopData.imageUrl,
      materialUrl: laptopData.materialUrl,
      couponUrl: laptopData.couponUrl || '',
      brandName: laptopData.brandName || '',
      brandCode: laptopData.brandCode || '',
      supportsComparison: true,

      // 产品参数规格
      productSpecs: {
        general: {
          brand: laptopData.brand,
          model: laptopData.model,
          color: laptopData.color,
          weight: laptopData.weight,
          dimensions: laptopData.dimensions
        },
        laptop: {
          screenSize: laptopData.screenSize,
          screenResolution: laptopData.screenResolution,
          processor: laptopData.processor,
          ram: laptopData.ram,
          storage: laptopData.storage,
          graphics: laptopData.graphics,
          operatingSystem: laptopData.operatingSystem,
          ports: laptopData.ports,
          battery: laptopData.battery
        }
      },

      // 分类信息
      categoryInfo: laptopData.categoryInfo || {
        cid1: 670,
        cid1Name: '电脑办公',
        cid2: 671,
        cid2Name: '电脑整机',
        cid3: 672,
        cid3Name: '笔记本'
      },

      // 店铺信息
      shopInfo: laptopData.shopInfo || {}
    };

    const product = new Product(productData);
    return await product.save();
  }

  /**
   * 批量导入产品数据（支持upsert模式）
   * @param {Object} data 产品数据对象 {phones: [], laptops: []}
   * @param {Object} options 导入选项
   */
  async batchImport(data, options = {}) {
    const { 
      upsert = true,           // 默认启用upsert模式
      skipDuplicates = true,   // 跳过重复数据
      batchSize = 10          // 批处理大小
    } = options;

    const results = {
      success: 0,
      failed: 0,
      errors: []
    };

    try {
      console.log('🚀 开始批量导入产品数据...');
      console.log(`📋 导入配置: upsert=${upsert}, skipDuplicates=${skipDuplicates}, batchSize=${batchSize}`);
      
      // 导入手机数据
      if (data.phones && data.phones.length > 0) {
        console.log(`\n📱 开始导入 ${data.phones.length} 个手机产品...`);
        const phoneResults = await this.batchImportPhones(data.phones, { upsert, skipDuplicates, batchSize });
        results.success += phoneResults.success;
        results.failed += phoneResults.failed;
        results.errors.push(...phoneResults.errors);
      }

      // 导入笔记本数据
      if (data.laptops && data.laptops.length > 0) {
        console.log(`\n💻 开始导入 ${data.laptops.length} 个笔记本产品...`);
        const laptopResults = await this.batchImportLaptops(data.laptops, { upsert, skipDuplicates, batchSize });
        results.success += laptopResults.success;
        results.failed += laptopResults.failed;
        results.errors.push(...laptopResults.errors);
      }

      // 输出统计结果
      console.log('\n📊 导入结果统计:');
      console.log(`✅ 成功导入: ${results.success} 个产品`);
      console.log(`❌ 导入失败: ${results.failed} 个产品`);

      if (results.errors.length > 0) {
        console.log('\n🔍 错误详情:');
        results.errors.forEach((error, index) => {
          console.log(`${index + 1}. ${error.product}: ${error.message}`);
        });
      }

      return results;

    } catch (error) {
      console.error('❌ 批量导入过程失败:', error);
      throw error;
    }
  }

  /**
   * 批量导入手机数据
   * @param {Array} phones 手机数据数组
   * @param {Object} options 导入选项
   */
  async batchImportPhones(phones, options = {}) {
    return await this.batchImportProducts(phones, 'phone', options);
  }

  /**
   * 批量导入笔记本数据
   * @param {Array} laptops 笔记本数据数组
   * @param {Object} options 导入选项
   */
  async batchImportLaptops(laptops, options = {}) {
    return await this.batchImportProducts(laptops, 'laptop', options);
  }

  /**
   * 通用批量导入产品数据
   * @param {Array} products 产品数据数组
   * @param {String} productType 产品类型
   * @param {Object} options 导入选项
   */
  async batchImportProducts(products, productType, options = {}) {
    const { upsert = true, skipDuplicates = true, batchSize = 10 } = options;
    const results = { success: 0, failed: 0, errors: [] };

    // 分批处理
    for (let i = 0; i < products.length; i += batchSize) {
      const batch = products.slice(i, i + batchSize);
      console.log(`📦 处理批次 ${Math.floor(i / batchSize) + 1}/${Math.ceil(products.length / batchSize)}: ${batch.length} 个产品`);

      // 并行处理当前批次
      const batchPromises = batch.map(async (productData) => {
        try {
          // 🆕 数据验证步骤
          this.validateProductData(productData, productType);
          
          if (upsert) {
            const result = await this.upsertProduct(productData, productType);
            return { success: true, result, product: productData.skuName };
          } else {
            const result = await this.createProduct(productData, productType);
            return { success: true, result, product: productData.skuName };
          }
        } catch (error) {
          if (skipDuplicates && error.code === 11000) {
            console.log(`⚠️ 跳过重复产品: ${productData.skuName}`);
            return { success: true, skipped: true, product: productData.skuName };
          }
          return { success: false, error, product: productData.skuName };
        }
      });

      const batchResults = await Promise.allSettled(batchPromises);

      // 统计当前批次结果
      batchResults.forEach((result, index) => {
        if (result.status === 'fulfilled') {
          if (result.value.success) {
            if (!result.value.skipped) {
              results.success++;
              console.log(`✅ 成功: ${result.value.product}`);
            }
          } else {
            results.failed++;
            results.errors.push({
              product: result.value.product,
              message: result.value.error.message
            });
            console.log(`❌ 导入失败: ${result.value.product} - ${result.value.error.message}`);
          }
        } else {
          results.failed++;
          const productName = batch[index]?.skuName || `批次${Math.floor(i / batchSize) + 1}-${index + 1}`;
          results.errors.push({
            product: productName,
            message: result.reason.message
          });
          console.log(`❌ 处理失败: ${productName} - ${result.reason.message}`);
        }
      });

      // 批次间的短暂延迟，避免数据库压力过大
      if (i + batchSize < products.length) {
        await new Promise(resolve => setTimeout(resolve, 100));
      }
    }

    return results;
  }

  /**
   * Upsert产品（存在则更新，不存在则创建）
   * @param {Object} productData 产品数据
   * @param {String} productType 产品类型
   */
  async upsertProduct(productData, productType) {
    const transformedData = this.transformProductData(productData, productType);
    
    const result = await Product.findOneAndUpdate(
      { skuId: productData.skuId },
      transformedData,
      { 
        upsert: true, 
        new: true, 
        runValidators: true,
        setDefaultsOnInsert: true 
      }
    );
    
    return result;
  }

  /**
   * 创建产品（传统方式）
   * @param {Object} productData 产品数据
   * @param {String} productType 产品类型
   */
  async createProduct(productData, productType) {
    const transformedData = this.transformProductData(productData, productType);
    const product = new Product(transformedData);
    return await product.save();
  }

  /**
   * 转换产品数据为统一格式
   * 支持新的嵌套数据格式和多配置模式
   * @param {Object} productData 原始产品数据
   * @param {String} productType 产品类型
   */
  transformProductData(productData, productType) {
    // 🆕 检测数据格式类型
    const isNewFormat = productData.productSpecs && productData.configurations;
    
    if (isNewFormat) {
      // 🆕 新格式数据：直接使用，只需要确保必要字段存在
      console.log(`📋 检测到新格式数据: ${productData.skuName}`);
      
      const transformedData = {
        skuId: productData.skuId,
        skuName: productData.skuName,
        productType: productType,
        imageUrl: productData.imageUrl,
        brandName: productData.brandName || '',
        supportsComparison: productData.supportsComparison !== false,
        
        // 🆕 新特性：多配置支持
        configurations: productData.configurations || [],
        defaultConfiguration: productData.defaultConfiguration,
        priceRange: productData.priceRange,
        
        // 🆕 完整的产品规格
        productSpecs: productData.productSpecs || {},
        
        // 向后兼容：设置基础价格
        price: productData.price || (productData.priceRange ? productData.priceRange.min : 0)
      };
      
      // 🆕 手动生成searchMatch字段
      transformedData.searchMatch = this.generateSearchMatch(
        transformedData.skuName,
        transformedData.productSpecs?.general?.brand
      );
      
      return transformedData;
    } else {
      // 🔄 旧格式数据：使用原有的转换逻辑
      console.log(`📋 检测到旧格式数据，执行转换: ${productData.skuName}`);
      
      const baseData = {
        skuId: productData.skuId,
        skuName: productData.skuName,
        productType: productType,
        price: productData.price,
        imageUrl: productData.imageUrl,
        brandName: productData.brandName || '',
        supportsComparison: true,

        // 🔄 旧格式：构建产品参数规格
        productSpecs: {
          general: {
            brand: productData.brand || productData.brandName,
            model: productData.model,
            color: productData.color,
            weight: productData.weight,
            dimensions: productData.dimensions
          }
        }
      };

      // 根据产品类型添加专用参数
      if (productType === 'phone') {
        baseData.productSpecs.phone = {
          screenSize: productData.screenSize,
          screenResolution: productData.screenResolution,
          processor: productData.processor,
          battery: productData.battery,
          camera: {
            rear: productData.rearCamera || productData.camera?.rear,
            front: productData.frontCamera || productData.camera?.front
          },
          operatingSystem: productData.operatingSystem,
          network: productData.network
        };
      } else if (productType === 'laptop') {
        baseData.productSpecs.laptop = {
          screenSize: productData.screenSize,
          screenResolution: productData.screenResolution,
          processor: productData.processor,
          graphics: productData.graphics,
          operatingSystem: productData.operatingSystem,
          ports: productData.ports,
          battery: productData.battery
        };
      }
      
      // 🆕 为旧格式数据创建单一配置
      if (productData.price) {
        baseData.configurations = [{
          name: 'default',
          storage: productData.storage || 'N/A',
          ram: productData.ram || 'N/A',
          price: productData.price,
          available: true
        }];
        baseData.defaultConfiguration = 'default';
        baseData.priceRange = {
          min: productData.price,
          max: productData.price
        };
      }

      // 🆕 手动生成searchMatch字段
      baseData.searchMatch = this.generateSearchMatch(
        baseData.skuName,
        baseData.productSpecs.general.brand
      );

      return baseData;
    }
  }

  /**
   * 生成搜索匹配字段
   * @param {String} skuName - 产品SKU名称
   * @param {String} brand - 产品品牌
   * @returns {String} 处理后的搜索匹配字符串
   */
  generateSearchMatch(skuName, brand) {
    const sku = skuName || '';
    const brandName = brand || '';
    
    // 拼接skuName和brand，去除空格和标点符号，保留中文字符
    const combined = (brandName + sku)
      .replace(/[\s.,!?;:()\[\]{}'""`''""、。，！？；：（）【】《》\-_+=]/g, '') // 只移除空格和常见标点符号
      .toLowerCase(); // 转为小写便于搜索
    
    return combined;
  }

  /**
   * 验证产品数据格式
   * 支持新旧两种数据格式的验证
   * @param {Object} productData - 产品数据
   * @param {String} type - 产品类型 (phone/laptop)
   */
  validateProductData(productData, type) {
    // 🆕 基础必需字段验证
    const basicRequiredFields = ['skuId', 'skuName', 'imageUrl'];
    
    for (const field of basicRequiredFields) {
      if (!productData[field]) {
        throw new Error(`缺少必需字段: ${field}`);
      }
    }

    // 🆕 检测数据格式
    const isNewFormat = productData.productSpecs && productData.configurations;
    
    if (isNewFormat) {
      // 🆕 新格式数据验证
      console.log(`🔍 验证新格式数据: ${productData.skuName}`);
      
      // 验证configurations数组
      if (!Array.isArray(productData.configurations) || productData.configurations.length === 0) {
        throw new Error('新格式数据必须包含有效的configurations数组');
      }
      
      // 验证每个配置项
      for (const config of productData.configurations) {
        if (!config.name || typeof config.price !== 'number' || config.price <= 0) {
          throw new Error(`配置项格式错误: ${JSON.stringify(config)}`);
        }
      }
      
      // 验证价格范围
      if (productData.priceRange) {
        if (typeof productData.priceRange.min !== 'number' || typeof productData.priceRange.max !== 'number') {
          throw new Error('价格范围格式错误');
        }
        if (productData.priceRange.min > productData.priceRange.max) {
          throw new Error('价格范围最小值不能大于最大值');
        }
      }
      
      // 验证产品规格
      if (!productData.productSpecs || typeof productData.productSpecs !== 'object') {
        throw new Error('新格式数据必须包含productSpecs对象');
      }
      
    } else {
      // 🔄 旧格式数据验证
      console.log(`🔍 验证旧格式数据: ${productData.skuName}`);
      
      const oldRequiredFields = ['price'];
      
      for (const field of oldRequiredFields) {
        if (!productData[field]) {
          throw new Error(`旧格式数据缺少必需字段: ${field}`);
        }
      }

      // 验证价格
      if (typeof productData.price !== 'number' || productData.price <= 0) {
        throw new Error('价格必须是大于0的数字');
      }
    }

    // 🆕 产品类型验证
    if (!['phone', 'laptop', 'tablet', 'headphones', 'smartwatch', 'other'].includes(type)) {
      throw new Error(`不支持的产品类型: ${type}`);
    }

    console.log(`✅ 数据验证通过: ${productData.skuName}`);
    return true;
  }
}

module.exports = ProductDataImporter; 