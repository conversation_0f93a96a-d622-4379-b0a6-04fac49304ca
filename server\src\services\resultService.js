const mongoose = require('mongoose');
const Question = require('../models/Question');
const Answer = require('../models/Answer');
const Result = require('../models/Result');
const Comment = require('../models/Comment');
const { generateTextWithHuggingFace } = require('../utils/aiService');
const { generateVotingAnalysis } = require('../utils/aiHelper');
const HUGGINGFACE_DEFAULT_MODEL = process.env.HUGGINGFACE_DEFAULT_MODEL;
const DEFAULT_AI_PROVIDER = process.env.DEFAULT_AI_PROVIDER || 'deepseek';

// 添加处理中的任务集合，用于去重
const processingTasks = new Set();

/**
 * 获取缓存的问题结果分析
 * @param {String} questionId 问题ID
 * @param {String} summaryType 总结类型，可选值：rule, ai
 * @returns {Promise<Object|null>} 缓存的问题结果分析，如果没有缓存则返回null
 */
const getCachedQuestionResult = async (questionId, summaryType) => {
  // 验证ObjectId有效性
  if (!mongoose.Types.ObjectId.isValid(questionId)) {
    throw new Error('无效的问题ID');
  }

  // 检查是否已有缓存的结果
  const existingResult = await Result.findOne({ 
    questionId,
    summaryType
  }).lean();
  
  if (existingResult) {
    console.log(`从缓存获取问题${summaryType}总结结果`);
    return existingResult.data;
  }
  
  return null;
};

/**
 * 检查问题是否正在处理中
 * @param {String} questionId 问题ID
 * @returns {Boolean} 是否正在处理中
 */
const isTaskProcessing = (questionId) => {
  return processingTasks.has(questionId.toString());
};

/**
 * 异步生成AI结果分析
 * @param {String} questionId 问题ID
 * @param {String} provider AI提供商，可选值：huggingface, azure
 * @returns {Promise<void>}
 */
const generateAIResultAsync = async (questionId, provider = DEFAULT_AI_PROVIDER) => {
  // 将questionId转为字符串，确保一致性
  const questionIdStr = questionId.toString();

  console.log("提供商", provider);
  
  // 检查该问题是否已经在处理中
  if (processingTasks.has(questionIdStr)) {
    console.log(`问题(${questionIdStr})的AI分析已在处理中，跳过重复生成`);
    return;
  }
  
  // 标记为处理中
  processingTasks.add(questionIdStr);
  console.log(`开始异步生成问题(${questionIdStr})的AI总结结果`);
  
  try {
    // 执行AI生成过程
    await getQuestionResultWithAI(questionId, provider);
    console.log(`异步生成问题(${questionIdStr})的AI总结结果完成`);
  } catch (err) {
    console.error(`异步生成问题(${questionIdStr})的AI总结结果失败:`, err.message);
  } finally {
    // 无论成功失败，都从处理中集合移除
    processingTasks.delete(questionIdStr);
  }
};

/**
 * 获取问题的结果分析（规则方式）
 * @param {String} questionId 问题ID
 * @returns {Promise<Object>} 问题的结果分析
 */
const getQuestionResult = async (questionId) => {
  console.log('开始获取问题规则总结结果');
  // 验证ObjectId有效性
  if (!mongoose.Types.ObjectId.isValid(questionId)) {
    throw new Error('无效的问题ID');
  }

  // 获取问题信息
  const question = await Question.findById(questionId).lean();
  if (!question) {
    throw new Error('问题不存在');
  }

  // 检查是否已有缓存的结果
  const existingResult = await getCachedQuestionResult(questionId, 'rule');
  
  if (existingResult) {
    return existingResult;
  }

  // 获取所有选项及其投票数据
  const optionsData = question.options.map(option => ({
    id: option._id,
    content: option.content,
    voteCount: option.voteCount,
    percentage: question.totalVotes > 0 
      ? Math.round((option.voteCount / question.totalVotes) * 100) 
      : 0
  }));

  // 排序选项（按照投票数降序）
  optionsData.sort((a, b) => b.voteCount - a.voteCount);

  // 获取热门理由（每个选项最多3个点赞最高的理由）
  const topReasons = [];
  for (const option of optionsData) {
    // 获取该选项的回答，按点赞数排序
    const answers = await Answer.find({
      questionId,
      optionId: option.id,
      content: { $ne: '' }  // 只获取有内容的回答
    })
    .sort({ likes: -1 })
    .limit(3)
    .populate({
      path: 'userId',
      select: 'nickname avatar',
      options: { lean: true }
    })
    .lean();

    // 格式化热门理由
    const reasons = answers.map(answer => ({
      id: answer._id,
      content: answer.content,
      likes: answer.likes,
      optionId: option.id,
      optionContent: option.content,
      user: answer.isAnonymous ? 
        { nickname: '匿名用户', avatar: '/assets/images/default-avatar.png' } : 
        {
          nickname: answer.userId.nickname,
          avatar: answer.userId.avatar
        }
    }));

    topReasons.push(...reasons);
  }

  // 基础数据
  const baseData = {
    totalVotes: question.totalVotes,
    winningOption: optionsData.length > 0 ? optionsData[0] : null,
    votingTrend: optionsData,
    topReasons: topReasons
  };
  
  // 生成决策报告
  const ruleBasedSummary = generateRuleBasedSummary(question, baseData);
  
  const resultData = {
    questionId: question._id,
    title: question.title,
    status: question.status,
    createdAt: question.createdAt,
    expiryTime: question.expiryTime,
    baseData,
    summary: ruleBasedSummary
  };

  // 如果问题已关闭，缓存结果
  if (question.status === 'closed') {
    await Result.findOneAndUpdate(
      { questionId, summaryType: 'rule' },
      { data: resultData, lastUpdated: new Date() },
      { upsert: true, new: true }
    );
    console.log('问题规则总结结果已缓存');
  }

  return resultData;
};

/**
 * 基于规则生成决策报告
 * @param {Object} question 问题对象
 * @param {Object} baseData 基础数据
 * @returns {String} 决策报告文本
 */
const generateRuleBasedSummary = (question, baseData) => {
  const { totalVotes, winningOption, votingTrend, topReasons } = baseData;
  
  // 构建报告文本
  let report = `# "${question.title}" 投票结果分析\n\n`;
  
  // 总体情况
  report += `## 总体情况\n\n`;
  report += `- 总投票人数：${totalVotes}人\n`;
  if (winningOption) {
    report += `- 得票最高选项：${winningOption.content}（${winningOption.voteCount}票，占比${winningOption.percentage}%）\n\n`;
  } else {
    report += `- 暂无投票\n\n`;
  }
  
  // 各选项情况
  report += `## 各选项得票情况\n\n`;
  votingTrend.forEach((option, index) => {
    report += `${index + 1}. ${option.content}：${option.voteCount}票（${option.percentage}%）\n`;
  });
  report += `\n`;
  
  // 主要支持理由
  report += `## 用户选择理由分析\n\n`;
  const reasonsByOption = {};
  
  // 按选项整理理由
  topReasons.forEach(reason => {
    if (!reasonsByOption[reason.optionContent]) {
      reasonsByOption[reason.optionContent] = [];
    }
    reasonsByOption[reason.optionContent].push(reason);
  });
  
  // 输出每个选项的主要理由
  Object.keys(reasonsByOption).forEach(optionContent => {
    report += `### ${optionContent} 支持理由：\n\n`;
    reasonsByOption[optionContent].forEach(reason => {
      report += `- ${reason.content}（${reason.likes}个赞）\n`;
    });
    report += `\n`;
  });
  
  // 决策建议
  report += `## 决策建议\n\n`;
  if (winningOption) {
    report += `基于${totalVotes}人的投票结果，${winningOption.content}是最受欢迎的选择，得到了${winningOption.percentage}%的支持率。`;
    report += `如果您注重大多数人的选择，可以考虑这个选项。`;
    
    // 如果有其他选项且票数接近，提供补充建议
    if (votingTrend.length > 1 && votingTrend[1].percentage > 30) {
      report += `不过，${votingTrend[1].content}也获得了${votingTrend[1].percentage}%的支持率，`;
      report += `如果您有特殊需求，也可以将其作为备选方案。`;
    }
  } else {
    report += `目前尚无足够的投票数据提供明确建议，建议等待更多用户参与后再做决策。`;
  }
  
  return report;
};

/**
 * 使用AI获取问题的结果分析
 * @param {String} questionId 问题ID
 * @param {String} provider AI提供商，可选值：huggingface, azure, deepseek
 * @returns {Promise<Object>} 问题的AI结果分析
 */
const getQuestionResultWithAI = async (questionId, provider = DEFAULT_AI_PROVIDER) => {
  // 验证ObjectId有效性
  if (!mongoose.Types.ObjectId.isValid(questionId)) {
    throw new Error('无效的问题ID');
  }

  // 获取问题信息
  const question = await Question.findById(questionId).lean();
  if (!question) {
    throw new Error('问题不存在');
  }

  // 检查是否已有缓存的AI结果
  const existingResult = await getCachedQuestionResult(questionId, 'ai');
  
  if (existingResult) {
    return existingResult;
  }

  // 获取所有选项及其投票数据
  const optionsData = question.options.map(option => ({
    id: option._id,
    content: option.content,
    voteCount: option.voteCount,
    percentage: question.totalVotes > 0 
      ? Math.round((option.voteCount / question.totalVotes) * 100) 
      : 0
  }));

  // 排序选项（按照投票数降序）
  optionsData.sort((a, b) => b.voteCount - a.voteCount);
  
  // 基础数据
  const baseData = {
    totalVotes: question.totalVotes,
    winningOption: optionsData.length > 0 ? optionsData[0] : null,
    votingTrend: optionsData
  };
  
  // 获取每个选项的详细回答数据（每个选项最多15个有内容的回答）
  const optionsAnswers = [];
  for (const option of optionsData) {
    // 获取该选项的回答，按点赞数排序
    const answers = await Answer.find({
      questionId,
      optionId: option.id,
      content: { $ne: '' }  // 只获取有内容的回答
    })
    .sort({ likes: -1 })
    .limit(15)  // 增加回答数量限制
    .populate({
      path: 'userId',
      select: 'nickname',
      options: { lean: true }
    })
    .lean();

    // 获取所有回答的评论信息
    const answersWithComments = [];
    for (const answer of answers) {
      let comments = [];
      try {
        // 获取该回答的所有评论，不设置点赞数限制
        comments = await Comment.find({
          answerId: answer._id,
          isDeleted: false,
          parentId: null  // 只获取顶级评论
        })
        .sort({ createdAt: -1 })  // 按创建时间降序排序
        .limit(5)  // 增加每个回答的评论数量限制
        .populate({
          path: 'userId',
          select: 'nickname',
          options: { lean: true }
        })
        .lean();
      } catch (err) {
        console.log('获取评论失败:', err.message);
        comments = [];
      }
      
      // 格式化评论
      const formattedComments = comments.map(comment => ({
        content: comment.content,
        isAnonymous: comment.isAnonymous,
        user: comment.isAnonymous ? '匿名用户' : (comment.userId ? comment.userId.nickname : '未知用户'),
        createdAt: comment.createdAt
      }));
      
      // 添加到回答中
      answersWithComments.push({
        content: answer.content,
        likes: answer.likes,
        isAnonymous: answer.isAnonymous,
        user: answer.isAnonymous ? '匿名用户' : (answer.userId ? answer.userId.nickname : '未知用户'),
        comments: formattedComments,
        createdAt: answer.createdAt
      });
    }
    
    // 添加到选项回答集合
    optionsAnswers.push({
      optionId: option.id,
      optionContent: option.content,
      voteCount: option.voteCount,
      percentage: option.percentage,
      answers: answersWithComments
    });
  }
  
  // 获取问题详细信息
  const questionDetails = {
    title: question.title,
    description: question.description || '',
    scene: question.scene || '',
    keyFactors: question.keyFactors || '',
    budget: question.budget || {},
    tags: question.tags || [],
    category: question.category || '',
    createdAt: question.createdAt,
    expiryTime: question.expiryTime
  };
  
  // 构建输入提示
  let prompt = `请对以下电子消费品选择投票结果进行客观总结分析:\n\n`;
  
  // 添加问题完整信息
  prompt += `## 问题信息\n`;
  prompt += `**标题**: ${questionDetails.title}\n`;
  if (questionDetails.description) {
    prompt += `**详细描述**: ${questionDetails.description}\n`;
  }
  if (questionDetails.category) {
    prompt += `**产品类别**: ${questionDetails.category}\n`;
  }
  if (questionDetails.scene) {
    prompt += `**使用场景**: ${questionDetails.scene}\n`;
  }
  if (questionDetails.keyFactors) {
    prompt += `**关键考量因素**: ${questionDetails.keyFactors}\n`;
  }
  if (questionDetails.budget && (questionDetails.budget.min || questionDetails.budget.max)) {
    const budgetInfo = [];
    if (questionDetails.budget.min) budgetInfo.push(`${questionDetails.budget.min}`);
    if (questionDetails.budget.max) budgetInfo.push(`${questionDetails.budget.max}`);
    const budgetRange = budgetInfo.length === 2 ? `${budgetInfo[0]}-${budgetInfo[1]}` : budgetInfo[0];
    prompt += `**预算范围**: ${budgetRange}${questionDetails.budget.currency || 'CNY'}\n`;
  }
  if (questionDetails.tags && questionDetails.tags.length > 0) {
    prompt += `**相关标签**: ${questionDetails.tags.join(', ')}\n`;
  }
  prompt += `**创建时间**: ${new Date(questionDetails.createdAt).toLocaleString('zh-CN')}\n`;
  if (questionDetails.expiryTime) {
    prompt += `**截止时间**: ${new Date(questionDetails.expiryTime).toLocaleString('zh-CN')}\n`;
  }
  
  // 添加投票结果和用户讨论
  prompt += `\n## 投票结果与用户讨论\n`;
  prompt += `**总投票人数**: ${baseData.totalVotes}人\n\n`;
  
  // 添加详细的选项信息和用户讨论
  for (const option of optionsAnswers) {
    prompt += `### 选项: ${option.optionContent}\n`;
    prompt += `**得票情况**: ${option.voteCount}票 (${option.percentage}%)\n\n`;
    
    // 添加用户支持理由和讨论
    if (option.answers && option.answers.length > 0) {
      prompt += `**用户选择理由与讨论**:\n\n`;
      for (const [index, answer] of option.answers.entries()) {
        const userInfo = answer.user;
        const answerTime = new Date(answer.createdAt).toLocaleString('zh-CN');
        prompt += `${index + 1}. **${userInfo}** (${answerTime}) [${answer.likes}个赞]:\n`;
        prompt += `   "${answer.content}"\n\n`;
        
        // 添加该理由下的所有评论讨论
        if (answer.comments && answer.comments.length > 0) {
          prompt += `   用户讨论:\n`;
          for (const comment of answer.comments) {
            const commentTime = new Date(comment.createdAt).toLocaleString('zh-CN');
            prompt += `   - **${comment.user}** (${commentTime}): "${comment.content}"\n`;
          }
          prompt += `\n`;
        }
      }
    } else {
      prompt += `**用户选择理由**: 暂无详细理由\n\n`;
    }
    
    prompt += `---\n\n`;
  }
  
  // 修改分析要求，专注于总结而非建议
  prompt += `## 总结要求\n`;
  prompt += `请基于以上完整的投票信息和用户讨论，进行客观的数据总结分析。重点关注:\n\n`;
  prompt += `1. **投票结果概况**: 各选项的得票情况和支持度分布\n`;
  prompt += `2. **用户观点总结**: 每个选项获得支持的主要原因和用户关注点\n`;
  prompt += `3. **讨论热点分析**: 用户讨论中的争议点和共识点\n`;
  prompt += `4. **用户关注焦点**: 从所有发言中提取用户最关心的产品特性和使用需求\n\n`;
  prompt += `**注意**: 请客观总结用户的真实发言和投票数据，不要添加主观建议或推荐意见。以数据和用户观点为准，保持中立客观的分析态度。输出格式使用清晰的Markdown格式。`;
  
  // 调用AI模型生成投票分析
  let aiSummary;
  try {
    if (provider === 'huggingface') {
      // HuggingFace保持原有调用方式
      aiSummary = await generateTextWithHuggingFace(
        { inputs: prompt },
        HUGGINGFACE_DEFAULT_MODEL
      );
    } else {
      // 使用封装好的投票分析函数
      aiSummary = await generateVotingAnalysis(prompt, provider);
    }
    
    // 如果返回为空，使用备用消息
    if (!aiSummary) {
      aiSummary = "# 投票结果总结\n\nAI未能生成有效的总结，请查看投票数据和用户讨论内容。";
    }
  } catch (err) {
    console.error('AI总结生成失败:', err);
    aiSummary = "# 投票结果总结\n\nAI总结生成失败，请查看投票数据或稍后再试。";
  }
  
  // 构建AI结果数据
  const aiResultData = {
    questionId: question._id,
    title: question.title,
    status: question.status,
    createdAt: question.createdAt,
    expiryTime: question.expiryTime,
    baseData,
    summary: aiSummary  // AI生成的客观总结报告
  };
  
  // 无论问题状态如何，都缓存AI结果 (为了提高性能)
  await Result.findOneAndUpdate(
    { questionId, summaryType: 'ai' },
    { data: aiResultData, lastUpdated: new Date() },
    { upsert: true, new: true }
  );
  console.log('问题AI总结结果已缓存');
  
  return aiResultData;
};

/**
 * 清除问题结果缓存
 * @param {String} questionId 问题ID
 * @param {String} summaryType 总结类型，可选值：rule, ai, all(默认)
 * @returns {Promise<void>}
 */
const clearQuestionResultCache = async (questionId, summaryType = 'all') => {
  if (!mongoose.Types.ObjectId.isValid(questionId)) {
    throw new Error('无效的问题ID');
  }
  
  const query = { questionId };
  
  // 如果指定了总结类型且不是'all'，只清除该类型的缓存
  if (summaryType !== 'all') {
    query.summaryType = summaryType;
  }
  
  await Result.deleteMany(query);
  console.log(`问题结果缓存已清除 (类型: ${summaryType})`);
};

module.exports = {
  getQuestionResult,
  getQuestionResultWithAI,
  clearQuestionResultCache,
  getCachedQuestionResult,
  generateAIResultAsync,
  isTaskProcessing
}; 