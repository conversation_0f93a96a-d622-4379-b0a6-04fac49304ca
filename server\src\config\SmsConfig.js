/**
 * 短信服务配置
 * 注意: 此文件仅用于开发环境模拟短信服务
 * 生产环境应使用环境变量配置敏感信息
 */
module.exports = {
  // 是否使用模拟短信服务（开发环境使用）
  useMockSms: true,
  
  // 默认模拟验证码（所有验证码都返回123456）
  mockVerifyCode: '123456',

  // 短信签名
  signName: '宠物社区',
  
  // 短信服务提供商（阿里云/腾讯云）
  provider: 'aliyun',
  
  // 阿里云配置（实际项目中应从环境变量读取）
  accessKeyId: 'YOUR_ACCESS_KEY_ID',
  accessKeySecret: 'YOUR_ACCESS_KEY_SECRET',
  
  // 短信模板代码
  templates: {
    register: 'SMS_TEMPLATE_REGISTER',
    login: 'SMS_TEMPLATE_LOGIN',
    resetPassword: 'SMS_TEMPLATE_RESET_PASSWORD',
    changePhone: 'SMS_TEMPLATE_CHANGE_PHONE',
    withdraw: 'SMS_TEMPLATE_WITHDRAW'
  },
  
  // 频率限制配置
  rateLimit: {
    perMinute: 1,  // 每分钟允许发送1条
    perHour: 5,    // 每小时允许发送5条
    perDay: 10     // 每天允许发送10条
  },
  
  // 验证码有效期（秒）
  expireTime: 600  // 10分钟
}; 