const { getUserComparisonHistory, getUserComparisonStats } = require('../../services/user/userComparisonHistoryService');

/**
 * 获取用户的AI产品对比历史记录
 * GET /api/users/me/comparison-history
 */
const getComparisonHistory = async (req, res) => {
  try {
    const userId = req.user.id; // 从认证中间件获取用户ID
    const { page = 1, limit = 20 } = req.query;

    const result = await getUserComparisonHistory(userId, page, limit);

    if (!result.success) {
      return res.status(400).json({
        success: false,
        message: result.error
      });
    }

    res.json({
      success: true,
      message: '获取对比历史成功',
      data: result.data
    });

  } catch (error) {
    console.error('❌ 获取用户对比历史控制器错误:', error.message);
    res.status(500).json({
      success: false,
      message: '服务器内部错误'
    });
  }
};

module.exports = {
  getComparisonHistory
};