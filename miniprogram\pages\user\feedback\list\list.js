const { feedback } = require('../../../../utils/api');
const util = require('../../../../utils/util');

Page({
  /**
   * 页面的初始数据
   */
  data: {
    feedbackList: [],
    currentPage: 1,
    hasMore: true,
    loading: false,
    isEmpty: false
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function (options) {
    // 设置导航栏标题
    wx.setNavigationBarTitle({
      title: '我的反馈'
    });
    
    this.loadFeedbackList();
  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh: function () {
    this.refreshFeedbackList();
  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom: function () {
    if (this.data.hasMore && !this.data.loading) {
      this.loadMoreFeedback();
    }
  },

  /**
   * 加载反馈列表
   */
  loadFeedbackList: function() {
    if (this.data.loading) return;
    
    this.setData({ loading: true });
    
    feedback.getUserFeedbacks({
      page: this.data.currentPage,
      limit: 10
    })
      .then(res => {
        this.setData({ loading: false });
        
        if (res.success && res.data) {
          const feedbacks = res.data.feedbacks || [];
          const pagination = res.data.pagination || {};
          
          this.setData({
            feedbackList: [...this.data.feedbackList, ...feedbacks],
            hasMore: this.data.currentPage < (pagination.pages || 0),
            isEmpty: this.data.feedbackList.length === 0 && feedbacks.length === 0
          });
        } else {
          throw new Error(res.message || '获取反馈列表失败');
        }
      })
      .catch(err => {
        this.setData({ loading: false });
        console.error('获取反馈列表失败:', err);
        util.showToast(err.message || '获取反馈列表失败');
      });
  },

  /**
   * 刷新反馈列表
   */
  refreshFeedbackList: function() {
    this.setData({
      feedbackList: [],
      currentPage: 1,
      hasMore: true,
      isEmpty: false
    });
    
    this.loadFeedbackList();
    
    // 停止下拉刷新
    setTimeout(() => {
      wx.stopPullDownRefresh();
    }, 500);
  },

  /**
   * 加载更多反馈
   */
  loadMoreFeedback: function() {
    this.setData({
      currentPage: this.data.currentPage + 1
    });
    this.loadFeedbackList();
  },

  /**
   * 处理反馈项点击事件（来自组件）
   */
  goToFeedbackDetail: function(e) {
    const feedbackId = e.detail.id;
    wx.navigateTo({
      url: `/pages/user/feedback/detail/detail?id=${feedbackId}`
    });
  },

  /**
   * 跳转到提交反馈页面
   */
  goToSubmitFeedback: function() {
    wx.navigateTo({
      url: '/pages/user/feedback/feedback'
    });
  }
}); 