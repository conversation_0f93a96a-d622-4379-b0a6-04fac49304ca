const smsService = require('../services/smsService');
const { success, error } = require('../utils/response');

/**
 * 发送短信验证码
 * @route POST /api/v1/sms/send
 * @access Public
 */
exports.sendVerificationCode = async (req, res, next) => {
  try {
    const { phone, purpose } = req.body;
    const deviceInfo = req.body.deviceInfo || {};
    const ip = req.ip || req.connection.remoteAddress;
    
    // 验证手机号格式（中国大陆手机号）
    if (!phone || !/^1[3-9]\d{9}$/.test(phone)) {
      return error(res, 400, '请输入有效的手机号');
    }
    
    // 验证用途
    const validPurposes = ['register', 'login', 'auth', 'reset_password', 'change_phone', 'withdraw'];
    if (!purpose || !validPurposes.includes(purpose)) {
      return error(res, 400, '无效的验证码用途');
    }
    
    // 发送验证码
    const result = await smsService.sendVerificationCode(phone, purpose, deviceInfo, ip);
    
    // 返回结果
    return success(res, 200, result.message, {
      expiresIn: result.expiresIn,
      account: result.account
    });
  } catch (err) {
    // 捕获频率限制错误
    if (err.statusCode === 429) {
      return error(res, 429, err.message, { 
        remainingTime: err.remainingTime 
      });
    }
    
    next(err);
  }
};

/**
 * 验证短信验证码
 * @route POST /api/v1/sms/verify
 * @access Public
 */
exports.verifyCode = async (req, res, next) => {
  try {
    const { phone, verifyCode, purpose } = req.body;
    
    // 验证参数
    if (!phone || !verifyCode || !purpose) {
      return error(res, 400, '缺少必要参数');
    }
    
    // 验证验证码
    const result = await smsService.verifyCode(phone, verifyCode, purpose);
    
    if (result.success) {
      return success(res, 200, result.message);
    } else {
      return error(res, 400, result.message, {
        remainingAttempts: result.remainingAttempts
      });
    }
  } catch (err) {
    next(err);
  }
}; 