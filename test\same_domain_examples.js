/**
 * 同领域产品对比工具使用示例
 * 展示如何使用same_domain_product_comparison.js进行同类型产品的详细参数对比
 */

const { 
  compareProductsSameDomain,
  formatComparisonOutput,
  saveComparisonResult 
} = require('./same_domain_product_comparison.js');

/**
 * 示例1: 高端智能手机对比
 */
async function example1_flagship_phones() {
  console.log('\n📱 示例1: 高端智能手机参数对比');
  console.log('='.repeat(60));
  
  const phones = ['iPhone 15 Pro Max', 'Samsung Galaxy S24 Ultra', 'Google Pixel 8 Pro'];
  
  try {
    const result = await compareProductsSameDomain(phones);
    
    if (result.success) {
      console.log(`✅ 对比成功`);
      console.log(`📊 产品类别: ${result.productCategory}`);
      console.log(`🔢 分析参数: ${Object.keys(result.parameterAnalysis).length} 个`);
      
      // 显示产品规格概览
      console.log('\n📋 产品规格概览:');
      result.productSpecs.forEach((product, index) => {
        console.log(`  ${index + 1}. ${product.name}`);
        if (product.basicInfo) {
          console.log(`     品牌: ${product.basicInfo.brand}`);
          console.log(`     价格: ${product.basicInfo.officialPrice}`);
        }
      });
      
      // 显示部分参数分析
      console.log('\n🔍 参数分析示例:');
      const firstParam = Object.keys(result.parameterAnalysis)[0];
      if (firstParam) {
        const analysis = result.parameterAnalysis[firstParam];
        console.log(`  📊 ${firstParam}:`);
        console.log(`    说明: ${analysis.description?.substring(0, 100)}...`);
        console.log(`    排名: ${analysis.productRanking?.join(' > ') || '暂无排名'}`);
      }
      
      // 保存结果
      await saveComparisonResult(result, 'flagship_phones_comparison.json');
      
      return result;
    } else {
      console.log(`❌ 对比失败: ${result.error}`);
    }
  } catch (error) {
    console.error('示例1执行失败:', error.message);
  }
}

/**
 * 示例2: 游戏笔记本对比
 */
async function example2_gaming_laptops() {
  console.log('\n💻 示例2: 游戏笔记本参数对比');
  console.log('='.repeat(60));
  
  const laptops = ['ROG幻16', 'Legion 7i', 'Alienware x17'];
  
  try {
    const result = await compareProductsSameDomain(laptops);
    
    if (result.success) {
      console.log(`✅ 对比成功 - ${result.productCategory}`);
      
      // 显示综合推荐
      if (result.overallRecommendation) {
        console.log('\n💡 综合推荐:');
        console.log(result.overallRecommendation.substring(0, 200) + '...');
      }
      
      await saveComparisonResult(result, 'gaming_laptops_comparison.json');
      return result;
    }
  } catch (error) {
    console.error('示例2执行失败:', error.message);
  }
}

/**
 * 示例3: 无线耳机对比
 */
async function example3_wireless_earbuds() {
  console.log('\n🎧 示例3: 无线耳机参数对比');
  console.log('='.repeat(60));
  
  const earbuds = ['AirPods Pro 2', 'Sony WF-1000XM4', 'Bose QuietComfort Earbuds'];
  
  try {
    const result = await compareProductsSameDomain(earbuds);
    
    if (result.success) {
      console.log(`✅ 对比成功 - ${result.productCategory}`);
      
      // 显示关键参数分析
      console.log('\n🔍 关键参数分析:');
      Object.entries(result.parameterAnalysis).slice(0, 3).forEach(([param, analysis]) => {
        console.log(`  📊 ${param}:`);
        console.log(`    ${analysis.comparison?.substring(0, 150)}...`);
      });
      
      await saveComparisonResult(result, 'wireless_earbuds_comparison.json');
      return result;
    }
  } catch (error) {
    console.error('示例3执行失败:', error.message);
  }
}

/**
 * 示例4: 新能源汽车对比
 */
async function example4_electric_cars() {
  console.log('\n🚗 示例4: 新能源汽车参数对比');
  console.log('='.repeat(60));
  
  const cars = ['特斯拉Model Y', '比亚迪唐EV', '蔚来ES6'];
  
  try {
    const result = await compareProductsSameDomain(cars);
    
    if (result.success) {
      console.log(`✅ 对比成功 - ${result.productCategory}`);
      console.log(`📊 分析参数数量: ${Object.keys(result.parameterAnalysis).length}`);
      
      await saveComparisonResult(result, 'electric_cars_comparison.json');
      return result;
    }
  } catch (error) {
    console.error('示例4执行失败:', error.message);
  }
}

/**
 * 自定义产品对比
 * @param {Array<String>} productNames 产品名称列表
 * @param {String} customName 自定义保存文件名
 */
async function customProductComparison(productNames, customName = 'custom_comparison') {
  console.log(`\n🔧 自定义产品对比: ${productNames.join(' vs ')}`);
  console.log('='.repeat(60));
  
  try {
    const result = await compareProductsSameDomain(productNames);
    
    if (result.success) {
      console.log(`✅ 对比成功`);
      console.log(`📊 产品类别: ${result.productCategory}`);
      console.log(`📱 对比产品: ${result.requestedProducts.join(' | ')}`);
      console.log(`🔢 分析参数: ${Object.keys(result.parameterAnalysis).length} 个`);
      console.log(`⏰ 分析时间: ${new Date(result.analysisTimestamp).toLocaleString()}`);
      
      // 显示完整格式化输出
      console.log('\n📄 完整分析报告:');
      const formattedOutput = formatComparisonOutput(result);
      console.log(formattedOutput.substring(0, 1000) + '\n...(查看完整报告请打开生成的txt文件)');
      
      await saveComparisonResult(result, `${customName}.json`);
      
      return result;
    } else {
      console.log(`❌ 对比失败: ${result.error}`);
      return null;
    }
  } catch (error) {
    console.error('自定义对比执行失败:', error.message);
    return null;
  }
}

/**
 * 参数详情展示函数
 * @param {Object} result 对比结果
 */
function showParameterDetails(result) {
  if (!result || !result.success) {
    console.log('❌ 无有效的对比结果');
    return;
  }

  console.log('\n📊 详细参数展示');
  console.log('='.repeat(60));
  
  // 展示每个产品的详细参数
  result.productSpecs.forEach((product, index) => {
    console.log(`\n${index + 1}. ${product.name}`);
    console.log('-'.repeat(30));
    
    if (product.basicInfo) {
      console.log('基本信息:');
      Object.entries(product.basicInfo).forEach(([key, value]) => {
        console.log(`  ${key}: ${value}`);
      });
    }
    
    if (product.detailedSpecs) {
      console.log('详细规格:');
      Object.entries(product.detailedSpecs).forEach(([key, value]) => {
        if (typeof value === 'object') {
          console.log(`  ${key}:`);
          Object.entries(value).forEach(([subKey, subValue]) => {
            console.log(`    ${subKey}: ${subValue}`);
          });
        } else {
          console.log(`  ${key}: ${value}`);
        }
      });
    }
  });
  
  // 展示AI参数分析
  console.log('\n🧠 AI参数分析结果');
  console.log('='.repeat(60));
  
  Object.entries(result.parameterAnalysis).forEach(([param, analysis]) => {
    console.log(`\n🔍 ${param}:`);
    console.log(`  📝 说明: ${analysis.description}`);
    console.log(`  📊 对比: ${analysis.comparison}`);
    console.log(`  ⭐ 评价: ${analysis.performance}`);
    console.log(`  👤 影响: ${analysis.userImpact}`);
    if (analysis.productRanking) {
      console.log(`  🏆 排名: ${analysis.productRanking.join(' > ')}`);
    }
  });
}

/**
 * 快速测试函数
 */
async function quickTest() {
  console.log('🚀 快速测试 - 对比三款主流智能手机');
  
  const phones = ['iPhone 15', 'Samsung Galaxy S24', 'Xiaomi 14'];
  
  const result = await customProductComparison(phones, 'quick_test_smartphones');
  
  if (result) {
    console.log('\n📊 快速测试完成！');
    console.log('🔍 详细参数和AI分析已生成');
    
    // 显示参数详情
    showParameterDetails(result);
  }
}

/**
 * 主函数 - 运行所有示例
 */
async function runAllExamples() {
  console.log('📊 同领域产品参数对比工具 - 使用示例');
  console.log('='.repeat(80));
  console.log('⚠️  注意: 请确保已设置DEEPSEEK_API_KEY环境变量');
  console.log('📋 输出内容: 1.产品详细参数 2.每个参数的AI分析结果');
  console.log('='.repeat(80));
  
  const examples = [
    example1_flagship_phones,
    example2_gaming_laptops,
    example3_wireless_earbuds,
    example4_electric_cars
  ];
  
  for (let i = 0; i < examples.length; i++) {
    try {
      const result = await examples[i]();
      
      // 演示间隔
      if (i < examples.length - 1) {
        console.log('\n' + '-'.repeat(40));
        console.log('⏳ 等待3秒后继续下一个示例...');
        await new Promise(resolve => setTimeout(resolve, 3000));
      }
    } catch (error) {
      console.error(`示例${i + 1}执行失败:`, error.message);
    }
  }
  
  console.log('\n✅ 所有示例执行完成！');
  console.log('📁 检查test文件夹中的生成文件(.json和.txt)');
}

// 导出函数供其他模块使用
module.exports = {
  example1_flagship_phones,
  example2_gaming_laptops,
  example3_wireless_earbuds,
  example4_electric_cars,
  customProductComparison,
  showParameterDetails,
  runAllExamples,
  quickTest
};

// 如果直接运行此文件
if (require.main === module) {
  // 检查命令行参数
  const args = process.argv.slice(2);
  
  if (args.length > 0) {
    // 如果提供了产品名称，进行自定义对比
    console.log('🔧 执行自定义同领域产品对比...');
    customProductComparison(args, 'command_line_comparison')
      .then((result) => {
        if (result) {
          console.log('\n📊 显示详细参数和AI分析结果:');
          showParameterDetails(result);
        }
        process.exit(0);
      })
      .catch(error => {
        console.error('执行失败:', error.message);
        process.exit(1);
      });
  } else {
    // 运行快速测试
    console.log('🚀 运行快速测试...');
    console.log('💡 提示: 你也可以通过命令行参数指定同类型产品进行对比');
    console.log('   例如: node same_domain_examples.js "MacBook Pro M3" "ThinkPad X1" "Surface Laptop"');
    
    quickTest()
      .then(() => {
        console.log('\n💡 想要运行完整示例？请取消注释下面的代码行：');
        console.log('   // runAllExamples();');
      })
      .catch(error => {
        console.error('快速测试失败:', error.message);
        process.exit(1);
      });
    
    // 如果想运行所有示例，取消下面这行的注释
    // runAllExamples();
  }
}