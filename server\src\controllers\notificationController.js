const notificationService = require('../services/notificationService');
const { success, error } = require('../utils/response');

/**
 * 通知控制器
 * 处理通知相关的API请求
 */

/**
 * 获取当前用户的通知列表
 * @route GET /api/v1/notifications
 * @access 私有
 */
const getUserNotifications = async (req, res) => {
  try {
    const { page = 1, limit = 20 } = req.query;
    const userId = req.user.id;
    
    console.log('获取通知列表，参数:', { userId, page, limit });
    const result = await notificationService.getUserNotifications(
      userId, 
      parseInt(page), 
      parseInt(limit)
    );
    
    // console.log('获取通知列表成功', result);
    return success(res, 200, '获取通知列表成功', result);
  } catch (err) {
    return error(res, 500, err.message);
  }
};

/**
 * 标记通知为已读
 * @route PATCH /api/v1/notifications/:notificationId/mark-read
 * @access 私有
 */
const markAsRead = async (req, res) => {
  try {
    const { notificationId } = req.params;
    const userId = req.user.id;
    
    const notification = await notificationService.markAsRead(notificationId, userId);
    
    if (!notification) {
      return error(res, 404, '通知不存在或无权操作');
    }
    
    return success(res, 200, '通知已标为已读', notification);
  } catch (err) {
    return error(res, 500, err.message);
  }
};

/**
 * 标记所有通知为已读
 * @route PATCH /api/v1/notifications/mark-all-read
 * @access 私有
 */
const markAllAsRead = async (req, res) => {
  try {
    const userId = req.user.id;
    
    await notificationService.markAllAsRead(userId);
    
    return success(res, 200, '所有通知已标记为已读');
  } catch (err) {
    return error(res, 500, err.message);
  }
};

/**
 * 获取未读通知数量
 * @route GET /api/v1/notifications/unread-count
 * @access 私有
 */
const getUnreadCount = async (req, res) => {
  try {
    const userId = req.user.id;
    
    const count = await notificationService.getUnreadCount(userId);
    
    return success(res, 200, '获取未读通知数量成功', { count });
  } catch (err) {
    return error(res, 500, err.message);
  }
};

module.exports = {
  getUserNotifications,
  markAsRead,
  markAllAsRead,
  getUnreadCount
}; 